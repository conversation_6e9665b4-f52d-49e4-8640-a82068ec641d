{"": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.dia", "emit-module-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftdeps"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift": {"index-unit-output-path": "/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o"}}
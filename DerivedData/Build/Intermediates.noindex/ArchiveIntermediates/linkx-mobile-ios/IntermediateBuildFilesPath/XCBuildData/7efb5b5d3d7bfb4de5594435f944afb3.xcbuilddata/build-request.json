{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa5aa9e4669fc376686cdedbf7adea4907f"}], "containerPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "install", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.2", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "install", "ASSET_PACK_FOLDER_PATH": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/OnDemandResources", "ASSETCATALOG_COMPILER_FLATTENED_APP_ICON_PATH": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/ProductIcon.png", "COLOR_DIAGNOSTICS": "YES", "DEPLOYMENT_LOCATION": "YES", "DEPLOYMENT_POSTPROCESSING": "YES", "diagnostic_message_length": "170", "DSTROOT": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_SIGNATURE_AGGREGATION": "YES", "ENABLE_XOJIT_PREVIEWS": "YES", "INDEX_ENABLE_DATA_STORE": "NO", "MESSAGES_APPLICATION_EXTENSION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/MessagesApplicationExtensionSupport", "MESSAGES_APPLICATION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/MessagesApplicationSupport", "OBJROOT": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath", "ONLY_ACTIVE_ARCH": "YES", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/PrecompiledHeaders", "SIGNATURE_METADATA_FOLDER_PATH": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Signatures", "SWIFT_STDLIB_TOOL_UNSIGNED_DESTINATION_DIR": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/SwiftSupport", "SYMROOT": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath", "WATCHKIT_2_SUPPORT_FOLDER_PATH": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/WatchKitSupport2"}}}}, "schemeCommand": "archive", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}
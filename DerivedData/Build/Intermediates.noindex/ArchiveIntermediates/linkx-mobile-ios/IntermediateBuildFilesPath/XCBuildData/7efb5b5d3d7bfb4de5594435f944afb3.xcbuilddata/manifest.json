{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>": {"is-command-timestamp": true}, "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>": {"is-command-timestamp": true}, "<TRIGGER: SetOwnerAndGroup tuan:staff /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>": {"is-command-timestamp": true}, "<TRIGGER: Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/_CodeSignature", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<target-linkx-mobile-ios-****************************************************************--begin-scanning>", "<target-linkx-mobile-ios-****************************************************************--end>", "<target-linkx-mobile-ios-****************************************************************--linker-inputs-ready>", "<target-linkx-mobile-ios-****************************************************************--modules-ready>", "<workspace-Release-iphonesimulator18.2-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/_CodeSignature", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Assets.car", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/PkgInfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap"], "roots": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath"], "outputs": ["<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-iphonesimulator18.2-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Release-iphonesimulator18.2-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "signature": "e9a0edc116155c32c5676b0af9e57744"}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator"]}, "P0:::Gate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM-target-linkx-mobile-ios-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios", "<GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Metadata.appintents>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<SetMode /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<SetOwner /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/PkgInfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--start>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Assets.car", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--generated-headers>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--swift-generated-headers>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Metadata.appintents>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/ssu", "--bundle-id", "com.linkx-mobile-ios", "--product-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "--extracted-metadata-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Metadata.appintents", "--deployment-postprocessing", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "e99d981bc8c9ba2ef9d9fc64ea6bfab1"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift/", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<TRIGGER: Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/_CodeSignature", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<TRIGGER: CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:CompileAssetCatalog /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalog /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Assets.car"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "control-enabled": false, "deps": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies"], "deps-style": "dependency-info", "signature": "d28b66af0b3f142948c2761fa7c9a708"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "deps": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Metadata.appintents>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--begin-linking>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--begin-scanning>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--end": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--entry>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Metadata.appintents>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "<GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios>", "<GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/PkgInfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<SetMode /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<SetOwner /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app", "<Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/", "<target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions>", "<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>", "<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>", "<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>", "<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterProduct>", "<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>", "<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>", "<target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--generated-headers>", "<target-linkx-mobile-ios-****************************************************************--swift-generated-headers>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--end>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--entry>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--linker-inputs-ready>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--modules-ready>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Metadata.appintents>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "<GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--unsigned-product-ready>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Gate target-linkx-mobile-ios-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--will-sign>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "--bundle-identifier", "com.linkx-mobile-ios", "--generate-swift-asset-symbols", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "control-enabled": false, "signature": "0b2ac15dbc8bc7f9a48ce64a582c9e20"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios", "<GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "2e2ddd6050faa4507247346b41962111"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["<target-linkx-mobile-ios-****************************************************************--start>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<TRIGGER: MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/PkgInfo"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:ProcessProductPackaging  /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "d8589f9a85d8e4c3a6592e8aa69b84cb"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:SetMode u+w,go-w,a+rX /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "shell", "description": "SetMode u+w,go-w,a+rX /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["<SetOwner /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: SetOwnerAndGroup tuan:staff /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<SetMode /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "args": ["/bin/chmod", "-RH", "u+w,go-w,a+rX", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "4b0918aefdf30a88c84ece2a36496555"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:SetOwnerAndGroup tuan:staff /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "shell", "description": "SetOwnerAndGroup tuan:staff /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<SetOwner /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>", "<TRIGGER: SetOwnerAndGroup tuan:staff /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "args": ["/usr/sbin/chown", "-RH", "tuan:staff", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "945b2cad6ab7b361c4ee7d9070a08079"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios": {"tool": "shell", "description": "Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "inputs": ["<GenerateDSYMFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app.dSYM/Contents/Resources/DWARF/linkx-mobile-ios>", "<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios normal>"], "outputs": ["<Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>", "<TRIGGER: Strip /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip", "-D", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "298f8990da6a9c5daf25e1771d9dba32"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:SwiftDriver Compilation linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "<ClangStatCache /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:SymLink /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app ../../InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "symlink", "description": "SymLink /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app ../../InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["<target-linkx-mobile-ios-****************************************************************--start>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx-mobile-ios.app"], "contents": "../../InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "repair-via-ownership-analysis": true}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "shell", "description": "Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "5d1cb1e79e9d5dec79551db08760be6c"}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/Info.plist", "<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"], "outputs": ["<Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Release:ValidateDevelopmentAssets /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios normal": {"tool": "shell", "description": "Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios normal", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator", "<target-linkx-mobile-ios-****************************************************************--generated-headers>", "<target-linkx-mobile-ios-****************************************************************--swift-generated-headers>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-L/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator", "-F/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-F/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/BuildProductsPath/Release-iphonesimulator", "-filelist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-final_output", "-<PERSON><PERSON><PERSON>", "/Applications/linkx-mobile-ios.app/linkx-mobile-ios", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/InstallationBuildProductsLocation/Applications/linkx-mobile-ios.app/linkx-mobile-ios"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "deps": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat"], "deps-style": "dependency-info", "signature": "bcec2775b679273c5241409cb25c11b2"}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:SwiftDriver Compilation Requirements linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "<ClangStatCache /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/linkx-mobile-ios/IntermediateBuildFilesPath/linkx-mobile-ios.build/Release-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap"]}}}
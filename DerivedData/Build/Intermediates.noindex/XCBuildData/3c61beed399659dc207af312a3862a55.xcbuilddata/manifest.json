{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios": {"is-mutated": true}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/_CodeSignature", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>", "<target-linkx-mobile-ios-****************************************************************--begin-scanning>", "<target-linkx-mobile-ios-****************************************************************--end>", "<target-linkx-mobile-ios-****************************************************************--linker-inputs-ready>", "<target-linkx-mobile-ios-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/_CodeSignature", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Assets.car", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_signature", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/PkgInfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap"], "roots": ["/tmp/linkx-mobile-ios.dst", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products"], "outputs": ["<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "signature": "e9a0edc116155c32c5676b0af9e57744"}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Metadata.appintents>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/PkgInfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--start>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Assets.car", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_signature", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--generated-headers>"]}, "P0:::Gate target-linkx-mobile-ios-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--swift-generated-headers>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Metadata.appintents>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/ssu", "--bundle-id", "com.linkx-mobile-ios", "--product-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "--extracted-metadata-path", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "820e678fd67aebb91d5bacadc8c32915"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift/", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios normal>", "<TRIGGER: MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/_CodeSignature", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<TRIGGER: CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift/", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift/", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,1", "--filter-for-device-os-version", "18.3.1", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "control-enabled": false, "signature": "769614dd8ea0b834deb148b95088f0ed"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "control-enabled": false, "signature": "1cc076a5e12cb0ae8339c22b20ecffea"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "deps": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Metadata.appintents>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/linkx-mobile-ios.dst>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/linkx-mobile-ios.dst>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--begin-linking>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/linkx-mobile-ios.dst>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--begin-scanning>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--end": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--entry>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned/", "<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Metadata.appintents>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Assets.car", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_signature", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/PkgInfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap", "<target-linkx-mobile-ios-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-linkx-mobile-ios-****************************************************************--Barrier-ChangePermissions>", "<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>", "<target-linkx-mobile-ios-****************************************************************--Barrier-CopyAside>", "<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>", "<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterProduct>", "<target-linkx-mobile-ios-****************************************************************--Barrier-StripSymbols>", "<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>", "<target-linkx-mobile-ios-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--DocumentationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--GeneratedFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--HeadermapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--InfoPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ModuleMapTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ProductPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--RealityAssetsTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SanitizerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--StubBinaryTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestHostTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--TestTargetTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--VersionPlistTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-linkx-mobile-ios-****************************************************************--generated-headers>", "<target-linkx-mobile-ios-****************************************************************--swift-generated-headers>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--end>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/linkx-mobile-ios.dst>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--entry>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/linkx-mobile-ios.dst>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary Preview Injection Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--linker-inputs-ready>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--modules-ready>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/ssu/root.ssu.yaml", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned/", "<CopySwiftStdlib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Metadata.appintents>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Assets.car", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_signature", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "<target-linkx-mobile-ios-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--unsigned-product-ready>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Gate target-linkx-mobile-ios-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-linkx-mobile-ios-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-linkx-mobile-ios-****************************************************************--will-sign>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "--bundle-identifier", "com.linkx-mobile-ios", "--generate-swift-asset-symbols", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "control-enabled": false, "signature": "95169be80777cfd935b7c26e3cace46a"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets /Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Assets.xcassets/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned/", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_signature", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Assets.car"], "deps": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_dependencies"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/thinned>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_output/unthinned>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "inputs": ["<target-linkx-mobile-ios-****************************************************************--start>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "<MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>", "<TRIGGER: MkDir /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/assetcatalog_generated_info.plist", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/PkgInfo"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "<target-linkx-mobile-ios-****************************************************************--ProductStructureTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "788caa70c13aea8b74fd24af0063d8f7"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "<target-linkx-mobile-ios-****************************************************************--Barrier-CodeSign>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:SwiftDriver Compilation linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "<ClangStatCache /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-linkx-mobile-ios-****************************************************************--generated-headers>", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"tool": "shell", "description": "Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "<target-linkx-mobile-ios-****************************************************************--Barrier-Validate>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "2df1554099f4d58a72a9cb4594d14f5d"}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/Info.plist", "<target-linkx-mobile-ios-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-linkx-mobile-ios-****************************************************************--will-sign>", "<target-linkx-mobile-ios-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"], "outputs": ["<Validate /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app>"]}, "P0:target-linkx-mobile-ios-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Preview Content", "<target-linkx-mobile-ios-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios-ce159b2766cd6a078d41b2f1364d5fa5-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule/", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx_mobile_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib normal", "inputs": ["<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/linkx-mobile-ios.debug.dylib", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "0a858ee9a30796f348dcf46bf9e2e73f"}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios normal": {"tool": "shell", "description": "Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios normal", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios", "<Linked Binary /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios>", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "signature": "6788b701930ceabc384798008a0a876a"}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib normal", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Date+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_iosApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.app-Simulated.xcent.der", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "<target-linkx-mobile-ios-****************************************************************--generated-headers>", "<target-linkx-mobile-ios-****************************************************************--swift-generated-headers>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "-install_name", "@rpath/linkx-mobile-ios.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_lto.o", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-fobjc-link-runtime", "-fprofile-instr-generate", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat", "-o", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Products/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "deps": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_dependency_info.dat"], "deps-style": "dependency-info", "signature": "816ffcea12b24915b11bfa68d24d20ce"}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:SwiftDriver Compilation Requirements linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements linkx-mobile-ios normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/Date+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Extensions/View+Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/NotificationService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Constants.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/EmptyStateView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/ContentView.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/linkx_mobile_iosApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "<ClangStatCache /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-linkx-mobile-ios-****************************************************************--copy-headers-completion>", "<target-linkx-mobile-ios-****************************************************************--ModuleVerifierTaskProducer>", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios Swift Compilation Requirements Finished", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftmodule", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftsourceinfo", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.abi.json", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios.swiftdoc"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "inputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx_mobile_ios-Swift.h", "<target-linkx-mobile-ios-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/linkx_mobile_ios-Swift.h"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-OutputFileMap.json"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.LinkFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftConstValuesFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios.SwiftFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios_const_extract_protocols.json"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/empty-linkx-mobile-ios.plist"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-DebugDylibPath-normal-arm64.txt"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-non-framework-target-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-all-target-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-generated-files.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-own-target-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios-project-headers.hmap"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.DependencyMetadataFileList"]}, "P2:target-linkx-mobile-ios-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap", "inputs": ["<target-linkx-mobile-ios-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/linkx-mobile-ios.hmap"]}}}
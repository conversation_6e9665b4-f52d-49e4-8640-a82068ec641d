{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5681dcc22f3f67979a2c19e54f017ba49", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5f250f2a4ac3809a14c7142de5c72d6f4", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55d8b4e1f2cc9ccfb22ed28559bc78196", "path": "Date+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b5f462a8a497cc4acc47d7bc626ca44f", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c815b99adcaa72831ce366cb431819aa", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51b3c1c0a41bd48b94c716f54892bbc84", "path": "APIClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5eea0d9a14c39d21666fdbf4f53efbaf1", "path": "APIEndpoints.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa59119607b2d58d001cc857f00d571c95a", "name": "Network", "path": "Network", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51302bfbc1ce547fb5d11147caff1152d", "path": "AnalyticsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5af44d7695639d5b0b63edbd8601010b7", "path": "ConfigurationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b5d6637b37855eb2ea9af280fa8bec8a", "path": "NotificationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a58be73561c8b24b12650401c95e59df", "path": "SyncService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa528dd16c32f630a5eef2bcd30c51bdd54", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa573402145a4572670338e7b82e17bd999", "path": "KeychainManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa59e1c147d69e6ea2c263971533be86487", "name": "Storage", "path": "Storage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d3b90a98ea9ab6c2d3ef9ef0666da744", "path": "AppConstants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa564f2e30c1709ae31ad68ea71f33d9022", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51b3e485ba7daa38835a5de77baea2554", "path": "Error<PERSON><PERSON>ling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5451b7ff3aab5bce9f0b4b8cc83b21269", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a54d4d55b79b3396fcd2b1a5b1b88731", "path": "Logger.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5a705d790251d63e88a98c776c2348187", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5f4c83658cd822e309cb1e4507775b5c4", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53f4c1d4436fbc99fe80720d88516c34a", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa599b99a5c7d5ac1922ae8e62ffeaa96e3", "path": "Re<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53a94c86d98b14c0bdf688ad8bda82a2c", "path": "Transaction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ae626a2a761e85187f3d48ea11a773be", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa50dd215e2d3f24429e94ab88b1173df35", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ff5f958e42625067c95cf4ce3324dd24", "path": "AuthRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5814588a6abc95e6b65ef6aa93a72f60f", "path": "RewardRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa568a7261b597be63e83a335aae550420b", "path": "TransactionRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51ffd6918a7f3543af0f20080a8826f47", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa50b451b6e92e4fec8a657d7a087e8f043", "name": "Domain", "path": "Domain", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d138a8b90eff946174185796f5e8c6ae", "path": "AuthViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55e701d67f442c0d1dc34fc97ab664c10", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5065f20d99a7a7c9999756f92b13030db", "path": "ForgotPasswordView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5141388bbf1bd4fcd131a109ead0a58a8", "path": "LoginView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa559f3c6ebca0ec598a7722b6edc129a37", "path": "RegisterView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d81dee292d6aa6f77e16466df4f78db2", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa522de135f8e0a9c5cf27202b2b73c1860", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa585065614de615e108c5ae5525b29248a", "path": "EmptyStateView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f517b3de258661c71c7ed1b36217aacd", "path": "QRScannerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5cd02e6955084f4e88a1b529510c73170", "path": "RewardCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58f3e0df8258299ce88d74b2f96649913", "path": "TransactionRowView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5913266a98f951986e292f37e178eb6ad", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5ba9b7f042053ac8ea6c29bd46d2d3eb4", "name": "Common", "path": "Common", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56545a39065698bec8df1beebf06d179e", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5e1301343ce2722100ceaec08cc29c21a", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51759d3c3b2c874cfefe48f8e06e5909e", "name": "Home", "path": "Home", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51888c2a551343f97659fbb84d588d9ad", "path": "EditProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5485e5b8f154bcd9915f1b1a35267764b", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa552947ded7173bd951a45cc5da91e6a70", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54e8584c86ec12022cbda8f93b86d8073", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b970d50d39713a14688a53dc077e1067", "path": "SupportView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5445d4a5e1b504c96ce94a4704c9b0d80", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa520ca296402145c99f6e05c26c28dd679", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5aac219296ccd1827affcccfefc7fdf5a", "path": "MyRedemptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50b4ee986ef03181e862a426f12ce4f19", "path": "RewardSearchView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa577378b04e2f40189250b8d2bc10c293e", "path": "RewardsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5289a4a73d7ab24514f231cf43fe110a3", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5316eb4378f70b7b08ae4e96bbb79ef03", "name": "Rewards", "path": "Rewards", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56903d45cbabceeca1d6e28458c17f00c", "path": "TransactionDetailView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5f1355ac91162f88f8aa6bdbf8c14ed95", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa585317219a85dc4b5b45994dccada3999", "name": "Transactions", "path": "Transactions", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d82f1bbb9ed6a80752c3b2028dd41ac4", "path": "ReceiveTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59eb4492519501d0de1fce5d3758f74ff", "path": "SendTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54677c53cd444712192fc620beedda108", "path": "WalletView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa56631bb6f221e4dbfb1b6c8749e6fb498", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5cbab1b9c05588504b21a2c062498ec8b", "name": "Wallet", "path": "Wallet", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5461c4befa61e96a18576a1c630240247", "name": "Presentation", "path": "Presentation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa52e64d8b7622f9cac20a3b07d77cb9fe2", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53d482cf196481fb04f713ad9a38c3605", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa5de31ddc2cef58776985dacb0dbd5f87f", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5550b1595c39a944dfe0b79c06889b72b", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c3e694734e85c2c8cb3cb28509e6bb32", "path": "linkx_mobile_iosApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5fe89b10bf0404f92e4675e05ba69a732", "path": "LinkXApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51a5509bb0a7553857db590a1538a840f", "name": "linkx-mobile-ios", "path": "linkx-mobile-ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa51b27b65aca3ffad73bad0b400490815b", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f7241cfe17296e9a16c46cb64330649e", "path": "linkx_mobile_iosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53cead36256eeb6742addbe83b4b5fac8", "name": "linkx-mobile-iosTests", "path": "linkx-mobile-iosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa571af807abccab6247f0339994a68ca01", "path": "linkx_mobile_iosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5cbe4999ba192fbd0c54d1a1fa28262e9", "path": "linkx_mobile_iosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53704883f05c5846c46e46fd70ecaa4f3", "name": "linkx-mobile-iosUITests", "path": "linkx-mobile-iosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "ce159b2766cd6a078d41b2f1364d5fa57505e7038dda8ae52337a220eba11a24", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5946ae46e4993d6f103808e295df5bb26", "name": "linkx-mobile-ios", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5", "path": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "projectDirectory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "targets": ["TARGET@v11_hash=638a1981ecb05bcf9ad8f51a520e77a1", "TARGET@v11_hash=e59f82a8e8fe699bea8e8d23ef8fb7f4", "TARGET@v11_hash=261bff59d6989040ecf16930fc7d0299"]}
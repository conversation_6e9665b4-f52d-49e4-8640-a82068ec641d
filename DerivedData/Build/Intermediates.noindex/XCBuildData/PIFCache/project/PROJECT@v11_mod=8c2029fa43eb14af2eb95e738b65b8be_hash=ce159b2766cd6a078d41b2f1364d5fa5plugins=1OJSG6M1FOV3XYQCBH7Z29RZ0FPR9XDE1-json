{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5681dcc22f3f67979a2c19e54f017ba49", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5f250f2a4ac3809a14c7142de5c72d6f4", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5aa424206377fe9b9a259160c74dac74d", "path": "Date+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5eadb4e339049d4224a9e711967b918a5", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5491dd8affac05259fd7e69791e86582b", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55d4dd1f862962bf51d1e9ef1dc174fbe", "path": "APIClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51acb69f9d0959e66d9c44fef84c9799f", "path": "APIEndpoints.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa595ab54fd18c8c9a39de70fe198e3b15c", "name": "Network", "path": "Network", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a6bba8041105e1b5b991f0de043715b6", "path": "AnalyticsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56c076b4b073ef61d0fa3b652926ea9a6", "path": "ConfigurationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa57ef1facdfb5bbc6f2a0c49534a7ecab0", "path": "NotificationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b0acb61a69b314d77771c470601e5ade", "path": "SyncService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d4837b34f7671536ac2ed713ab2c508e", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa564afac8dec67ca41dcf5a9ca9737bf76", "path": "KeychainManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5325ea3b76da4422cd13d86f3a80b8222", "name": "Storage", "path": "Storage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5099f871e9a2fbd20a650c562e1d9f54f", "path": "AppConstants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ee28c8b256521641ab1b8cb13e498ffa", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e61776a6cb2018fc4d71b6ea360628bf", "path": "Error<PERSON><PERSON>ling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa52aa402a52728087c55c0384380e68a3a", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d362c1a4bd1ad0c5cf01d7ae2101e7fb", "path": "Logger.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa532c64d9834826ae9fcd9f31448afffe5", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5733e10148ca6b12eb4da221a129419b3", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d0dee65f3061ef6a746b9088c56ba522", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa52a665d8ce79ab0949fafc44fb4486dca", "path": "Re<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50ca4ea42a406c569676eb2d552eb0e35", "path": "Transaction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b2d529d9887f0e3313765e329bd1d9f2", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d5e0d9d7040c4617c0ba66c31dd7d410", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5bd7b23a2aa61dfc9cb99588268eb512c", "path": "AuthRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ea92b161dd94eb781b2cea2219f12272", "path": "RewardRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d9aa136a28518972459a6a727071e60a", "path": "TransactionRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53d8a5dfc1f78c68f298404d0ca0f1a90", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d04d3c6ba910ca7343298bde45be5dec", "name": "Domain", "path": "Domain", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa556ea51f96060e649ab744b99d7c4e210", "path": "AuthViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5f43a0830b6b0e1d8312e8deb91c2c531", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5fb4efcf897e1c53c1acafcec42722b13", "path": "ForgotPasswordView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c0c6640c265358039b8afdc3c091c458", "path": "LoginView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e56ec26aad58c2fba158fb6fd4619344", "path": "RegisterView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b24dab753a2ee7b62e68395134a4e479", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5fbf27cf23fc05185fd8dc7b0455a3da7", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e7691db0400b25a34990ca902d583a45", "path": "EmptyStateView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5855ece5448dbd1b09a38d7c0c17006da", "path": "QRScannerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51ad32c6d8b91de090fae46357ebf952f", "path": "RewardCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa539cfa246071a7a80d3ee32b73186794c", "path": "TransactionRowView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa59d46d5abb75cc9753411592a425a524b", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55919d009deedbd8a953e40864b77f1f3", "name": "Common", "path": "Common", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa57b5e4c242ebbab2672b2c6cf6211d808", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa50e01c8d5c771559dbd7d51bac7dfae69", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa554581affc6e05b627f95064d4ca42b81", "name": "Home", "path": "Home", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5696f53167d2ce241db2721d36970bb0a", "path": "EditProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa52dbbb6f13183b62aa892b9a4b4ad4886", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56e4eb9e1fe85e05b14faa2349a1b9bc3", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5bc201dd48eb821e35949a53548df7da6", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa521f8a82f16d5548f3bf515910cd01275", "path": "SupportView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa532198617918e252919c26b036f128f78", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55b55aebeb215cc74ee1f294613644ed8", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5fc7132fbc71ca84da584097411cb2cf6", "path": "MyRedemptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54bd00effea2ed77c1578eaba5a499b55", "path": "RewardSearchView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59bd411a2cad0004d4c0dd1bfef8a4576", "path": "RewardsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c3bc13af4e625ad34c56f977824833de", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa508da22655e5da7a34bb2959e30457a74", "name": "Rewards", "path": "Rewards", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53e76de2b9b9cb153feb917b892a2c699", "path": "TransactionDetailView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa50c30523d6ef434cc1bb7fac7b7e35db1", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa57953209edf04ff1c09f2b5ae700ebf36", "name": "Transactions", "path": "Transactions", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa510f54d67d0606d2f695f8cabb21545eb", "path": "ReceiveTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5564a549922fb7e12cb1f707c5da54ba9", "path": "SendTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5519884cee12b4106502b6b9e1a1ec9fe", "path": "WalletView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa564db69d17bd20d97e98efdb3a9fb314b", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa582ed7f35e1fb397c173557e41b475afa", "name": "Wallet", "path": "Wallet", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51fa3ff510761a8788154c1b978b2c25c", "name": "Presentation", "path": "Presentation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa521666bc50a56879b8e5e17043739220e", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5a79830ac09d6c9da2a0d84b97ae903b4", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa59cfbb80d880cb457ec3337b9b2b2a363", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55a6e827a162c876cdba2f0d90369d52a", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51886fc6b140a894b5d633c06d4b5b2b9", "path": "linkx_mobile_iosApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa52d8483ce905eb1766e03ddecf0a88a17", "path": "LinkXApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51a5509bb0a7553857db590a1538a840f", "name": "linkx-mobile-ios", "path": "linkx-mobile-ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa5c48362256f99b05cdb7c0ea6d04e41dd", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a418f257e02eaaa3fee3d65b18d0f309", "path": "linkx_mobile_iosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53cead36256eeb6742addbe83b4b5fac8", "name": "linkx-mobile-iosTests", "path": "linkx-mobile-iosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5cba3e4b9cc5b95fd63d2ae490d546c11", "path": "linkx_mobile_iosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5271b382f47f8ce8ea101d6f157b0cb05", "path": "linkx_mobile_iosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53704883f05c5846c46e46fd70ecaa4f3", "name": "linkx-mobile-iosUITests", "path": "linkx-mobile-iosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "ce159b2766cd6a078d41b2f1364d5fa57505e7038dda8ae52337a220eba11a24", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5946ae46e4993d6f103808e295df5bb26", "name": "linkx-mobile-ios", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5", "path": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "projectDirectory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "targets": ["TARGET@v11_hash=96c504ec4339f208d1429a737fddcaf9", "TARGET@v11_hash=05c15c829249e61bd5efc9c6bcc0fbac", "TARGET@v11_hash=5e95646da1c90a5493bd87d49c28d91e"]}
{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5681dcc22f3f67979a2c19e54f017ba49", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5f250f2a4ac3809a14c7142de5c72d6f4", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa594f0d1bc6e35ceacacd0d5e38d8991cf", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa560128761ff2cbf54ba80e7785996cde9", "path": "APIClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa587681ea14ccff4683a7ed974b91f3e6e", "path": "APIEndpoints.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5daa797789f67071115dae1bcc062b0c1", "name": "Network", "path": "Network", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c5cb38e81a34f01738989c9afa7ec25e", "path": "AnalyticsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa528fb790beb8245f45e41add04d0012ed", "path": "ConfigurationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56889a250adcc7d562f309b8ebd053e9c", "path": "NotificationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa573f05b851b52e389cbf175245857f953", "path": "SyncService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5e98967bf33a2eb332f3a04ad185878c7", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a2a88358dbe6c641eef46c8b9d41d3d7", "path": "KeychainManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa570d3020123941495225e8b8e6c4d2da0", "name": "Storage", "path": "Storage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ae2ede53ca1d3b355861d6b877319082", "path": "AppConstants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5406f1c9c63b38a842f9a96929f2accc0", "path": "Error<PERSON><PERSON>ling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e452c980f612c38c4d9d812cc0472c27", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a279625c8a20330c27e3e74848434431", "path": "Logger.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5caed6791fc8bf4a2fa9aaaf93d4535b0", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa510428a29a0bb528762b2f683cf0edf7f", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f2380357af5437bdd95bae6cdf95ba08", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa52798d2c00061cd4e067dece8c027dc05", "path": "Re<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5df1b1c68b9596a57e0be89dbed214380", "path": "Transaction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59795e19651674c1e267d0fdab1a8f22f", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5a335d2645bded5d70fe25e3eba6305d7", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5682bbe69e2bb282bc8d4c4d35da6440f", "path": "AuthRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58956af0f80de8605312079114494f701", "path": "RewardRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa544dcc711ba432976bfbad0f2f2bf73cf", "path": "TransactionRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa59b355f27814f5c5904b2db149e117841", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55bddd6f02218baadbd3f599a9a25692d", "name": "Domain", "path": "Domain", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e912417a8342c4176856f01fe3a96f24", "path": "AuthViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c00b541973be0e0ffb10535df73e7ec9", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa598ed22819ed78147d80e7703c367ecac", "path": "ForgotPasswordView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa546bb797777047ad150f791815b999583", "path": "LoginView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5026932b1acbe87f1f10f479777dc8224", "path": "RegisterView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5db62066b94b3345e9376fd4c7912c512", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa519af3828e46eb88df05981d3fb3f86c9", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ce699fbb253dce80c11ba75809b28cf1", "path": "EmptyStateView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51bcd3df7da68f760e2d261f3a887e684", "path": "QRScannerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5570727e732126b462a01e8e91ce8edf2", "path": "RewardCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ff629a57da7e66ef8c1679973c4b7195", "path": "TransactionRowView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa550dd0888ba4c451fd77a6610b49cfa5c", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa508943b5e970bd109cbdecef8db7cb450", "name": "Common", "path": "Common", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54e9231efafa9cb2d59617ccf162810c7", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5dee1b64b188c11970010cde2d7338aab", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa505d41534cbff5520b7fc05211375c89d", "name": "Home", "path": "Home", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa542de0abc9626990e5b3fffd77b880154", "path": "EditProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa512883b5b3e562c71f4a3ba2805c11c32", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f3a5f8461c414c821057a028e0a1798d", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51e02d470a5d0a41d5b5e8de2d5a05cf6", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56079175c69f116c548b45f7e5c57a990", "path": "SupportView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa557cc7afecd58d85bb9b2cb6fd169b4c4", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55912d9a8f856511697e8a123c711bfe8", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5853e835631ac69b08e4436f4ca11d5e8", "path": "MyRedemptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5434a123292cd39572c70a943eeeabe47", "path": "RewardSearchView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b2a4d4996f0b6098467aff267509a7ad", "path": "RewardsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa59cd92420f7a5c657644f12504cf558bf", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5dcb9cb38fef0339d881607b3ac4ec316", "name": "Rewards", "path": "Rewards", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b55c29ad93b7eb5e8c53d0b0df0b2f9e", "path": "TransactionDetailView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c68808400a16418d60501b6fc8c15f5f", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa52455fbe0896bb1e66b2e07a1ea8223f1", "name": "Transactions", "path": "Transactions", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54b2d79f32a5f2c17303496baf3540b73", "path": "ReceiveTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59753e1fd3d116481e5092a496a3c36fd", "path": "SendTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a7b6611b795e9e6c04f07aaf0161c15c", "path": "WalletView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b4384d343103b10d92135bd5a253c498", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b0251a7f3daa616274754823cafd8cad", "name": "Wallet", "path": "Wallet", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa58af8afa01f251e371273ba0edd92eadf", "name": "Presentation", "path": "Presentation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa54aecabe2c1f0f2ae9ecb443e1b80b087", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53fd43de9905147f93e9503346d1d07e0", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa51f4ecdb27b3643d9766c00f81b77fc48", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a0438c43bbe70f99b4952fe357a3c781", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa549b26d94558c4ef5324df9ae42d28e98", "path": "linkx_mobile_iosApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa591a3f566b26a5206770a83ae0d41f44e", "path": "LinkXApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51a5509bb0a7553857db590a1538a840f", "name": "linkx-mobile-ios", "path": "linkx-mobile-ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa5d18952507f13b049126dad70bc41036f", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53416fd54f24899400d6a20224fc35ac4", "path": "linkx_mobile_iosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53cead36256eeb6742addbe83b4b5fac8", "name": "linkx-mobile-iosTests", "path": "linkx-mobile-iosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa547cd5ba1813c96bfb75f9a2466773402", "path": "linkx_mobile_iosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59c36540271aceff312e5aaba86817f8b", "path": "linkx_mobile_iosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53704883f05c5846c46e46fd70ecaa4f3", "name": "linkx-mobile-iosUITests", "path": "linkx-mobile-iosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "ce159b2766cd6a078d41b2f1364d5fa57505e7038dda8ae52337a220eba11a24", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5946ae46e4993d6f103808e295df5bb26", "name": "linkx-mobile-ios", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5", "path": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "projectDirectory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "targets": ["TARGET@v11_hash=037dce6d91f9dece8f442d3285b244d2", "TARGET@v11_hash=bfa3df15c738f4675e6b64932cc443ea", "TARGET@v11_hash=17dcf346ebd9a94477a932d41652c19b"]}
{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5681dcc22f3f67979a2c19e54f017ba49", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5f250f2a4ac3809a14c7142de5c72d6f4", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa5670fa4131671ebaa49e7b7c399495466", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa57a21ece69e5a5be25f4ea918198f1fb9", "path": "APIClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e3a84adce66386861a8552db901b90c5", "path": "APIEndpoints.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa50e99e034b0dadefdbd58879aff4193b9", "name": "Network", "path": "Network", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa503785fde37b5ad533a1ab8966338a464", "path": "AnalyticsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5321810945786eab11257171f50663606", "path": "ConfigurationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa578c22e988016cd3d2d8692c71b50f929", "path": "OCRService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59d79ce5f6bcbe22e070f9638fb05f875", "path": "SyncService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa503467f1400cadd7593a72744f9b3e492", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b0c5b3d08dd5d0588b21bed7dd28cef1", "path": "DataCleanupManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e7ec18f5505ca5d741cce87182f32f90", "path": "KeychainManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d0b49b98497f967542bdb2b719c34c9d", "name": "Storage", "path": "Storage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53bc31b97b0876522107805b50c0c672f", "path": "AppConstants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c279a7648c8cbbaf2660a65724c79050", "path": "Error<PERSON><PERSON>ling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e958d8c319fbaf1d309092469f8dd0da", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa518d417477abdb2bd923bf8f5ffd8d4bf", "path": "Logger.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d027d19dd9f07694915c69c3716aff21", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55d58d05443e402a52d1a32096a9616fb", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa513881b006aa12be94ba572a4c2e3334b", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51ffea1bdbdc950f7c7675b2d82ac86ef", "path": "Notification.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ff98086a2ea749df82e0b531b3ba1a90", "path": "Re<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56a21a9a19a5f8a1cca850bcaffef0072", "path": "Transaction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5bc1d6271537f6ea4a683af2f0dd3febb", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51e5b5f0eb1a3663e37b3ebba56fe3a96", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d07fcf461c27efb55a81997bbec780ed", "path": "AuthRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa514a4b26dd9d3ee18122d482c7846a339", "path": "RewardRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e5b85921ea703917797c309917f5d3ab", "path": "TransactionRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5a81156accfee694de9f346090af72809", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c324daa642b19f81c19e7f1ca9578828", "name": "Domain", "path": "Domain", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa542114713b922d87c1189f1c8c1ddebfd", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"guid": "ce159b2766cd6a078d41b2f1364d5fa58cbebb01b2733494e498f9f6a199eb06", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa58a6e6d316ae3bdacd2d7ad7069c4e9c1", "name": "Merchants", "path": "Merchants", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa55af85b68271c4a366d7092676247c305", "name": "Presentation", "path": "Presentation", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5771666be35a9b4b8d3fca7738a0d30a6", "name": "linkx-mobile-ios", "path": "linkx-mobile-ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58beb6b216e4278f347d636f4bfe21f51", "path": "AdminComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa57d623df7e90b9e9c096b2f35c578b145", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59220dd0f38f04660a6cfc581fe0ebbe5", "path": "AdminViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5209ac08de0940270e18ed8072e015f7f", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f310d9bba054b409a3f1276be6a0078b", "path": "AdminDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a480f6091b9b178f28acdcd855150fa9", "path": "AdminPlaceholderViews.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa572ebb9b7d1ec3994a157cd10464fec09", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa590c369ebb50f6244e3e829de438cdb24", "name": "Admin", "path": "Admin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56422d20318714ca33596cc6462851340", "path": "AuthViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5ec28e33425b226ef7fe565d55886db01", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a6ec399a09a70c7b8bfb0705d9732b96", "path": "ForgotPasswordView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50f07045679a22bec8dbb2113eeaa3945", "path": "LoginView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f88d6296ba7d427ec47ce868c3e665ec", "path": "RegisterView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa571338955ae488ddf9be8b46b6304c19c", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b8ab778eb0cd28940bded52fa9ffc56c", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa529e1ccfa3275d057b2625001572ff979", "path": "BadgeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54ab5ff44b68854af7d322623609af4ab", "path": "ManualReceiptEntryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5edce665886ea2e05fe1775ef301c48e1", "path": "ModernTabBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51d19272bd5e43a2adfaf002e608fac27", "path": "QRScannerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a35ba824ea16236929a1be0a63304a1a", "path": "ReceiptScannerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5250c1ef69a12edda9395096a70db7d29", "path": "RewardCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa558c290c063d5332ff6a64b73f6c4e08c", "path": "TransactionRowView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d251ff474a3d375d2ec9deac0e78e38a", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f1bcc6855ed9ea85671db7353e4e2585", "path": "EmptyStateView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58a1c9b6959fdb4247014c9223a867401", "path": "ModernEmptyStateView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e650da2c874d4b0bd2215e811cb001b3", "path": "RoleBasedTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa547659b6fd273e40f3c0143e317008dc4", "path": "SkeletonLoadingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5cf6c90417e4fee8eebf23c2633026055", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5478b347202b394635dd20b6ae8a0bc53", "name": "Common", "path": "Common", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a3889da4de6f86da2354661bd395fb62", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa56c288adcc61d8b2ae80ada2ee3a3e252", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b8b8efd97a3bb4136828c60a8d6de526", "name": "Home", "path": "Home", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa526bfb37995008e0877253cd036282574", "path": "MerchantComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5ab3c398949ec031dd622f7da42750a52", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa551bd2338adeb68ffec2f1e2e7bcf6d1d", "path": "MerchantViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c08f57e38d926bd0a6644f249609e2e0", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5987cd6ce725a21091be78334f0571fc8", "path": "MerchantDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5290d711109f3ae833afff00bb44fec51", "path": "MerchantPlaceholderViews.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa510a65003a6cfc386865aa53063e8fd44", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa550e3306bbe829afae419e7168a8b0a7f", "name": "Merchant", "path": "Merchant", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5bb7ff6d09dc583b2e6e3344a416085eb", "path": "MerchantsViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa552c8d48cd20c0b397e6c72c9273945ca", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ca3b9a78dc011e3a5e8692fb85ae45ed", "path": "MerchantDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58a89ad40e3d9f20e32f85dea6214cb14", "path": "MerchantSearchView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa57c4189ba25474e30ceaa61f64dbe9b44", "path": "MerchantsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5c84c77357378573b37632c9306e321d0", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa525e690f08ab9eb1e91db69a85129432e", "name": "Merchants", "path": "Merchants", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55607aac9d992756d0f2f94e32f9aa377", "path": "NotificationsViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa56e514affb1b0750b691c4af43477ace5", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55db617a9348b8bc2fb2bc847ce2a5ba0", "path": "NotificationRowView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53ec808a58d8538a2b5e6a6d9f7741c37", "path": "NotificationSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5fcf3b26ce4356a4cd62b778a53d25204", "path": "NotificationsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa586cf960cffc5f1b6f15c084f36076b48", "path": "NotificationsViewPreview.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53f841a30184181e65b404c576119b2f2", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa54db84bc215b2f90f903a74a0ade9c995", "name": "Notifications", "path": "Notifications", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c3b6e63e37e8e04d8baeaf4bcb1dfdcb", "path": "EditProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53d1ae83c0f3f8c4e21fea80ff10bf75d", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5dd71bec870dfe92faadb6d74b9c34227", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5e2071c2c642e2e89bda88bdc3f884be0", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ce928a688bc162a5d11895ca0cb45d50", "path": "SupportView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa52089878326ab255e378f3dbcdb2186d7", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b566d0d6905f58b5179a65d6ee83d9e1", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c718bfa96da35ca24caa3ba171fdf3cd", "path": "MyRedemptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5830896df086a3bb7f324fc5ad7de1671", "path": "RewardSearchView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ab8df73f619e81a501a9436e7d8f2821", "path": "RewardsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa57bef4dc3b5ee30105d25a4c13a0aeeda", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa50ff7ffae98f301f731a4f6166bdf9208", "name": "Rewards", "path": "Rewards", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5df18d7bd380147ae7f5a0569734f281d", "path": "TransactionDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56786cb5ff86f3db57c19423fbcd5dd91", "path": "TransactionHistoryView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5245b1110ff10a106e3e6f6411e8bca61", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa56f335e72c74cacf28fdf51eab6f9a196", "name": "Transactions", "path": "Transactions", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58145fb740c482c47a5e42049e8a2ee68", "path": "ReceiveTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50335ac77edc85d713893043af87031b7", "path": "SendTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5380c68ac3f7156c5657e40c5ed729735", "path": "WalletView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa59bc524899f637229fe49f0790c5e8e3a", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa544e0d5929524ddb2ad0e827bf48382a4", "name": "Wallet", "path": "Wallet", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53c845fa1d1b3534003a8cf3d5282ec0d", "name": "Presentation", "path": "Presentation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f787241078b07cc55e063ea85d6226f7", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5cd8239c4aa02c085c1d2ac63a69e79d4", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5594672118fc861ba3fb4bc33a6e4023f", "path": "NotificationService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa57b1283c7bb252b5afa26af728d11ace5", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa50da9f9abc4fac39bd68714d82c09f3ed", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "ce159b2766cd6a078d41b2f1364d5fa5587376d347e0d7c64cfe76aec5a7e7ab", "path": "linkx-mobile-ios.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5ac7854aa8d2fd0955e890737b10feae7", "path": "LinkXApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51a5509bb0a7553857db590a1538a840f", "name": "linkx-mobile-ios", "path": "linkx-mobile-ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa513573115f350bd553d39713a990318a2", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa528783d7e462172d0920ba7ac58b94379", "path": "linkx_mobile_iosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53cead36256eeb6742addbe83b4b5fac8", "name": "linkx-mobile-iosTests", "path": "linkx-mobile-iosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54b5e2cca361529e73b0e1c3996c71c40", "path": "linkx_mobile_iosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b60f292f242b02fec82de8bf009e0106", "path": "linkx_mobile_iosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53704883f05c5846c46e46fd70ecaa4f3", "name": "linkx-mobile-iosUITests", "path": "linkx-mobile-iosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "ce159b2766cd6a078d41b2f1364d5fa57505e7038dda8ae52337a220eba11a24", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5946ae46e4993d6f103808e295df5bb26", "name": "linkx-mobile-ios", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5", "path": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "projectDirectory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "targets": ["TARGET@v11_hash=bf6ffefd70f02afcc588a9908295c4e8", "TARGET@v11_hash=ee8a02b32cc7a043f67e2b7784dde8c2", "TARGET@v11_hash=c56b2bc5baf69157ee8e2f1319ec427a"]}
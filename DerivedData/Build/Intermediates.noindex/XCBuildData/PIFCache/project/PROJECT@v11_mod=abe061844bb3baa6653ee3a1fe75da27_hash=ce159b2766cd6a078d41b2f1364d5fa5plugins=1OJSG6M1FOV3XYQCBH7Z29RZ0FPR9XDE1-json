{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5681dcc22f3f67979a2c19e54f017ba49", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5f250f2a4ac3809a14c7142de5c72d6f4", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5893a327220e3674ea9ef897880659df6", "path": "Date+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5664d782cf773541f4f535e28a5ad19c2", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b1671775ca484b6683f4db27e3117791", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50e69090bc3416e6fefc149dd1662d344", "path": "APIClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa560cde82cc0e22bd2476f5808e56f1302", "path": "APIEndpoints.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa54d51b03db1f9abaac5112e9f294a163a", "name": "Network", "path": "Network", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa534b50b0998ef7287a9be0c4ac41ca81e", "path": "AnalyticsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa51e942c9f023a5477b03bd40b98e11e5d", "path": "ConfigurationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53fbde6a616962a79b2a8703fbcee2ba5", "path": "NotificationService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56566af0172384262d8c6f1f010f1a610", "path": "SyncService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa57bbd539680e8bfec208ff6bbf111f11e", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b2ff115dda9c5b3d2f6b06bf295741f9", "path": "KeychainManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d63eaa2a9e2a78ee9d8b6c56dfdc8b25", "name": "Storage", "path": "Storage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa596c76f393d3c1088616481f46dbf250c", "path": "AppConstants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56d0319e16be7eeadcf605420cfec0f94", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5b963205a1ca3b8d93c1e80fcd674ff53", "path": "Error<PERSON><PERSON>ling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f2653a83dd4e0df129c2fdef40d500c0", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50c7271208e6f577d40d00de1c9fb0df2", "path": "Logger.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa56bdc1d4cf549c6f5173e9c4617528e29", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5a42c1854ccacfbd0b781e50be19b2cd8", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa59141038dc1ecabf1679803088d06a673", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5c5e934922ebbe183c0df9f910b9d0fa8", "path": "Re<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5fd4c49f5fb8cad8d2c35a04a66db7cea", "path": "Transaction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa573a3e5d4fd15c8d7acada7459f4243ad", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53571e4aec28c2fc663b895e4bcfde4b0", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa584434bc025c4dffae8bb8f068bd9f250", "path": "AuthRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa50eafc24b2aa168b145f0ff7e703c1e64", "path": "RewardRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d98414fc5b09cacfa3e641cc67fd8267", "path": "TransactionRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa548925f511582aa532855139e805d94a2", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa550ce3c88210b9e2137fe19171c73059c", "name": "Domain", "path": "Domain", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa53bd02025fbe62835d9025e4979c7a9e0", "path": "AuthViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d10528f7cc5234f62e6d6b63a7262607", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a56771557be8e441057e9ef781be8382", "path": "ForgotPasswordView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58c77303feb9647ca74d69755778b96b9", "path": "LoginView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54d5e9832b96fc695106b3aa66f87d071", "path": "RegisterView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5441a64a4afa3bb2f310fe8abbd7294ce", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa561627667eafc801a846b741d2243263d", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5926ffc3a9f5d5cd7f491c5f0d1f51cc6", "path": "EmptyStateView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa57e6a9d1b580d073a23075cfbaea054a4", "path": "QRScannerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5dfb764c54850ddb60d585832d9b5cfdb", "path": "RewardCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa575bae3591944239f7db3ae0086c1868a", "path": "TransactionRowView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa532db0b665c94b00d9e72067acbff76f4", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5bc912c0a8b0e56586f4ad0c75f288771", "name": "Common", "path": "Common", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5d3b431b72040805e460e25f7032ab8c1", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53baf1a7154a04f8f50f9decfbfa8799e", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa58c8ef278d51d3d73bb5c3e6e38148240", "name": "Home", "path": "Home", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f3d38b73bf31897913f7afb2a910db2f", "path": "EditProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa589b9bfeba5aa8e36890b48a93dbe54c3", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa54869a3dd958af2578991bc50b4bfec9f", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa521bdf21dde1a4391a6a52274abd00680", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56c97468092c4320447d6deb7da7ff6eb", "path": "SupportView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa57b49f7dc3c34760563da37953a0bfdbe", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5e7fae12470d499a127e977cc1b024f05", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5efc7794585fc44b096782f5564aa518d", "path": "MyRedemptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55a39e9878c8b78e9e1cd3703c94d44e1", "path": "RewardSearchView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa501af348549f032bfdfe3608e828699a4", "path": "RewardsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5b49788509ff4b958a48199aca64207a8", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5d0fd66a8dfe4c4dbd618c56ca5fc84ac", "name": "Rewards", "path": "Rewards", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f006b544e6b1fcffaff8929cfd4bf7b4", "path": "TransactionDetailView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53d426b9398e32b95a00efb8b1acf7abe", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5fe0e41ae78f7cd0689cf40c0ac08d270", "name": "Transactions", "path": "Transactions", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5dffdd9cf592f0829b1e43bf7d66e4d00", "path": "ReceiveTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58a2912be44699d6ccfbc90eb51986335", "path": "SendTokensView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa55826f1eae22e9c813714b5f06481cbe3", "path": "WalletView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5698f7708ceab0ecef67abcc5acce2bf6", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5bc226b96d9d7d75eb3f3ebb98605b67c", "name": "Wallet", "path": "Wallet", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5aac01812a354a78b44db4b9ff2a7a61b", "name": "Presentation", "path": "Presentation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa595b2cc48b24ea23eb498fbc1b00758c4", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5cb2c33c8a8c5b3811c0795b281ee5112", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "ce159b2766cd6a078d41b2f1364d5fa504a8e3a48641adfa90778c686cc67b50", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5490622497125f9fb222a3a64632a4079", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5f23314658029977395e7e2d4428be6b4", "path": "linkx_mobile_iosApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa58811f5835b78f188785fd8043d853a11", "path": "LinkXApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa51a5509bb0a7553857db590a1538a840f", "name": "linkx-mobile-ios", "path": "linkx-mobile-ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "ce159b2766cd6a078d41b2f1364d5fa5c0debac0a4694d42cc5804674efd83d7", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa56dd09e46f7d3f658c5c26fc181651b96", "path": "linkx_mobile_iosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53cead36256eeb6742addbe83b4b5fac8", "name": "linkx-mobile-iosTests", "path": "linkx-mobile-iosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5597135158a26f2635017fb4adb1684ce", "path": "linkx_mobile_iosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ce159b2766cd6a078d41b2f1364d5fa5a869072ced0db30003c8bb4388fd6f7d", "path": "linkx_mobile_iosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa53704883f05c5846c46e46fd70ecaa4f3", "name": "linkx-mobile-iosUITests", "path": "linkx-mobile-iosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "ce159b2766cd6a078d41b2f1364d5fa57505e7038dda8ae52337a220eba11a24", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "ce159b2766cd6a078d41b2f1364d5fa5946ae46e4993d6f103808e295df5bb26", "name": "linkx-mobile-ios", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "ce159b2766cd6a078d41b2f1364d5fa5", "path": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios.xcodeproj", "projectDirectory": "/Users/<USER>/Develop/linkx/linkx-mobile-ios", "targets": ["TARGET@v11_hash=b1bc6c16226ecfcee1e9c3544f769701", "TARGET@v11_hash=908a218d73ce6094b65f6288dddd863e", "TARGET@v11_hash=38024f73756430206956f60e360a8cb1"]}
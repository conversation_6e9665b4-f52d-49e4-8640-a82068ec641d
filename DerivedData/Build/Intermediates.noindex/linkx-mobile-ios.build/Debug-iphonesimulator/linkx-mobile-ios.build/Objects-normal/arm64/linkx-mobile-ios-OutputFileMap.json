{"": {"diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.dia", "emit-module-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/linkx-mobile-ios-master.swiftdeps"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIClient.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIClient~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Network/APIEndpoints.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/APIEndpoints~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/AnalyticsService.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AnalyticsService~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/ConfigurationService.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ConfigurationService~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/OCRService.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/OCRService~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Services/SyncService.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SyncService~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/DataCleanupManager.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/DataCleanupManager~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Storage/KeychainManager.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/KeychainManager~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/AppConstants.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AppConstants~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/ErrorHandling.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ErrorHandling~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Extensions.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Extensions~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Core/Utils/Logger.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Logger~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Merchant.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Merchant~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Notification.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Notification~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Reward.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Reward~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/Transaction.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/Transaction~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Models/User.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/User~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/AuthRepository.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthRepository~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/RewardRepository.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardRepository~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Domain/Repositories/TransactionRepository.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRepository~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/LinkXApp.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LinkXApp~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Admin/Components/AdminComponents.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminComponents~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Admin/ViewModels/AdminViewModel.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminViewModel~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Admin/Views/AdminDashboardView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminDashboardView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Admin/Views/AdminPlaceholderViews.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AdminPlaceholderViews~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/ViewModels/AuthViewModel.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/AuthViewModel~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/ForgotPasswordView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ForgotPasswordView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/LoginView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/LoginView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Authentication/Views/RegisterView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RegisterView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/BadgeView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/BadgeView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/ManualReceiptEntryView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ManualReceiptEntryView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/ModernTabBar.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernTabBar~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/QRScannerView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/QRScannerView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/ReceiptScannerView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiptScannerView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/RewardCardView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardCardView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Components/TransactionRowView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionRowView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Views/EmptyStateView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EmptyStateView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Views/ModernEmptyStateView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ModernEmptyStateView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Views/RoleBasedTabView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RoleBasedTabView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Common/Views/SkeletonLoadingView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SkeletonLoadingView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Home/Views/HomeView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/HomeView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchant/Components/MerchantComponents.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantComponents~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchant/ViewModels/MerchantViewModel.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantViewModel~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchant/Views/MerchantDashboardView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDashboardView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchant/Views/MerchantPlaceholderViews.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantPlaceholderViews~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchants/ViewModels/MerchantsViewModel.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsViewModel~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchants/Views/MerchantDetailView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantDetailView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchants/Views/MerchantSearchView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantSearchView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Merchants/Views/MerchantsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MerchantsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Notifications/ViewModels/NotificationsViewModel.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewModel~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Notifications/Views/NotificationRowView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationRowView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Notifications/Views/NotificationSettingsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationSettingsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Notifications/Views/NotificationsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Notifications/Views/NotificationsViewPreview.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationsViewPreview~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/EditProfileView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/EditProfileView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/ProfileView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ProfileView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SecuritySettingsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SecuritySettingsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SettingsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SettingsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Profile/Views/SupportView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SupportView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/MyRedemptionsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/MyRedemptionsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardSearchView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardSearchView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Rewards/Views/RewardsView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/RewardsView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionDetailView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionDetailView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Transactions/Views/TransactionHistoryView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/TransactionHistoryView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/ReceiveTokensView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/ReceiveTokensView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/SendTokensView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/SendTokensView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Presentation/Wallet/Views/WalletView.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/WalletView~partial.swiftmodule"}, "/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/Services/NotificationService.swift": {"const-values": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftconstvalues", "dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.d", "diagnostics": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.dia", "index-unit-output-path": "/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "llvm-bc": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.bc", "object": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.o", "swift-dependencies": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService.swiftdeps", "swiftmodule": "/Users/<USER>/Develop/linkx/linkx-mobile-ios/DerivedData/Build/Intermediates.noindex/linkx-mobile-ios.build/Debug-iphonesimulator/linkx-mobile-ios.build/Objects-normal/arm64/NotificationService~partial.swiftmodule"}}
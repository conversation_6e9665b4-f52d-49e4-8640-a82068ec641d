<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CodeCoverageBuildableInfos</key>
	<array>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>17A56CD82E2D19DE00A938D3:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>linkx-mobile-ios.app</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app/linkx-mobile-ios</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>ContentView.swift</string>
				<string>Core/Extensions/Date+Extensions.swift</string>
				<string>Core/Extensions/View+Extensions.swift</string>
				<string>Core/Network/APIClient.swift</string>
				<string>Core/Network/APIEndpoints.swift</string>
				<string>Core/Services/AnalyticsService.swift</string>
				<string>Core/Services/ConfigurationService.swift</string>
				<string>Core/Services/NotificationService.swift</string>
				<string>Core/Services/SyncService.swift</string>
				<string>Core/Storage/KeychainManager.swift</string>
				<string>Core/Utils/AppConstants.swift</string>
				<string>Core/Utils/Constants.swift</string>
				<string>Core/Utils/ErrorHandling.swift</string>
				<string>Core/Utils/Extensions.swift</string>
				<string>Core/Utils/Logger.swift</string>
				<string>Domain/Models/Merchant.swift</string>
				<string>Domain/Models/Reward.swift</string>
				<string>Domain/Models/Transaction.swift</string>
				<string>Domain/Models/User.swift</string>
				<string>Domain/Repositories/AuthRepository.swift</string>
				<string>Domain/Repositories/RewardRepository.swift</string>
				<string>Domain/Repositories/TransactionRepository.swift</string>
				<string>LinkXApp.swift</string>
				<string>Presentation/Authentication/ViewModels/AuthViewModel.swift</string>
				<string>Presentation/Authentication/Views/ForgotPasswordView.swift</string>
				<string>Presentation/Authentication/Views/LoginView.swift</string>
				<string>Presentation/Authentication/Views/RegisterView.swift</string>
				<string>Presentation/Common/Components/EmptyStateView.swift</string>
				<string>Presentation/Common/Components/QRScannerView.swift</string>
				<string>Presentation/Common/Components/RewardCardView.swift</string>
				<string>Presentation/Common/Components/TransactionRowView.swift</string>
				<string>Presentation/Home/Views/HomeView.swift</string>
				<string>Presentation/Profile/Views/EditProfileView.swift</string>
				<string>Presentation/Profile/Views/ProfileView.swift</string>
				<string>Presentation/Profile/Views/SecuritySettingsView.swift</string>
				<string>Presentation/Profile/Views/SettingsView.swift</string>
				<string>Presentation/Profile/Views/SupportView.swift</string>
				<string>Presentation/Rewards/Views/MyRedemptionsView.swift</string>
				<string>Presentation/Rewards/Views/RewardSearchView.swift</string>
				<string>Presentation/Rewards/Views/RewardsView.swift</string>
				<string>Presentation/Transactions/Views/TransactionDetailView.swift</string>
				<string>Presentation/Wallet/Views/ReceiveTokensView.swift</string>
				<string>Presentation/Wallet/Views/SendTokensView.swift</string>
				<string>Presentation/Wallet/Views/WalletView.swift</string>
				<string>linkx_mobile_iosApp.swift</string>
			</array>
			<key>SourceFilesCommonPathPrefix</key>
			<string>/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-ios/</string>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>17A56CE82E2D19DF00A938D3:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>linkx-mobile-iosTests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app/PlugIns/linkx-mobile-iosTests.xctest/linkx-mobile-iosTests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-iosTests/linkx_mobile_iosTests.swift</string>
			</array>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>17A56CF22E2D19DF00A938D3:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>linkx-mobile-iosUITests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-iosUITests-Runner.app/PlugIns/linkx-mobile-iosUITests.xctest/linkx-mobile-iosUITests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>.swift</string>
				<string>LaunchTests.swift</string>
			</array>
			<key>SourceFilesCommonPathPrefix</key>
			<string>/Users/<USER>/Develop/linkx/linkx-mobile-ios/linkx-mobile-iosUITests/linkx_mobile_iosUITests</string>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
	</array>
	<key>ContainerInfo</key>
	<dict>
		<key>ContainerName</key>
		<string>linkx-mobile-ios</string>
		<key>SchemeName</key>
		<string>linkx-mobile-ios</string>
	</dict>
	<key>TestConfigurations</key>
	<array>
		<dict>
			<key>Name</key>
			<string>Test Scheme Action</string>
			<key>TestTargets</key>
			<array>
				<dict>
					<key>BlueprintName</key>
					<string>linkx-mobile-iosUITests</string>
					<key>BlueprintProviderName</key>
					<string>linkx-mobile-ios</string>
					<key>BlueprintProviderRelativePath</key>
					<string>linkx-mobile-ios.xcodeproj</string>
					<key>BundleIdentifiersForCrashReportEmphasis</key>
					<array>
						<string>com.linkx-mobile-ios</string>
						<string>com.linkx-mobile-iosTests</string>
						<string>com.linkx-mobile-iosUITests</string>
					</array>
					<key>ClangProfileDataDirectoryPath</key>
					<string>__DERIVEDDATA__/Build/ProfileData</string>
					<key>CommandLineArguments</key>
					<array/>
					<key>DefaultTestExecutionTimeAllowance</key>
					<integer>600</integer>
					<key>DependentProductPaths</key>
					<array>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app</string>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app/PlugIns/linkx-mobile-iosTests.xctest</string>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-iosUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-iosUITests-Runner.app/PlugIns/linkx-mobile-iosUITests.xctest</string>
					</array>
					<key>DiagnosticCollectionPolicy</key>
					<integer>1</integer>
					<key>EnvironmentVariables</key>
					<dict>
						<key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
						<string>com.apple.AppStore</string>
						<key>OS_ACTIVITY_DT_MODE</key>
						<string>YES</string>
						<key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
						<string>1</string>
						<key>TERM</key>
						<string>dumb</string>
					</dict>
					<key>IsUITestBundle</key>
					<true/>
					<key>IsXCTRunnerHostedTestBundle</key>
					<true/>
					<key>ParallelizationEnabled</key>
					<true/>
					<key>PreferredScreenCaptureFormat</key>
					<string>screenRecording</string>
					<key>ProductModuleName</key>
					<string>linkx_mobile_iosUITests</string>
					<key>SystemAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
					<key>TestBundlePath</key>
					<string>__TESTHOST__/PlugIns/linkx-mobile-iosUITests.xctest</string>
					<key>TestHostBundleIdentifier</key>
					<string>com.linkx-mobile-iosUITests.xctrunner</string>
					<key>TestHostPath</key>
					<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-iosUITests-Runner.app</string>
					<key>TestLanguage</key>
					<string></string>
					<key>TestRegion</key>
					<string></string>
					<key>TestTimeoutsEnabled</key>
					<false/>
					<key>TestingEnvironmentVariables</key>
					<dict>
						<key>DYLD_FRAMEWORK_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator:__PLATFORMS__/iPhoneSimulator.platform/Developer/Library/Frameworks</string>
						<key>DYLD_INSERT_LIBRARIES</key>
						<string>__SIMRUNTIMEROOT__/usr/lib/libMainThreadChecker.dylib</string>
						<key>DYLD_LIBRARY_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator:__PLATFORMS__/iPhoneSimulator.platform/Developer/usr/lib</string>
						<key>MTC_CRASH_ON_REPORT</key>
						<string>1</string>
						<key>__XCODE_BUILT_PRODUCTS_DIR_PATHS</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XPC_DYLD_FRAMEWORK_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XPC_DYLD_LIBRARY_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
					</dict>
					<key>ToolchainsSettingValue</key>
					<array/>
					<key>UITargetAppCommandLineArguments</key>
					<array/>
					<key>UITargetAppEnvironmentVariables</key>
					<dict>
						<key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
						<string>com.apple.AppStore</string>
						<key>DYLD_FRAMEWORK_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>DYLD_LIBRARY_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XCODE_BUILT_PRODUCTS_DIR_PATHS</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XPC_DYLD_FRAMEWORK_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XPC_DYLD_LIBRARY_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
					</dict>
					<key>UITargetAppMainThreadCheckerEnabled</key>
					<true/>
					<key>UITargetAppPath</key>
					<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app</string>
					<key>UserAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
				</dict>
				<dict>
					<key>BlueprintName</key>
					<string>linkx-mobile-iosTests</string>
					<key>BlueprintProviderName</key>
					<string>linkx-mobile-ios</string>
					<key>BlueprintProviderRelativePath</key>
					<string>linkx-mobile-ios.xcodeproj</string>
					<key>BundleIdentifiersForCrashReportEmphasis</key>
					<array>
						<string>com.linkx-mobile-ios</string>
						<string>com.linkx-mobile-iosTests</string>
						<string>com.linkx-mobile-iosUITests</string>
					</array>
					<key>ClangProfileDataDirectoryPath</key>
					<string>__DERIVEDDATA__/Build/ProfileData</string>
					<key>CommandLineArguments</key>
					<array/>
					<key>DefaultTestExecutionTimeAllowance</key>
					<integer>600</integer>
					<key>DependentProductPaths</key>
					<array>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app</string>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app/PlugIns/linkx-mobile-iosTests.xctest</string>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-iosUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-iosUITests-Runner.app/PlugIns/linkx-mobile-iosUITests.xctest</string>
					</array>
					<key>DiagnosticCollectionPolicy</key>
					<integer>1</integer>
					<key>EnvironmentVariables</key>
					<dict>
						<key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
						<string>com.apple.AppStore</string>
						<key>OS_ACTIVITY_DT_MODE</key>
						<string>YES</string>
						<key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
						<string>1</string>
						<key>TERM</key>
						<string>dumb</string>
					</dict>
					<key>IsAppHostedTestBundle</key>
					<true/>
					<key>ParallelizationEnabled</key>
					<true/>
					<key>PreferredScreenCaptureFormat</key>
					<string>screenRecording</string>
					<key>ProductModuleName</key>
					<string>linkx_mobile_iosTests</string>
					<key>SystemAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
					<key>TestBundlePath</key>
					<string>__TESTHOST__/PlugIns/linkx-mobile-iosTests.xctest</string>
					<key>TestHostBundleIdentifier</key>
					<string>com.linkx-mobile-ios</string>
					<key>TestHostPath</key>
					<string>__TESTROOT__/Debug-iphonesimulator/linkx-mobile-ios.app</string>
					<key>TestLanguage</key>
					<string></string>
					<key>TestRegion</key>
					<string></string>
					<key>TestTimeoutsEnabled</key>
					<false/>
					<key>TestingEnvironmentVariables</key>
					<dict>
						<key>DYLD_FRAMEWORK_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator:__PLATFORMS__/iPhoneSimulator.platform/Developer/Library/Frameworks</string>
						<key>DYLD_INSERT_LIBRARIES</key>
						<string>__TESTHOST__/Frameworks/libXCTestBundleInject.dylib:__SIMRUNTIMEROOT__/usr/lib/libMainThreadChecker.dylib</string>
						<key>DYLD_LIBRARY_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator:__PLATFORMS__/iPhoneSimulator.platform/Developer/usr/lib</string>
						<key>MTC_CRASH_ON_REPORT</key>
						<string>1</string>
						<key>XCInjectBundleInto</key>
						<string>unused</string>
						<key>__XCODE_BUILT_PRODUCTS_DIR_PATHS</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XPC_DYLD_FRAMEWORK_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
						<key>__XPC_DYLD_LIBRARY_PATH</key>
						<string>__TESTROOT__/Debug-iphonesimulator</string>
					</dict>
					<key>ToolchainsSettingValue</key>
					<array/>
					<key>UserAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>TestPlan</key>
	<dict>
		<key>IsDefault</key>
		<true/>
		<key>Name</key>
		<string>linkx-mobile-ios</string>
	</dict>
	<key>__xctestrun_metadata__</key>
	<dict>
		<key>FormatVersion</key>
		<integer>2</integer>
	</dict>
</dict>
</plist>

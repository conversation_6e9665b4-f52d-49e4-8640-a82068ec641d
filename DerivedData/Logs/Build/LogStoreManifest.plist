<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>152198BE-87AE-4C97-8B17-B8EF26E18B3D</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>152198BE-87AE-4C97-8B17-B8EF26E18B3D.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>linkx-mobile-ios project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>linkx-mobile-ios</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project linkx-mobile-ios with scheme linkx-mobile-ios</string>
			<key>timeStartedRecording</key>
			<real>774712238.81113303</real>
			<key>timeStoppedRecording</key>
			<real>774712238.97544503</real>
			<key>title</key>
			<string>Cleaning project linkx-mobile-ios with scheme linkx-mobile-ios</string>
			<key>uniqueIdentifier</key>
			<string>152198BE-87AE-4C97-8B17-B8EF26E18B3D</string>
		</dict>
	</dict>
</dict>
</plist>

---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/Photos.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1733472479000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/Photos.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
    size:            41784
  - mtime:           1731231370000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            2050253
    sdk_relative:    true
  - mtime:           1731232120000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1731232345000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3712
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            997
    sdk_relative:    true
  - mtime:           1731232380000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            998
    sdk_relative:    true
  - mtime:           1731232368000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            961
    sdk_relative:    true
  - mtime:           1731232368000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1423
    sdk_relative:    true
  - mtime:           1731232386000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            693
    sdk_relative:    true
  - mtime:           1731232345000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            16104
    sdk_relative:    true
  - mtime:           1731231433000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            4198
    sdk_relative:    true
  - mtime:           1731232399000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            19775
    sdk_relative:    true
  - mtime:           1731232717000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            248687
    sdk_relative:    true
  - mtime:           1731233121000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22664
    sdk_relative:    true
  - mtime:           1731234258000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            172314
    sdk_relative:    true
  - mtime:           1731227095000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1731228246000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1731230793000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1731228628000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1731228215000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1731227770000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1731234228000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            6429
    sdk_relative:    true
  - mtime:           1731234408000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            56578
    sdk_relative:    true
  - mtime:           1731234573000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22703
    sdk_relative:    true
  - mtime:           1731234578000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            33153
    sdk_relative:    true
  - mtime:           1731232745000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3740
    sdk_relative:    true
  - mtime:           1731234295000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            94364
    sdk_relative:    true
  - mtime:           1731235070000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            998796
    sdk_relative:    true
  - mtime:           1731235399000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            42479
    sdk_relative:    true
  - mtime:           1731227987000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1731654169000000000
    path:            'System/Library/Frameworks/CoreLocation.framework/Headers/CoreLocation.apinotes'
    size:            1557
    sdk_relative:    true
  - mtime:           1731235658000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1730064096000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1731230309000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1731387490000000000
    path:            'System/Library/Frameworks/CoreAudioTypes.framework/Headers/CoreAudioTypes.apinotes'
    size:            1519
    sdk_relative:    true
  - mtime:           1731645472000000000
    path:            'System/Library/Frameworks/CoreMedia.framework/Headers/CoreMedia.apinotes'
    size:            65704
    sdk_relative:    true
  - mtime:           1731236699000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1731387602000000000
    path:            'System/Library/Frameworks/AudioToolbox.framework/Headers/AudioToolbox.apinotes'
    size:            114
    sdk_relative:    true
  - mtime:           1731472819000000000
    path:            'System/Library/Frameworks/MediaToolbox.framework/Headers/MediaToolbox.apinotes'
    size:            215
    sdk_relative:    true
  - mtime:           1731228760000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1731235954000000000
    path:            'System/Library/Frameworks/AVFAudio.framework/Headers/AVFAudio.apinotes'
    size:            8309
    sdk_relative:    true
  - mtime:           1731646876000000000
    path:            'System/Library/Frameworks/AVFoundation.framework/Headers/AVFoundation.apinotes'
    size:            98450
    sdk_relative:    true
  - mtime:           1731588002000000000
    path:            'System/Library/Frameworks/Photos.framework/Headers/Photos.apinotes'
    size:            308
    sdk_relative:    true
  - mtime:           1731654096000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            52738
    sdk_relative:    true
  - mtime:           1731235363000000000
    path:            'usr/lib/swift/CoreMIDI.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            15986
    sdk_relative:    true
  - mtime:           1731236560000000000
    path:            'usr/lib/swift/CoreAudio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            9859
    sdk_relative:    true
  - mtime:           1731235474000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            25853
    sdk_relative:    true
  - mtime:           1731473052000000000
    path:            'usr/lib/swift/CoreMedia.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            133172
    sdk_relative:    true
  - mtime:           1731234262000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            229349
    sdk_relative:    true
  - mtime:           1731235969000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1653
    sdk_relative:    true
  - mtime:           1731235320000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            20456
    sdk_relative:    true
  - mtime:           1731473060000000000
    path:            'usr/lib/swift/AVFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            72702
    sdk_relative:    true
  - mtime:           1731235946000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            584
    sdk_relative:    true
  - mtime:           1731232120000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1731234575000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            107896
    sdk_relative:    true
  - mtime:           1731237875000000000
    path:            'usr/lib/swift/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            19135
    sdk_relative:    true
  - mtime:           1731588059000000000
    path:            'System/Library/Frameworks/Photos.framework/Modules/Photos.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1978
    sdk_relative:    true
version:         1
...

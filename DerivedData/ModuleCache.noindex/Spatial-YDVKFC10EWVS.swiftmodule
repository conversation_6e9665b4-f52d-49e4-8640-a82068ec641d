---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1733472373000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
    size:            543168
  - mtime:           1731231370000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            2050253
    sdk_relative:    true
  - mtime:           1731232345000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3712
    sdk_relative:    true
  - mtime:           1731232368000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1423
    sdk_relative:    true
  - mtime:           1731232345000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            16104
    sdk_relative:    true
  - mtime:           1731232120000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            997
    sdk_relative:    true
  - mtime:           1731232380000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            998
    sdk_relative:    true
  - mtime:           1731232368000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            961
    sdk_relative:    true
  - mtime:           1731232386000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            693
    sdk_relative:    true
  - mtime:           1731231433000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            4198
    sdk_relative:    true
  - mtime:           1731232399000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            19775
    sdk_relative:    true
  - mtime:           1731232717000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            248687
    sdk_relative:    true
  - mtime:           1731233121000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22664
    sdk_relative:    true
  - mtime:           1731234262000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            229349
    sdk_relative:    true
  - mtime:           1731234411000000000
    path:            'usr/lib/swift/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            99059
    sdk_relative:    true
version:         1
...

//
//  APIClient.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Combine

// MARK: - Network Error
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case serverError(Int, String?)
    case unauthorized
    case networkUnavailable
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noData:
            return "No data received"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        case .serverError(let code, let message):
            return "Server error (\(code)): \(message ?? "Unknown error")"
        case .unauthorized:
            return "Unauthorized access"
        case .networkUnavailable:
            return "Network unavailable"
        case .unknown(let error):
            return "Unknown error: \(error.localizedDescription)"
        }
    }
}

// MARK: - API Response
struct APIResponse<T: Codable>: Codable {
    let data: T?
    let message: String?
    let success: Bool
}

struct EmptyResponse: Codable {
    // Empty response for endpoints that don't return data
}

// MARK: - API Client
class APIClient: ObservableObject {
    static let shared = APIClient()

    private let baseURL: String
    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder
    private let logger = Logger.shared

    @Published var isLoading = false
    @Published var networkStatus: NetworkStatus = .connected

    private init() {
        self.baseURL = AppConstants.Environment.current.apiBaseURL

        // Configure URLSession with timeout and caching
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = AppConstants.API.timeout
        config.timeoutIntervalForResource = AppConstants.API.timeout * 2
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        config.urlCache = URLCache(memoryCapacity: 10 * 1024 * 1024, diskCapacity: 50 * 1024 * 1024)

        self.session = URLSession(configuration: config)

        // Configure JSON decoder/encoder
        self.decoder = JSONDecoder()
        self.encoder = JSONEncoder()

        // Configure date formatting - use custom strategy to handle multiple formats
        decoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // Try multiple date formats
            let formatters = [
                // ISO8601 with milliseconds and Z
                "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                // ISO8601 with milliseconds and timezone
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
                // ISO8601 without milliseconds
                "yyyy-MM-dd'T'HH:mm:ss'Z'",
                "yyyy-MM-dd'T'HH:mm:ssZ"
            ]

            for format in formatters {
                let formatter = DateFormatter()
                formatter.dateFormat = format
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                formatter.locale = Locale(identifier: "en_US_POSIX")

                if let date = formatter.date(from: dateString) {
                    return date
                }
            }

            throw DecodingError.dataCorrupted(
                DecodingError.Context(
                    codingPath: decoder.codingPath,
                    debugDescription: "Cannot decode date string \(dateString)"
                )
            )
        }

        // Use standard ISO8601 for encoding
        let encoderFormatter = DateFormatter()
        encoderFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        encoderFormatter.timeZone = TimeZone(abbreviation: "UTC")
        encoderFormatter.locale = Locale(identifier: "en_US_POSIX")
        encoder.dateEncodingStrategy = .formatted(encoderFormatter)

        // Start network monitoring
        startNetworkMonitoring()
    }
    
    // MARK: - Network Status
    enum NetworkStatus {
        case connected
        case disconnected
        case poor
    }

    // MARK: - Network Monitoring
    private func startNetworkMonitoring() {
        // TODO: Implement network reachability monitoring
        // This would typically use Network framework
    }

    // MARK: - Generic Request Method
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        responseType: T.Type,
        requiresAuth: Bool = true
    ) async throws -> T {

        let startTime = CFAbsoluteTimeGetCurrent()

        guard let url = URL(string: baseURL + endpoint) else {
            logger.error("Invalid URL: \(baseURL + endpoint)", category: .network)
            throw NetworkError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("iOS", forHTTPHeaderField: "X-Platform")
        request.setValue(AppConstants.AppInfo.fullVersion, forHTTPHeaderField: "X-App-Version")

        // Add authorization header if required and token exists
        if requiresAuth, let token = TokenManager.shared.getToken() {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // Add custom headers
        headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Add parameters for POST/PUT requests
        if let parameters = parameters, method != .GET {
            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
            } catch {
                throw NetworkError.decodingError(error)
            }
        }
        
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.unknown(URLError(.badServerResponse))
            }
            
            // Handle HTTP status codes
            switch httpResponse.statusCode {
            case 200...299:
                break
            case 401:
                // Token expired, clear it
                TokenManager.shared.clearToken()
                throw NetworkError.unauthorized
            case 400...499:
                let errorMessage = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
                let message = errorMessage?["message"] as? String
                throw NetworkError.serverError(httpResponse.statusCode, message)
            case 500...599:
                throw NetworkError.serverError(httpResponse.statusCode, "Server error")
            default:
                throw NetworkError.serverError(httpResponse.statusCode, "Unknown error")
            }
            
            // Decode response
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                return try decoder.decode(T.self, from: data)
            } catch {
                throw NetworkError.decodingError(error)
            }
            
        } catch {
            if error is NetworkError {
                throw error
            } else {
                throw NetworkError.unknown(error)
            }
        }
    }

    // MARK: - Helper Methods
    private func performRequestWithRetry(request: URLRequest, retryCount: Int = 0) async throws -> (Data, URLResponse) {
        do {
            return try await session.data(for: request)
        } catch {
            // Retry logic for network errors
            if retryCount < AppConstants.API.retryAttempts {
                if shouldRetry(error: error) {
                    logger.warning("Request failed, retrying (\(retryCount + 1)/\(AppConstants.API.retryAttempts))", category: .network)
                    try await Task.sleep(nanoseconds: UInt64(AppConstants.API.retryDelay * 1_000_000_000))
                    return try await performRequestWithRetry(request: request, retryCount: retryCount + 1)
                }
            }
            throw error
        }
    }

    private func shouldRetry(error: Error) -> Bool {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut, .networkConnectionLost, .notConnectedToInternet:
                return true
            default:
                return false
            }
        }
        return false
    }

    private func handleHTTPStatusCode(_ statusCode: Int, data: Data) throws {
        switch statusCode {
        case 200...299:
            break
        case 401:
            // Token expired, clear it and notify auth state
            TokenManager.shared.clearToken()
            throw NetworkError.unauthorized
        case 403:
            throw NetworkError.unauthorized
        case 404:
            throw NetworkError.decodingError(NSError(domain: "APIClient", code: -1))
        case 429:
            throw NetworkError.serverError(429, "Too many requests")
        case 400...499:
            let errorMessage = try? JSONSerialization.jsonObject(with: data) as? [String: Any]
            let message = errorMessage?["message"] as? String ?? "Client error"
            throw NetworkError.serverError(statusCode, message)
        case 500...599:
            throw NetworkError.serverError(statusCode, "Server error")
        default:
            throw NetworkError.serverError(statusCode, "Unknown error")
        }
    }
}

// MARK: - AnyEncodable Helper
struct AnyEncodable: Encodable {
    private let encodable: Encodable

    init(_ encodable: Encodable) {
        self.encodable = encodable
    }

    func encode(to encoder: Encoder) throws {
        try encodable.encode(to: encoder)
    }
}

extension APIClient {
    // MARK: - Multipart Upload Method
    func uploadMultipart<T: Codable>(
        endpoint: String,
        fileData: Data,
        fileName: String,
        fieldName: String,
        mimeType: String,
        additionalFields: [String: String] = [:],
        headers: [String: String]? = nil,
        responseType: T.Type,
        requiresAuth: Bool = true
    ) async throws -> T {

        guard let url = URL(string: baseURL + endpoint) else {
            throw NetworkError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        // Create boundary
        let boundary = "Boundary-\(UUID().uuidString)"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        // Add auth header if required
        if requiresAuth {
            if let token = TokenManager.shared.getToken() {
                request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            } else {
                throw NetworkError.unauthorized
            }
        }

        // Add custom headers
        headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }

        // Create multipart body
        var body = Data()

        // Add additional fields
        for (key, value) in additionalFields {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("Content-Disposition: form-data; name=\"\(key)\"\r\n\r\n".data(using: .utf8)!)
            body.append("\(value)\r\n".data(using: .utf8)!)
        }

        // Add file data
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"\(fieldName)\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: \(mimeType)\r\n\r\n".data(using: .utf8)!)
        body.append(fileData)
        body.append("\r\n".data(using: .utf8)!)
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)

        request.httpBody = body

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.unknown(NSError(domain: "APIClient", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"]))
            }

            // Log response for debugging
            if let responseString = String(data: data, encoding: .utf8) {
                Logger.shared.debug("API Response (\(httpResponse.statusCode)): \(responseString)", category: .network)
            }

            guard 200...299 ~= httpResponse.statusCode else {
                if httpResponse.statusCode == 401 {
                    throw NetworkError.unauthorized
                } else {
                    throw NetworkError.serverError(httpResponse.statusCode, String(data: data, encoding: .utf8))
                }
            }

            let decodedResponse = try decoder.decode(responseType, from: data)
            return decodedResponse

        } catch {
            if error is NetworkError {
                throw error
            } else if error is DecodingError {
                throw NetworkError.decodingError(error)
            } else {
                throw NetworkError.unknown(error)
            }
        }
    }
}

// MARK: - HTTP Method
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - Token Manager
class TokenManager {
    static let shared = TokenManager()
    
    private let tokenKey = "linkx_access_token"
    private let userKey = "linkx_current_user"
    
    private init() {}
    
    func saveToken(_ token: String) {
        UserDefaults.standard.set(token, forKey: tokenKey)
    }
    
    func getToken() -> String? {
        return UserDefaults.standard.string(forKey: tokenKey)
    }
    
    func clearToken() {
        UserDefaults.standard.removeObject(forKey: tokenKey)
        UserDefaults.standard.removeObject(forKey: userKey)
    }
    
    func saveUser<T: Codable>(_ user: T) {
        if let encoded = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(encoded, forKey: userKey)
        }
    }
    
    func getUser<T: Codable>(_ type: T.Type) -> T? {
        guard let data = UserDefaults.standard.data(forKey: userKey) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
    
    var isLoggedIn: Bool {
        return getToken() != nil
    }
}

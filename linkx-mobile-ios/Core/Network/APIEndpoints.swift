//
//  APIEndpoints.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation

// MARK: - API Endpoints
struct APIEndpoints {
    
    // MARK: - Authentication Endpoints
    struct Auth {
        static let login = "/v1/auth/login"
        static let register = "/v1/auth/register"
        static let refreshToken = "/v1/auth/refresh"
        static let logout = "/v1/auth/logout"
        static let forgotPassword = "/v1/auth/forgot-password"
        static let resetPassword = "/v1/auth/reset-password"
        static let verifyEmail = "/v1/auth/verify-email"
        static let resendVerification = "/v1/auth/resend-verification"
        static let changePassword = "/v1/auth/change-password"

        // Merchant Auth Endpoints
        static let merchantRegister = "/v1/auth/merchant/register"
        static let merchantLogin = "/v1/auth/merchant/login"
    }
    
    // MARK: - User Endpoints
    struct Users {
        static let profile = "/v1/users/profile"
        static let updateProfile = "/v1/users/profile"
        static let balance = "/v1/users/balance"
        static let uploadAvatar = "/v1/users/avatar"
        static let deleteAccount = "/v1/users/account"
        static let preferences = "/v1/users/preferences"
        static let sessions = "/v1/users/sessions"
        static let securityLog = "/v1/users/security-log"
        static let exportData = "/v1/users/export-data"
    }
    
    // MARK: - Wallet Endpoints
    struct Wallet {
        static let balance = "/v1/users/balance"  // Updated to match OpenAPI v1
        static let address = "/v1/wallet/address"
        static let generateAddress = "/v1/wallet/generate-address"
        static let transactions = "/v1/transactions/history"
        static func transaction(id: String) -> String {
            return "/v1/transactions/\(id)"
        }
        static let send = "/v1/wallet/send"
        static let receive = "/v1/wallet/receive"
        static let history = "/v1/transactions/history"
        static let stats = "/v1/wallet/stats"
    }
    
    // MARK: - Transaction Endpoints
    struct Transactions {
        static let list = "/v1/transactions/history"
        static let create = "/v1/transactions"
        static func details(id: String) -> String {
            return "/v1/transactions/\(id)"
        }
        static let earn = "/v1/transactions/earn"
        static let balance = "/v1/transactions/balance"
        static let history = "/v1/transactions/history"
        static let transfer = "/v1/transactions/transfer"
        static let status = "/v1/transactions/status"
        static let fees = "/v1/transactions/fees"
        static let estimate = "/v1/transactions/estimate"
    }
    
    // MARK: - Merchant Endpoints
    struct Merchants {
        static let list = "/v1/merchants"
        static func details(id: String) -> String {
            return "/v1/merchants/\(id)"
        }
        static let search = "/v1/merchants/search"
        static let nearby = "/v1/merchants/nearby"
        static let categories = "/v1/merchants/categories"
        static let verify = "/v1/merchants/verify"
        static let qrCode = "/v1/merchants/qr-code"
        static func generateApiKey(id: String) -> String {
            return "/v1/merchants/\(id)/generate-api-key"
        }

        // Merchant Dashboard Endpoints
        static let dashboardWallet = "/merchant/dashboard/wallet"
        static let dashboardPayments = "/merchant/dashboard/payments"

        // Merchant API Endpoints
        static let apiProfile = "/merchant-api/profile"
        static let apiStats = "/merchant-api/stats"
        static let apiWebhookTest = "/merchant-api/webhook-test"
    }
    
    // MARK: - Reward Endpoints
    struct Rewards {
        static let list = "/v1/rewards"
        static func details(id: String) -> String {
            return "/v1/rewards/\(id)"
        }
        static let featured = "/v1/rewards/featured"
        static let popular = "/v1/rewards/popular"
        static let categories = "/v1/rewards/categories"
        static let search = "/v1/rewards/search"
        static func redeem(id: String) -> String {
            return "/v1/rewards/\(id)/redeem"
        }
        static let redemptions = "/v1/rewards/user/redemptions"
        static func redemption(id: String) -> String {
            return "/v1/rewards/redemptions/\(id)"
        }
        static let availability = "/v1/rewards/availability"
    }
    
    // MARK: - Notification Endpoints
    struct Notifications {
        static let list = "/v1/notifications"
        static let markRead = "/v1/notifications/mark-read"
        static let markAllRead = "/v1/notifications/mark-all-read"
        static let preferences = "/v1/notifications/preferences"
        static let deviceToken = "/v1/notifications/device-token"
        static let unsubscribe = "/v1/notifications/unsubscribe"
    }
    
    // MARK: - Admin Endpoints
    struct Admin {
        static let stats = "/v1/admin/stats"
        static let rewards = "/v1/admin/rewards"
        static func reward(id: String) -> String {
            return "/v1/admin/rewards/\(id)"
        }
    }

    // MARK: - OCR Endpoints
    struct OCR {
        static let processReceipt = "/v1/ocr/process-receipt"
        static let processReceiptUrl = "/v1/ocr/process-receipt-url"
        static let history = "/v1/ocr/history"
        static func receipt(id: String) -> String {
            return "/v1/ocr/receipt/\(id)"
        }
        static let stats = "/v1/ocr/stats"
    }

    // MARK: - Settlement Endpoints
    struct Settlements {
        static let summary = "/settlements/summary"
        static let list = "/settlements"
        static let generateWeekly = "/settlements/generate-weekly"
        static func process(id: String) -> String {
            return "/settlements/\(id)/process"
        }
        static func complete(id: String) -> String {
            return "/settlements/\(id)/complete"
        }
    }

    // MARK: - Public Endpoints
    struct Public {
        static let merchantOnboard = "/public/merchants/onboard"
        static let health = "/health"
        static let healthReady = "/health/ready"
        static let healthLive = "/health/live"
        static let healthStartup = "/health/startup"
    }

    // MARK: - Support Endpoints
    struct Support {
        static let tickets = "/v1/support/tickets"
        static let createTicket = "/v1/support/tickets"
        static func ticket(id: String) -> String {
            return "/v1/support/tickets/\(id)"
        }
        static let faq = "/v1/support/faq"
        static let feedback = "/v1/support/feedback"
        static let contact = "/v1/support/contact"
    }

    // MARK: - Analytics Endpoints
    struct Analytics {
        static let events = "/v1/analytics/events"
        static let userStats = "/v1/analytics/user-stats"
        static let transactionStats = "/v1/analytics/transaction-stats"
        static let rewardStats = "/v1/analytics/reward-stats"
    }

    // MARK: - System Endpoints
    struct System {
        static let health = "/health"
        static let version = "/v1/system/version"
        static let config = "/v1/system/config"
        static let maintenance = "/v1/system/maintenance"
        static let announcements = "/v1/system/announcements"
    }
}

// MARK: - API Request Builder
struct APIRequest {
    let endpoint: String
    let method: HTTPMethod
    let parameters: [String: Any]?
    let headers: [String: String]?
    let requiresAuth: Bool
    
    init(
        endpoint: String,
        method: HTTPMethod = .GET,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        requiresAuth: Bool = true
    ) {
        self.endpoint = endpoint
        self.method = method
        self.parameters = parameters
        self.headers = headers
        self.requiresAuth = requiresAuth
    }
}



// MARK: - API Error
struct APIError: Codable, Error {
    let code: String
    let message: String
    let field: String?
    let details: [String: String]?

    enum CodingKeys: String, CodingKey {
        case code, message, field, details
    }
}

// MARK: - Pagination Request
struct PaginationRequest: Codable {
    let page: Int
    let limit: Int
    let sortBy: String?
    let sortOrder: SortOrder?
    
    enum SortOrder: String, Codable {
        case asc = "asc"
        case desc = "desc"
    }
    
    init(
        page: Int = 1,
        limit: Int = AppConstants.Pagination.defaultLimit,
        sortBy: String? = nil,
        sortOrder: SortOrder? = nil
    ) {
        self.page = page
        self.limit = limit
        self.sortBy = sortBy
        self.sortOrder = sortOrder
    }
    
    var queryParameters: [String: Any] {
        var params: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let sortBy = sortBy {
            params["sortBy"] = sortBy
        }
        
        if let sortOrder = sortOrder {
            params["sortOrder"] = sortOrder.rawValue
        }
        

        
        return params
    }
}

// MARK: - Search Request
struct SearchRequest: Codable {
    let query: String
    let category: String?
    let pagination: PaginationRequest
    
    init(
        query: String,
        category: String? = nil,
        pagination: PaginationRequest = PaginationRequest()
    ) {
        self.query = query
        self.category = category
        self.pagination = pagination
    }
    
    var queryParameters: [String: Any] {
        var params = pagination.queryParameters
        params["q"] = query

        if let category = category {
            params["category"] = category
        }

        return params
    }
}

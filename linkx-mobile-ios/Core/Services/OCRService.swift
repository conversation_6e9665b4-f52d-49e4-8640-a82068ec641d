//
//  OCRService.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 22/7/25.
//

import Foundation
import UIKit

class OCRService: ObservableObject {
    static let shared = OCRService()
    private let apiClient = APIClient.shared

    // Demo mode flag - set to false to use real API
    private let isDemoMode = false

    private init() {}
    
    // MARK: - Public Methods
    
    /// Process receipt image using OCR
    func processReceipt(image: UIImage) async throws -> ReceiptData {
        Logger.shared.info("Processing receipt image", category: .network)

        // Demo mode - return mock data for testing
        if isDemoMode {
            return try await processDemoReceipt(image: image)
        }

        // Resize image if needed to handle "Image too large" error
        let resizedImage = resizeImageIfNeeded(image, maxDimension: 3000)

        // Convert image to JPEG data for multipart upload
        guard let imageData = resizedImage.jpegData(compressionQuality: 0.7) else {
            throw OCRError.imageProcessingFailed
        }

        do {
            Logger.shared.debug("Sending OCR request to: \(APIEndpoints.OCR.processReceipt)", category: .network)
            Logger.shared.debug("Image data size: \(imageData.count) bytes", category: .network)

            // Use multipart form data upload instead of JSON
            let response: ReceiptProcessingResultDto = try await apiClient.uploadMultipart(
                endpoint: APIEndpoints.OCR.processReceipt,
                fileData: imageData,
                fileName: "receipt.jpg",
                fieldName: "receipt",
                mimeType: "image/jpeg",
                additionalFields: [:],
                responseType: ReceiptProcessingResultDto.self
            )

            Logger.shared.info("Receipt processed: success=\(response.success), status=\(response.status ?? "nil"), receiptId=\(response.receiptId ?? "nil"), tokensEarned=\(response.tokensEarned ?? 0)", category: .network)

            // Check if there are errors or failed status
            if let errors = response.errors, !errors.isEmpty {
                Logger.shared.error("OCR processing failed with errors: \(errors.joined(separator: ", "))", category: .network)
                throw NetworkError.serverError(400, "OCR processing failed: \(errors.first ?? "Unknown error")")
            }

            if response.status == "FAILED" {
                Logger.shared.error("OCR processing failed with status: FAILED", category: .network)
                throw NetworkError.serverError(400, "OCR processing failed")
            }

            return mapProcessingResultToReceiptData(response)

        } catch {
            Logger.shared.error("Failed to process receipt: \(error)", category: .network)

            // Log more details about the error
            if let networkError = error as? NetworkError {
                switch networkError {
                case .serverError(let code, let message):
                    Logger.shared.error("Server error: code=\(code), message=\(message ?? "nil")", category: .network)
                case .networkUnavailable:
                    Logger.shared.error("Network unavailable", category: .network)
                case .decodingError(let underlying):
                    Logger.shared.error("Decoding error: \(underlying)", category: .network)
                case .invalidURL:
                    Logger.shared.error("Invalid URL", category: .network)
                case .noData:
                    Logger.shared.error("No data received", category: .network)
                case .unauthorized:
                    Logger.shared.error("Unauthorized - check JWT token", category: .network)
                case .unknown(let underlying):
                    Logger.shared.error("Unknown error: \(underlying)", category: .network)
                }
            }

            throw error
        }
    }
    
    /// Process receipt from URL
    func processReceiptFromURL(url: String) async throws -> ReceiptData {
        Logger.shared.info("Processing receipt from URL: \(url)", category: .network)
        
        let request = ProcessReceiptURLRequest(imageUrl: url)
        
        do {
            let response: ProcessReceiptResponse = try await apiClient.request(
                endpoint: APIEndpoints.OCR.processReceiptUrl,
                method: .POST,
                parameters: try request.asDictionary(),
                responseType: ProcessReceiptResponse.self
            )
            
            Logger.shared.info("Receipt from URL processed successfully", category: .network)
            return mapToReceiptData(response)
            
        } catch {
            Logger.shared.error("Failed to process receipt from URL: \(error)", category: .network)
            throw error
        }
    }
    
    /// Get OCR processing history
    func getProcessingHistory(limit: Int = 20, offset: Int = 0) async throws -> [OCRHistoryItem] {
        Logger.shared.info("Fetching OCR history", category: .network)
        
        let queryParams = [
            "limit": "\(limit)",
            "offset": "\(offset)"
        ]
        
        do {
            let response: OCRHistoryResponse = try await apiClient.request(
                endpoint: APIEndpoints.OCR.history + "?" + queryParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&"),
                method: .GET,
                responseType: OCRHistoryResponse.self
            )
            
            Logger.shared.info("OCR history fetched successfully", category: .network)
            return response.history
            
        } catch {
            Logger.shared.error("Failed to fetch OCR history: \(error)", category: .network)
            throw error
        }
    }
    
    /// Get OCR statistics
    func getOCRStats() async throws -> OCRStats {
        Logger.shared.info("Fetching OCR statistics", category: .network)
        
        do {
            let response: OCRStatsResponse = try await apiClient.request(
                endpoint: APIEndpoints.OCR.stats,
                method: .GET,
                responseType: OCRStatsResponse.self
            )
            
            Logger.shared.info("OCR stats fetched successfully", category: .network)
            return response.stats
            
        } catch {
            Logger.shared.error("Failed to fetch OCR stats: \(error)", category: .network)
            throw error
        }
    }

    // MARK: - Private Methods

    /// Resize image if it exceeds maximum dimensions
    private func resizeImageIfNeeded(_ image: UIImage, maxDimension: CGFloat) -> UIImage {
        let size = image.size

        // Check if resizing is needed
        if size.width <= maxDimension && size.height <= maxDimension {
            return image
        }

        // Calculate new size maintaining aspect ratio
        let aspectRatio = size.width / size.height
        var newSize: CGSize

        if size.width > size.height {
            newSize = CGSize(width: maxDimension, height: maxDimension / aspectRatio)
        } else {
            newSize = CGSize(width: maxDimension * aspectRatio, height: maxDimension)
        }

        // Resize the image
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        Logger.shared.debug("Resized image from \(size) to \(newSize)", category: .network)

        return resizedImage ?? image
    }

    // TODO: Implement image resizing to handle "Image too large" error
    /*
    private func resizeImageIfNeeded(_ image: UIImage, maxDimension: CGFloat) -> UIImage {
        let size = image.size

        // Check if image needs resizing
        if size.width <= maxDimension && size.height <= maxDimension {
            return image
        }

        // Calculate new size maintaining aspect ratio
        let aspectRatio = size.width / size.height
        var newSize: CGSize

        if size.width > size.height {
            newSize = CGSize(width: maxDimension, height: maxDimension / aspectRatio)
        } else {
            newSize = CGSize(width: maxDimension * aspectRatio, height: maxDimension)
        }

        // Resize image using UIGraphicsImageRenderer (iOS 10+)
        let renderer = UIGraphicsImageRenderer(size: newSize)
        let resizedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }

        Logger.shared.debug("Image resized from \(size) to \(newSize)", category: .network)

        return resizedImage
    }
    */

    private func processDemoReceipt(image: UIImage) async throws -> ReceiptData {
        Logger.shared.info("Processing receipt in demo mode", category: .network)

        // Simulate processing delay
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        // Generate random receipt data for demo
        let merchants = ["Starbucks", "McDonald's", "Subway", "KFC", "Pizza Hut", "Burger King", "Taco Bell"]
        let randomMerchant = merchants.randomElement() ?? "Demo Store"

        let items = generateRandomItems()
        let subtotal = items.reduce(0) { $0 + $1.totalPrice }
        let tax = subtotal * 0.08 // 8% tax
        let total = subtotal + tax

        let receiptData = ReceiptData(
            id: "demo-\(UUID().uuidString)",
            merchantName: randomMerchant,
            merchantAddress: "123 Demo Street, Demo City, DC 12345",
            date: Date(),
            totalAmount: total,
            subtotal: subtotal,
            taxAmount: tax,
            items: items,
            paymentMethod: ["Credit Card", "Debit Card", "Cash"].randomElement() ?? "Credit Card",
            receiptNumber: "RCP-\(Int.random(in: 1000...9999))",
            confidence: Double.random(in: 0.85...0.98),
            estimatedTokens: total * 0.0001 // 0.01% of VND amount as tokens
        )

        Logger.shared.info("Demo receipt processed successfully", category: .network)
        return receiptData
    }

    private func generateRandomItems() -> [ReceiptItem] {
        let itemTemplates = [
            ("Coffee", 2.50...5.00),
            ("Sandwich", 6.00...12.00),
            ("Salad", 8.00...15.00),
            ("Burger", 8.00...16.00),
            ("Pizza Slice", 3.00...6.00),
            ("Soda", 1.50...3.00),
            ("Water", 1.00...2.50),
            ("Fries", 2.00...4.00),
            ("Dessert", 4.00...8.00),
            ("Soup", 5.00...10.00)
        ]

        let numberOfItems = Int.random(in: 2...6)
        var items: [ReceiptItem] = []

        for _ in 0..<numberOfItems {
            let template = itemTemplates.randomElement()!
            let price = Double.random(in: template.1)
            let quantity = Int.random(in: 1...3)

            items.append(ReceiptItem(
                name: template.0,
                quantity: quantity,
                unitPrice: price,
                totalPrice: price * Double(quantity)
            ))
        }

        return items
    }

    private func mapProcessingResultToReceiptData(_ response: ReceiptProcessingResultDto) -> ReceiptData {
        let items = response.ocrResult?.items?.map { item in
            ReceiptItem(
                name: item.name,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice ?? 0.0
            )
        } ?? []

        // Parse date string to Date if available
        var receiptDate: Date?
        if let dateString = response.ocrResult?.date {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            receiptDate = formatter.date(from: dateString)
        }

        return ReceiptData(
            id: response.receiptId ?? "unknown-\(UUID().uuidString)",
            merchantName: response.ocrResult?.storeName ?? "Unknown Merchant",
            merchantAddress: response.ocrResult?.storeAddress,
            date: receiptDate ?? Date(),
            totalAmount: response.ocrResult?.totalAmount ?? 0.0,
            subtotal: response.ocrResult?.subtotal,
            taxAmount: response.ocrResult?.tax,
            items: items,
            paymentMethod: nil,
            receiptNumber: response.receiptId ?? "unknown",
            confidence: response.ocrResult?.confidence ?? 0.0,
            estimatedTokens: response.tokensEarned ?? 0.0
        )
    }

    private func mapToReceiptData(_ response: ProcessReceiptResponse) -> ReceiptData {
        let items = response.items?.map { item in
            ReceiptItem(
                name: item.name,
                quantity: item.quantity,
                unitPrice: item.price,
                totalPrice: (item.price ?? 0.0) * Double(item.quantity ?? 1)
            )
        } ?? []

        return ReceiptData(
            id: response.id,
            merchantName: response.merchantName ?? "Unknown Merchant",
            merchantAddress: nil,
            date: response.date ?? Date(),
            totalAmount: response.totalAmount ?? 0.0,
            subtotal: nil,
            taxAmount: nil,
            items: items,
            paymentMethod: nil,
            receiptNumber: nil,
            confidence: response.confidence ?? 0.0,
            estimatedTokens: (response.totalAmount ?? 0.0) * 0.0001
        )
    }
}

// MARK: - Request Models

struct ProcessReceiptRequest: Codable {
    let image: String // Base64 encoded image
    let imageFormat: String // "jpeg", "png", etc.

    func asDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 0, userInfo: nil)
        }
        return dictionary
    }
}

struct ProcessReceiptURLRequest: Codable {
    let imageUrl: String
}

// MARK: - Response Models

struct ProcessReceiptResponse: Codable {
    let id: String
    let merchantName: String?
    let totalAmount: Double?
    let date: Date?
    let items: [ProcessedReceiptItem]?
    let rawText: String?
    let confidence: Double?
    let processingTime: Double?
    let status: String
    let createdAt: Date
}

struct ProcessedReceiptItem: Codable {
    let name: String
    let quantity: Int?
    let price: Double?
    let category: String?
}

struct OCRHistoryResponse: Codable {
    let history: [OCRHistoryItem]
    let total: Int
    let limit: Int
    let offset: Int
}

struct OCRHistoryItem: Codable {
    let id: String
    let merchantName: String?
    let totalAmount: Double?
    let status: String
    let confidence: Double?
    let processingTime: Double?
    let createdAt: Date
}

struct OCRStatsResponse: Codable {
    let stats: OCRStats
}

struct OCRStats: Codable {
    let totalProcessed: Int
    let successfulProcessed: Int
    let averageConfidence: Double
    let averageProcessingTime: Double
    let topMerchants: [String]
    let monthlyStats: [MonthlyOCRStat]
}

struct MonthlyOCRStat: Codable {
    let month: String
    let count: Int
    let totalAmount: Double?
}

// MARK: - OpenAPI Spec Models
struct ReceiptProcessingResultDto: Codable {
    let success: Bool
    let receiptId: String?
    let transactionId: String?
    let tokensEarned: Double?
    let blockchainTxHash: String?
    let ocrResult: OCRResultDto?
    let validationResult: ValidationResultDto?
    let tokenCalculation: TokenCalculationDto?
    let errors: [String]?
    let warnings: [String]?
    let status: String?
}

struct OCRResultDto: Codable {
    let storeName: String?
    let storeAddress: String?
    let totalAmount: Double?
    let subtotal: Double?
    let tax: Double?
    let date: String?
    let time: String?
    let items: [OCRResultItemDto]?
    let confidence: Double?
    let provider: String?
}

struct OCRResultItemDto: Codable {
    let name: String
    let quantity: Int?
    let unitPrice: Double?
    let totalPrice: Double?
}

struct ValidationResultDto: Codable {
    let isValid: Bool
    let errors: [String]?
    let warnings: [String]?
}

struct TokenCalculationDto: Codable {
    let baseAmount: Double
    let bonusAmount: Double
    let totalTokens: Double
    let exchangeRate: Double
    let tierMultiplier: Double
    let merchantBonus: Double?
}

// MARK: - Error Types

enum OCRError: LocalizedError {
    case imageProcessingFailed
    case invalidImageFormat
    case processingTimeout
    case lowConfidence
    case networkError(String)
    
    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed:
            return "Failed to process the image. Please try again."
        case .invalidImageFormat:
            return "Invalid image format. Please use JPEG or PNG."
        case .processingTimeout:
            return "Processing timeout. Please try again."
        case .lowConfidence:
            return "Unable to read receipt clearly. Please try taking another photo."
        case .networkError(let message):
            return "Network error: \(message)"
        }
    }
}

// MARK: - Codable Extension
extension Encodable {
    func asDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to convert to dictionary"])
        }
        return dictionary
    }
}

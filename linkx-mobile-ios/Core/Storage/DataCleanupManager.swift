//
//  DataCleanupManager.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import Foundation

// MARK: - Data Cleanup Manager
class DataCleanupManager {
    static let shared = DataCleanupManager()
    
    private let tokenManager = TokenManager.shared
    private let keychainManager = KeychainManager.shared
    
    private init() {}
    
    // MARK: - Complete Data Cleanup
    
    /// Clears ALL user-related data from the app
    /// Use this for complete logout or when switching users
    func clearAllUserData() {
        print("🧹 DataCleanupManager: Starting complete user data cleanup")
        
        // Clear UserDefaults
        clearUserDefaults()
        
        // Clear Keychain
        clearKeychain()
        
        // Clear TokenManager (which uses UserDefaults)
        tokenManager.clearToken()
        
        print("🧹 DataCleanupManager: Complete user data cleanup finished")
    }
    
    /// Clears authentication data only (keeps user preferences)
    /// Use this for logout while preserving app settings
    func clearAuthenticationData() {
        print("🧹 DataCleanupManager: Starting authentication data cleanup")
        
        // Clear auth-related UserDefaults
        clearAuthUserDefaults()
        
        // Clear auth-related Keychain
        clearAuthKeychain()
        
        // Clear TokenManager
        tokenManager.clearToken()
        
        print("🧹 DataCleanupManager: Authentication data cleanup finished")
    }
    
    /// Clears cached user data but keeps authentication tokens
    /// Use this before fresh login to ensure clean user data
    func clearCachedUserData() {
        print("🧹 DataCleanupManager: Starting cached user data cleanup")
        
        // Only clear user data, keep tokens
        UserDefaults.standard.removeObject(forKey: "linkx_current_user")
        
        print("🧹 DataCleanupManager: Cached user data cleanup finished")
    }
    
    // MARK: - UserDefaults Cleanup
    
    private func clearUserDefaults() {
        let keysToRemove = [
            // Authentication
            "linkx_access_token",
            "linkx_current_user",
            "saved_email",
            
            // Biometric/Security
            "biometric_enabled",
            
            // Notifications
            "notification_settings",
            
            // App preferences (keep these for user experience)
            // "app_theme", "language_preference" - keep these
        ]
        
        for key in keysToRemove {
            UserDefaults.standard.removeObject(forKey: key)
            print("🧹 Removed UserDefaults key: \(key)")
        }
    }
    
    private func clearAuthUserDefaults() {
        let authKeysToRemove = [
            "linkx_access_token",
            "linkx_current_user",
            "saved_email"
        ]
        
        for key in authKeysToRemove {
            UserDefaults.standard.removeObject(forKey: key)
            print("🧹 Removed auth UserDefaults key: \(key)")
        }
    }
    
    // MARK: - Keychain Cleanup
    
    private func clearKeychain() {
        do {
            try keychainManager.clearAll()
            print("🧹 Cleared all Keychain data")
        } catch {
            print("🧹 Error clearing Keychain: \(error)")
        }
        
        // Also clear any additional keys that might not be in clearAll()
        let additionalKeys = [
            "saved_credentials",
            "linkx_access_token" // Sometimes stored in keychain too
        ]
        
        for key in additionalKeys {
            try? keychainManager.delete(key: key)
            print("🧹 Attempted to remove Keychain key: \(key)")
        }
    }
    
    private func clearAuthKeychain() {
        let authKeysToRemove = [
            AppConstants.KeychainKeys.accessToken,
            AppConstants.KeychainKeys.refreshToken,
            "saved_credentials",
            "linkx_access_token"
        ]
        
        for key in authKeysToRemove {
            try? keychainManager.delete(key: key)
            print("🧹 Removed auth Keychain key: \(key)")
        }
    }
    
    // MARK: - Verification Methods
    
    /// Verifies that all user data has been cleared
    func verifyDataCleared() -> Bool {
        // Check UserDefaults
        let hasUserDefaults = UserDefaults.standard.string(forKey: "linkx_access_token") != nil ||
                             UserDefaults.standard.data(forKey: "linkx_current_user") != nil
        
        // Check TokenManager
        let hasToken = tokenManager.isLoggedIn
        
        // Check Keychain (basic check)
        let hasKeychainToken = (try? keychainManager.loadAccessToken()) != nil
        
        let isCleared = !hasUserDefaults && !hasToken && !hasKeychainToken
        
        print("🧹 Data verification - UserDefaults: \(!hasUserDefaults), Token: \(!hasToken), Keychain: \(!hasKeychainToken)")
        print("🧹 Overall data cleared: \(isCleared)")
        
        return isCleared
    }
    
    /// Logs current data state for debugging
    func logCurrentDataState() {
        print("🧹 === Current Data State ===")
        
        // UserDefaults
        let hasUserDefaultsToken = UserDefaults.standard.string(forKey: "linkx_access_token") != nil
        let hasUserDefaultsUser = UserDefaults.standard.data(forKey: "linkx_current_user") != nil
        let hasSavedEmail = UserDefaults.standard.string(forKey: "saved_email") != nil
        
        print("🧹 UserDefaults - Token: \(hasUserDefaultsToken), User: \(hasUserDefaultsUser), Email: \(hasSavedEmail)")
        
        // TokenManager
        let hasTokenManagerToken = tokenManager.isLoggedIn
        let tokenManagerUser = tokenManager.getUser(User.self)
        
        print("🧹 TokenManager - Token: \(hasTokenManagerToken), User: \(tokenManagerUser != nil)")
        
        // Keychain
        let hasKeychainToken = (try? keychainManager.loadAccessToken()) != nil
        let hasKeychainRefresh = (try? keychainManager.loadRefreshToken()) != nil
        
        print("🧹 Keychain - Access Token: \(hasKeychainToken), Refresh Token: \(hasKeychainRefresh)")
        
        print("🧹 === End Data State ===")
    }
}

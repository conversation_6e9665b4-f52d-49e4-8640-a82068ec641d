//
//  KeychainManager.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Security
import LocalAuthentication

// MARK: - Keychain Error
enum KeychainError: Error, LocalizedError {
    case itemNotFound
    case duplicateItem
    case invalidItemFormat
    case unexpectedPasswordData
    case unhandledError(status: OSStatus)
    case biometricNotAvailable
    case biometricNotEnrolled
    case biometricLockout
    case userCancel
    case authenticationFailed
    
    var errorDescription: String? {
        switch self {
        case .itemNotFound:
            return "Item not found in keychain"
        case .duplicateItem:
            return "Item already exists in keychain"
        case .invalidItemFormat:
            return "Invalid item format"
        case .unexpectedPasswordData:
            return "Unexpected password data"
        case .unhandledError(let status):
            return "Unhandled keychain error: \(status)"
        case .biometricNotAvailable:
            return "Biometric authentication not available"
        case .biometricNotEnrolled:
            return "No biometric data enrolled"
        case .biometricLockout:
            return "Biometric authentication locked out"
        case .userCancel:
            return "User cancelled authentication"
        case .authenticationFailed:
            return "Authentication failed"
        }
    }
}

// MARK: - Keychain Manager
class KeychainManager {
    static let shared = KeychainManager()
    
    private let service = AppConstants.KeychainKeys.service
    
    private init() {}
    
    // MARK: - Basic Keychain Operations
    
    func save(key: String, data: Data) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        // Delete existing item first
        SecItemDelete(query as CFDictionary)
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw KeychainError.unhandledError(status: status)
        }
    }
    
    func save(key: String, string: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidItemFormat
        }
        try save(key: key, data: data)
    }
    
    func load(key: String) throws -> Data {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                throw KeychainError.itemNotFound
            }
            throw KeychainError.unhandledError(status: status)
        }
        
        guard let data = result as? Data else {
            throw KeychainError.unexpectedPasswordData
        }
        
        return data
    }
    
    func loadString(key: String) throws -> String {
        let data = try load(key: key)
        guard let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.invalidItemFormat
        }
        return string
    }
    
    func delete(key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw KeychainError.unhandledError(status: status)
        }
    }
    
    func exists(key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: false
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    // MARK: - Biometric Protected Operations
    
    func saveBiometric(key: String, data: Data, reason: String = AppConstants.Biometric.reason) throws {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            if let error = error {
                switch error.code {
                case LAError.biometryNotAvailable.rawValue:
                    throw KeychainError.biometricNotAvailable
                case LAError.biometryNotEnrolled.rawValue:
                    throw KeychainError.biometricNotEnrolled
                default:
                    throw KeychainError.authenticationFailed
                }
            }
            throw KeychainError.biometricNotAvailable
        }
        
        let access = SecAccessControlCreateWithFlags(
            nil,
            kSecAttrAccessibleWhenUnlockedThisDeviceOnly,
            .biometryAny,
            nil
        )
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessControl as String: access as Any,
            kSecUseOperationPrompt as String: reason
        ]
        
        // Delete existing item first
        SecItemDelete(query as CFDictionary)
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw KeychainError.unhandledError(status: status)
        }
    }
    
    func saveBiometric(key: String, string: String, reason: String = AppConstants.Biometric.reason) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidItemFormat
        }
        try saveBiometric(key: key, data: data, reason: reason)
    }
    
    func loadBiometric(key: String, reason: String = AppConstants.Biometric.reason) async throws -> Data {
        let context = LAContext()
        context.localizedFallbackTitle = AppConstants.Biometric.fallbackTitle
        context.localizedCancelTitle = AppConstants.Biometric.cancelTitle
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne,
            kSecUseAuthenticationContext as String: context,
            kSecUseOperationPrompt as String: reason
        ]
        
        return try await withCheckedThrowingContinuation { continuation in
            var result: AnyObject?
            let status = SecItemCopyMatching(query as CFDictionary, &result)
            
            switch status {
            case errSecSuccess:
                guard let data = result as? Data else {
                    continuation.resume(throwing: KeychainError.unexpectedPasswordData)
                    return
                }
                continuation.resume(returning: data)
                
            case errSecItemNotFound:
                continuation.resume(throwing: KeychainError.itemNotFound)
                
            case errSecAuthFailed:
                continuation.resume(throwing: KeychainError.biometricLockout)
                
            default:
                continuation.resume(throwing: KeychainError.unhandledError(status: status))
            }
        }
    }
    
    func loadBiometricString(key: String, reason: String = AppConstants.Biometric.reason) async throws -> String {
        let data = try await loadBiometric(key: key, reason: reason)
        guard let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.invalidItemFormat
        }
        return string
    }
    
    // MARK: - Convenience Methods for App-Specific Keys
    
    func saveAccessToken(_ token: String) throws {
        try save(key: AppConstants.KeychainKeys.accessToken, string: token)
    }
    
    func loadAccessToken() throws -> String {
        return try loadString(key: AppConstants.KeychainKeys.accessToken)
    }
    
    func deleteAccessToken() throws {
        try delete(key: AppConstants.KeychainKeys.accessToken)
    }
    
    func saveRefreshToken(_ token: String) throws {
        try save(key: AppConstants.KeychainKeys.refreshToken, string: token)
    }
    
    func loadRefreshToken() throws -> String {
        return try loadString(key: AppConstants.KeychainKeys.refreshToken)
    }
    
    func deleteRefreshToken() throws {
        try delete(key: AppConstants.KeychainKeys.refreshToken)
    }
    
    func saveWalletPrivateKey(_ privateKey: String) throws {
        try saveBiometric(
            key: AppConstants.KeychainKeys.walletPrivateKey,
            string: privateKey,
            reason: "Access your wallet private key"
        )
    }
    
    func loadWalletPrivateKey() async throws -> String {
        return try await loadBiometricString(
            key: AppConstants.KeychainKeys.walletPrivateKey,
            reason: "Access your wallet private key"
        )
    }
    
    func deleteWalletPrivateKey() throws {
        try delete(key: AppConstants.KeychainKeys.walletPrivateKey)
    }
    
    // MARK: - Clear All Data
    
    func clearAll() throws {
        let keys = [
            AppConstants.KeychainKeys.accessToken,
            AppConstants.KeychainKeys.refreshToken,
            AppConstants.KeychainKeys.walletPrivateKey,
            AppConstants.KeychainKeys.biometricData
        ]
        
        for key in keys {
            try? delete(key: key)
        }
    }
    
    // MARK: - Biometric Availability Check
    
    func isBiometricAvailable() -> Bool {
        let context = LAContext()
        var error: NSError?
        return context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
    }
    
    func biometricType() -> LABiometryType {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            return .none
        }
        
        return context.biometryType
    }
    
    func biometricTypeString() -> String {
        switch biometricType() {
        case .faceID:
            return "Face ID"
        case .touchID:
            return "Touch ID"
        case .opticID:
            return "Optic ID"
        case .none:
            return "None"
        @unknown default:
            return "Unknown"
        }
    }
}

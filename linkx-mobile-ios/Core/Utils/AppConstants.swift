//
//  AppConstants.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import SwiftUI

struct AppConstants {
    
    // MARK: - API Configuration
    struct API {
        static let baseURL = "https://api.linkx.com"  // Base URL without version
        static let timeout: TimeInterval = 30.0
        static let retryAttempts = 3
        static let retryDelay: TimeInterval = 1.0

        // Endpoints (v1 prefixed)
        struct Endpoints {
            static let auth = "/v1/auth"
            static let users = "/v1/users"
            static let transactions = "/v1/transactions"
            static let rewards = "/v1/rewards"
            static let merchants = "/v1/merchants"
            static let redemptions = "/v1/rewards/user/redemptions"
            static let admin = "/v1/admin"
            static let ocr = "/v1/ocr"
        }
    }
    
    // MARK: - UI Constants
    struct UI {
        static let cornerRadius: CGFloat = 12
        static let shadowRadius: CGFloat = 4
        static let buttonHeight: CGFloat = 50
        static let screenPadding: CGFloat = 16
        static let sectionSpacing: CGFloat = 24
        static let itemSpacing: CGFloat = 16
        static let cardPadding: CGFloat = 16
        
        // Animation
        static let animationDuration: Double = 0.3
        static let springAnimation = Animation.spring(response: 0.5, dampingFraction: 0.8)
        static let easeInOutAnimation = Animation.easeInOut(duration: animationDuration)
    }
    
    // MARK: - Colors
    struct Colors {
        // Primary Colors
        static let primary = Color(red: 0.2, green: 0.6, blue: 1.0) // Blue
        static let primaryDark = Color(red: 0.1, green: 0.4, blue: 0.8)
        static let primaryLight = Color(red: 0.6, green: 0.8, blue: 1.0)
        
        // Secondary Colors
        static let secondary = Color(red: 1.0, green: 0.6, blue: 0.2) // Orange
        static let accent = Color(red: 0.8, green: 0.2, blue: 0.8) // Purple
        
        // Status Colors
        static let success = Color(red: 0.2, green: 0.8, blue: 0.4) // Green
        static let warning = Color(red: 1.0, green: 0.8, blue: 0.0) // Yellow
        static let error = Color(red: 1.0, green: 0.3, blue: 0.3) // Red
        static let info = Color(red: 0.3, green: 0.7, blue: 1.0) // Light Blue
        
        // Neutral Colors
        static let background = Color(UIColor.systemBackground)
        static let surface = Color(UIColor.secondarySystemBackground)
        static let surfaceSecondary = Color(UIColor.tertiarySystemBackground)
        static let border = Color(UIColor.separator)
        
        // Text Colors
        static let textPrimary = Color(UIColor.label)
        static let textSecondary = Color(UIColor.secondaryLabel)
        static let textTertiary = Color(UIColor.tertiaryLabel)
    }
    
    // MARK: - Typography
    struct Typography {
        static let largeTitle = Font.largeTitle.weight(.bold)
        static let title = Font.title.weight(.semibold)
        static let title2 = Font.title2.weight(.semibold)
        static let title3 = Font.title3.weight(.medium)
        static let headline = Font.headline.weight(.semibold)
        static let subheadline = Font.subheadline.weight(.medium)
        static let body = Font.body
        static let callout = Font.callout
        static let caption = Font.caption
        static let caption2 = Font.caption2
    }
    
    // MARK: - Validation
    struct Validation {
        static let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        static let phoneRegex = "^[+]?[0-9]{10,15}$"
        static let minPasswordLength = 8
        static let maxPasswordLength = 128
        static let minNameLength = 2
        static let maxNameLength = 50
    }
    
    // MARK: - Storage Keys
    struct StorageKeys {
        static let accessToken = "access_token"
        static let refreshToken = "refresh_token"
        static let userId = "user_id"
        static let biometricEnabled = "biometric_enabled"
        static let notificationsEnabled = "notifications_enabled"
        static let selectedLanguage = "selected_language"
        static let onboardingCompleted = "onboarding_completed"
        static let lastSyncDate = "last_sync_date"
    }
    
    // MARK: - Keychain Keys
    struct KeychainKeys {
        static let service = "com.linkx.mobile"
        static let accessToken = "access_token"
        static let refreshToken = "refresh_token"
        static let walletPrivateKey = "wallet_private_key"
        static let biometricData = "biometric_data"
    }
    
    // MARK: - Biometric
    struct Biometric {
        static let reason = "Authenticate to access your LinkX account"
        static let fallbackTitle = "Use Passcode"
        static let cancelTitle = "Cancel"
    }
    
    // MARK: - Transaction
    struct Transaction {
        static let exchangeRate: Double = 1000.0 // 1 LXT = 1000 VND
        static let minTransferAmount: Double = 1.0
        static let maxTransferAmount: Double = 10000.0
        static let networkFee: Double = 0.001 // SOL
    }
    
    // MARK: - Pagination
    struct Pagination {
        static let defaultLimit = 20
        static let maxLimit = 100
    }
    
    // MARK: - Cache
    struct Cache {
        static let maxAge: TimeInterval = 300 // 5 minutes
        static let maxSize = 50 * 1024 * 1024 // 50 MB
    }
    
    // MARK: - Haptic Types
    enum HapticType {
        case light
        case medium
        case heavy
        case success
        case warning
        case error
    }
    
    // MARK: - App Information
    struct AppInfo {
        static let name = "LinkX"
        static let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
        static let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        static let bundleId = Bundle.main.bundleIdentifier ?? "com.linkx.mobile"
        
        static var fullVersion: String {
            return "\(version) (\(build))"
        }
    }
    
    // MARK: - Feature Flags
    struct FeatureFlags {
        static let biometricLoginEnabled = true
        static let socialLoginEnabled = false
        static let darkModeEnabled = true
        static let analyticsEnabled = true
        static let crashReportingEnabled = true
        static let debugMenuEnabled = false
        
        #if DEBUG
        static let loggingEnabled = true
        static let networkLoggingEnabled = true
        #else
        static let loggingEnabled = false
        static let networkLoggingEnabled = false
        #endif
    }
    
    // MARK: - URLs
    struct URLs {
        static let website = "https://linkx.com"
        static let support = "https://support.linkx.com"
        static let termsOfService = "https://linkx.com/terms"
        static let privacyPolicy = "https://linkx.com/privacy"
        static let appStore = "https://apps.apple.com/app/linkx"
        static let solanaExplorer = "https://explorer.solana.com"
    }
    
    // MARK: - Contact Information
    struct Contact {
        static let supportEmail = "<EMAIL>"
        static let supportPhone = "+***********"
        static let feedbackEmail = "<EMAIL>"
        static let businessEmail = "<EMAIL>"
    }
    
    // MARK: - Social Media
    struct SocialMedia {
        static let facebook = "https://facebook.com/linkx"
        static let twitter = "https://twitter.com/linkx"
        static let instagram = "https://instagram.com/linkx"
        static let linkedin = "https://linkedin.com/company/linkx"
    }
    
    // MARK: - Notification Types
    enum NotificationType: String, CaseIterable, Codable {
        case tokenEarned = "token_earned"
        case rewardRedeemed = "reward_redeemed"
        case transactionCompleted = "transaction_completed"
        case promotionalOffer = "promotional_offer"
        case systemUpdate = "system_update"
        case securityAlert = "security_alert"
        
        var displayName: String {
            switch self {
            case .tokenEarned:
                return "Token Earned"
            case .rewardRedeemed:
                return "Reward Redeemed"
            case .transactionCompleted:
                return "Transaction Completed"
            case .promotionalOffer:
                return "Promotional Offer"
            case .systemUpdate:
                return "System Update"
            case .securityAlert:
                return "Security Alert"
            }
        }
        
        var icon: String {
            switch self {
            case .tokenEarned:
                return "plus.circle.fill"
            case .rewardRedeemed:
                return "gift.fill"
            case .transactionCompleted:
                return "checkmark.circle.fill"
            case .promotionalOffer:
                return "megaphone.fill"
            case .systemUpdate:
                return "arrow.clockwise.circle.fill"
            case .securityAlert:
                return "shield.fill"
            }
        }
    }
    
    // MARK: - Environment
    enum Environment {
        case development
        case staging
        case production
        
        static var current: Environment {
            #if DEBUG
            return .development
            #elseif STAGING
            return .staging
            #else
            return .production
            #endif
        }
        
        var apiBaseURL: String {
            switch self {
            case .development:
                return "https://linkx-api.earnbase.io/api"  // Base URL includes /api prefix
            case .staging:
                return "https://linkx-api.earnbase.io/api"
            case .production:
                return "https://linkx-api.earnbase.io/api"
            }
        }
        
        var displayName: String {
            switch self {
            case .development:
                return "Development"
            case .staging:
                return "Staging"
            case .production:
                return "Production"
            }
        }
    }
}

// MARK: - Extensions for easier access
extension AppConstants.Colors {
    static var allColors: [Color] {
        return [primary, secondary, accent, success, warning, error, info]
    }
}

extension AppConstants.UI {
    static var defaultPadding: EdgeInsets {
        return EdgeInsets(top: itemSpacing, leading: screenPadding, bottom: itemSpacing, trailing: screenPadding)
    }
}

extension AppConstants.FeatureFlags {
    static func isEnabled(_ flag: String) -> Bool {
        // This could be extended to read from remote config
        switch flag {
        case "biometric_login":
            return biometricLoginEnabled
        case "social_login":
            return socialLoginEnabled
        case "dark_mode":
            return darkModeEnabled
        case "analytics":
            return analyticsEnabled
        case "crash_reporting":
            return crashReportingEnabled
        case "debug_menu":
            return debugMenuEnabled
        default:
            return false
        }
    }
}

//
//  Logger.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import os.log

// MARK: - Log Level
enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"
    
    var emoji: String {
        switch self {
        case .debug:
            return "🔍"
        case .info:
            return "ℹ️"
        case .warning:
            return "⚠️"
        case .error:
            return "❌"
        case .critical:
            return "🚨"
        }
    }
    
    var osLogType: OSLogType {
        switch self {
        case .debug:
            return .debug
        case .info:
            return .info
        case .warning:
            return .default
        case .error:
            return .error
        case .critical:
            return .fault
        }
    }
}

// MARK: - Log Category
enum LogCategory: String, CaseIterable {
    case network = "Network"
    case authentication = "Authentication"
    case transaction = "Transaction"
    case ui = "UI"
    case storage = "Storage"
    case security = "Security"
    case general = "General"
    
    var subsystem: String {
        return "com.linkx.mobile"
    }
}

// MARK: - Logger
class Logger {
    static let shared = Logger()
    
    private let dateFormatter: DateFormatter
    private var loggers: [LogCategory: os.Logger] = [:]
    
    #if DEBUG
    private let isLoggingEnabled = true
    #else
    private let isLoggingEnabled = false
    #endif
    
    private init() {
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        
        // Initialize OS loggers for each category
        for category in LogCategory.allCases {
            loggers[category] = os.Logger(subsystem: category.subsystem, category: category.rawValue)
        }
    }
    
    // MARK: - Public Logging Methods
    func debug(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .debug, message: message, category: category, file: file, function: function, line: line)
    }
    
    func info(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .info, message: message, category: category, file: file, function: function, line: line)
    }
    
    func warning(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .warning, message: message, category: category, file: file, function: function, line: line)
    }
    
    func error(_ message: String, error: Error? = nil, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        var fullMessage = message
        if let error = error {
            fullMessage += " | Error: \(error.localizedDescription)"
        }
        log(level: .error, message: fullMessage, category: category, file: file, function: function, line: line)
    }
    
    func critical(_ message: String, error: Error? = nil, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        var fullMessage = message
        if let error = error {
            fullMessage += " | Error: \(error.localizedDescription)"
        }
        log(level: .critical, message: fullMessage, category: category, file: file, function: function, line: line)
    }
    
    // MARK: - Specialized Logging Methods
    func logNetworkRequest(url: String, method: String, headers: [String: String]? = nil) {
        var message = "🌐 Network Request: \(method) \(url)"
        if let headers = headers, !headers.isEmpty {
            message += " | Headers: \(headers)"
        }
        info(message, category: .network)
    }
    
    func logNetworkResponse(url: String, statusCode: Int, responseTime: TimeInterval) {
        let message = "🌐 Network Response: \(statusCode) \(url) | Time: \(String(format: "%.3f", responseTime))s"
        if statusCode >= 200 && statusCode < 300 {
            info(message, category: .network)
        } else {
            warning(message, category: .network)
        }
    }
    
    func logNetworkError(url: String, error: Error) {
        let message = "🌐 Network Error: \(url)"
        self.error(message, error: error, category: .network)
    }
    
    func logUserAction(_ action: String, details: [String: Any]? = nil) {
        var message = "👤 User Action: \(action)"
        if let details = details {
            message += " | Details: \(details)"
        }
        info(message, category: .ui)
    }
    
    func logTransaction(_ type: String, amount: Double, status: String) {
        let message = "💰 Transaction: \(type) | Amount: \(amount) | Status: \(status)"
        info(message, category: .transaction)
    }
    
    func logAuthentication(_ event: String, success: Bool) {
        let message = "🔐 Auth: \(event) | Success: \(success)"
        if success {
            info(message, category: .authentication)
        } else {
            warning(message, category: .authentication)
        }
    }
    
    func logSecurityEvent(_ event: String, details: [String: Any]? = nil) {
        var message = "🔒 Security: \(event)"
        if let details = details {
            message += " | Details: \(details)"
        }
        warning(message, category: .security)
    }
    
    // MARK: - Private Methods
    private func log(level: LogLevel, message: String, category: LogCategory, file: String, function: String, line: Int) {
        guard isLoggingEnabled else { return }
        
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        let timestamp = dateFormatter.string(from: Date())
        let logMessage = "\(level.emoji) [\(level.rawValue)] \(timestamp) | \(fileName):\(line) \(function) | \(message)"
        
        // Console logging
        print(logMessage)
        
        // OS logging
        if let osLogger = loggers[category] {
            osLogger.log(level: level.osLogType, "\(message, privacy: .public)")
        }
        
        // File logging (in production)
        #if !DEBUG
        writeToFile(logMessage)
        #endif
    }
    
    private func writeToFile(_ message: String) {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let logFileURL = documentsPath.appendingPathComponent("linkx_logs.txt")
        let logEntry = message + "\n"
        
        if FileManager.default.fileExists(atPath: logFileURL.path) {
            if let fileHandle = try? FileHandle(forWritingTo: logFileURL) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(logEntry.data(using: .utf8) ?? Data())
                fileHandle.closeFile()
            }
        } else {
            try? logEntry.write(to: logFileURL, atomically: true, encoding: .utf8)
        }
    }
    
    // MARK: - Log Management
    func clearLogs() {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let logFileURL = documentsPath.appendingPathComponent("linkx_logs.txt")
        try? FileManager.default.removeItem(at: logFileURL)
        
        info("Logs cleared", category: .general)
    }
    
    func getLogFileURL() -> URL? {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        
        let logFileURL = documentsPath.appendingPathComponent("linkx_logs.txt")
        return FileManager.default.fileExists(atPath: logFileURL.path) ? logFileURL : nil
    }
    
    func getLogFileSize() -> String {
        guard let logFileURL = getLogFileURL() else {
            return "0 KB"
        }
        
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: logFileURL.path)
            if let fileSize = attributes[.size] as? Int64 {
                return ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
            }
        } catch let logError {
            Logger.shared.error("Failed to get log file size", error: logError, category: .general)
        }
        
        return "Unknown"
    }
}

// MARK: - Global Logging Functions
func logDebug(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.debug(message, category: category, file: file, function: function, line: line)
}

func logInfo(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.info(message, category: category, file: file, function: function, line: line)
}

func logWarning(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.warning(message, category: category, file: file, function: function, line: line)
}

func logError(_ message: String, error: Error? = nil, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.error(message, error: error, category: category, file: file, function: function, line: line)
}

func logCritical(_ message: String, error: Error? = nil, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.critical(message, error: error, category: category, file: file, function: function, line: line)
}

// MARK: - Performance Measurement
class PerformanceTimer {
    private let startTime: CFAbsoluteTime
    private let operation: String
    private let category: LogCategory
    
    init(operation: String, category: LogCategory = .general) {
        self.operation = operation
        self.category = category
        self.startTime = CFAbsoluteTimeGetCurrent()
        Logger.shared.debug("⏱️ Started: \(operation)", category: category)
    }
    
    func finish() {
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        Logger.shared.debug("⏱️ Finished: \(operation) in \(String(format: "%.3f", timeElapsed))s", category: category)
    }
}

// MARK: - Performance Measurement Helper
func measurePerformance<T>(operation: String, category: LogCategory = .general, block: () throws -> T) rethrows -> T {
    let timer = PerformanceTimer(operation: operation, category: category)
    defer { timer.finish() }
    return try block()
}

func measurePerformanceAsync<T>(operation: String, category: LogCategory = .general, block: () async throws -> T) async rethrows -> T {
    let timer = PerformanceTimer(operation: operation, category: category)
    defer { timer.finish() }
    return try await block()
}

//
//  Merchant.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation

// MARK: - Merchant Model
struct Merchant: Codable, Identifiable {
    let id: String
    let name: String
    let businessId: String
    let email: String
    let phone: String?
    let category: MerchantCategory
    let status: MerchantStatus
    let commissionRate: Double
    let minimumCommission: Double?
    let maximumCommission: Double?
    let description: String?
    let website: String?
    let logoUrl: String?
    let address: MerchantAddress?
    let operatingHours: [OperatingHour]?
    let socialMedia: SocialMedia?
    let walletAddress: String?
    let apiKey: String?
    let webhookUrl: String?
    let totalTransactions: Int
    let totalCommissionPaid: Double
    let totalSettlementReceived: Double
    let metadata: [String: AnyCodable]?
    let notes: String?
    let lastTransactionAt: Date?
    let onboardedAt: Date?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var displayCommissionRate: String {
        return String(format: "%.1f%%", commissionRate * 100)
    }
    
    var isActive: Bool {
        return status == .active
    }
    
    var displayAddress: String {
        guard let address = address else { return "No address" }
        var components: [String] = []
        
        if let street = address.street { components.append(street) }
        if let city = address.city { components.append(city) }
        if let state = address.state { components.append(state) }
        
        return components.joined(separator: ", ")
    }
}

// MARK: - Merchant Category
enum MerchantCategory: String, Codable, CaseIterable {
    case restaurant = "RESTAURANT"
    case retail = "RETAIL"
    case entertainment = "ENTERTAINMENT"
    case services = "SERVICES"
    case ecommerce = "ECOMMERCE"
    case other = "OTHER"
    
    var displayName: String {
        switch self {
        case .restaurant:
            return "Restaurant"
        case .retail:
            return "Retail"
        case .entertainment:
            return "Entertainment"
        case .services:
            return "Services"
        case .ecommerce:
            return "E-commerce"
        case .other:
            return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .restaurant:
            return "fork.knife"
        case .retail:
            return "bag"
        case .entertainment:
            return "gamecontroller"
        case .services:
            return "wrench.and.screwdriver"
        case .ecommerce:
            return "cart"
        case .other:
            return "building.2"
        }
    }
}

// MARK: - Merchant Status
enum MerchantStatus: String, Codable, CaseIterable {
    case pending = "PENDING"
    case active = "ACTIVE"
    case suspended = "SUSPENDED"
    case inactive = "INACTIVE"
    
    var displayName: String {
        switch self {
        case .pending:
            return "Pending"
        case .active:
            return "Active"
        case .suspended:
            return "Suspended"
        case .inactive:
            return "Inactive"
        }
    }
    
    var color: String {
        switch self {
        case .pending:
            return "orange"
        case .active:
            return "green"
        case .suspended:
            return "red"
        case .inactive:
            return "gray"
        }
    }
}

// MARK: - Merchant Address
struct MerchantAddress: Codable {
    let street: String?
    let city: String?
    let state: String?
    let zipCode: String?
    let country: String?
    let latitude: Double?
    let longitude: Double?
}

// MARK: - Operating Hours
struct OperatingHour: Codable {
    let dayOfWeek: Int // 0 = Sunday, 1 = Monday, etc.
    let openTime: String // "09:00"
    let closeTime: String // "22:00"
    let isClosed: Bool
    
    var dayName: String {
        let days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        return days[dayOfWeek]
    }
    
    var displayTime: String {
        if isClosed {
            return "Closed"
        }
        return "\(openTime) - \(closeTime)"
    }
}

// MARK: - Social Media
struct SocialMedia: Codable {
    let facebook: String?
    let instagram: String?
    let twitter: String?
    let linkedin: String?
    let youtube: String?
    let tiktok: String?
}

// MARK: - Merchant List Response
struct MerchantListResponse: Codable {
    let merchants: [Merchant]
    let total: Int
    let page: Int
    let limit: Int
    let hasMore: Bool
}

// MARK: - Merchant Search Filters
struct MerchantSearchFilters: Codable {
    let category: MerchantCategory?
    let status: MerchantStatus?
    let city: String?
    let searchQuery: String?
    let sortBy: MerchantSortBy?
    let sortOrder: SortOrder?
}

enum MerchantSortBy: String, Codable, CaseIterable {
    case name = "name"
    case commissionRate = "commissionRate"
    case totalTransactions = "totalTransactions"
    case createdAt = "createdAt"
    
    var displayName: String {
        switch self {
        case .name:
            return "Name"
        case .commissionRate:
            return "Commission Rate"
        case .totalTransactions:
            return "Total Transactions"
        case .createdAt:
            return "Date Added"
        }
    }
}

enum SortOrder: String, Codable, CaseIterable {
    case asc = "asc"
    case desc = "desc"
    
    var displayName: String {
        switch self {
        case .asc:
            return "Ascending"
        case .desc:
            return "Descending"
        }
    }
}

//
//  Receipt.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 22/7/25.
//

import Foundation

// MARK: - Receipt Data Model
struct ReceiptData: Codable, Identifiable {
    let id: String
    let merchantName: String
    let merchantAddress: String?
    let date: Date
    let totalAmount: Double
    let subtotal: Double?
    let taxAmount: Double?  // Renamed from 'tax' to 'taxAmount' for clarity
    let items: [ReceiptItem]
    let paymentMethod: String?
    let receiptNumber: String?
    let confidence: Double
    let estimatedTokens: Double
    let currency: String
    let rawText: String?
    
    // Computed properties
    var displayTotalAmount: String {
        return String(format: "%.2f %@", totalAmount, currency)
    }
    
    var displayEstimatedTokens: String {
        return String(format: "%.4f LXT", estimatedTokens)
    }
    
    var confidencePercentage: Int {
        return Int(confidence * 100)
    }
    
    var isHighConfidence: Bool {
        return confidence >= 0.8
    }
    
    // Legacy properties for backward compatibility
    var tax: Double? { taxAmount }
    var total: Double { totalAmount }
    
    // Initializer with default values
    init(
        id: String,
        merchantName: String,
        merchantAddress: String? = nil,
        date: Date,
        totalAmount: Double,
        subtotal: Double? = nil,
        taxAmount: Double? = nil,
        items: [ReceiptItem] = [],
        paymentMethod: String? = nil,
        receiptNumber: String? = nil,
        confidence: Double,
        estimatedTokens: Double,
        currency: String = "VND",
        rawText: String? = nil
    ) {
        self.id = id
        self.merchantName = merchantName
        self.merchantAddress = merchantAddress
        self.date = date
        self.totalAmount = totalAmount
        self.subtotal = subtotal
        self.taxAmount = taxAmount
        self.items = items
        self.paymentMethod = paymentMethod
        self.receiptNumber = receiptNumber
        self.confidence = confidence
        self.estimatedTokens = estimatedTokens
        self.currency = currency
        self.rawText = rawText
    }
}

// MARK: - Receipt Item Model
struct ReceiptItem: Codable, Identifiable {
    let id: UUID
    let name: String
    let quantity: Int?
    let unitPrice: Double?  // Price per unit
    let totalPrice: Double  // Total price for this item
    let category: String?
    
    // Computed properties
    var displayUnitPrice: String? {
        guard let unitPrice = unitPrice else { return nil }
        return String(format: "%.2f", unitPrice)
    }
    
    var displayTotalPrice: String {
        return String(format: "%.2f", totalPrice)
    }
    
    var displayQuantity: String {
        guard let quantity = quantity else { return "1" }
        return "\(quantity)"
    }
    
    // Legacy properties for backward compatibility
    var price: Double { totalPrice }
    var total: Double { totalPrice }
    
    // Initializer with default values
    init(
        id: UUID = UUID(),
        name: String,
        quantity: Int? = nil,
        unitPrice: Double? = nil,
        totalPrice: Double,
        category: String? = nil
    ) {
        self.id = id
        self.name = name
        self.quantity = quantity
        self.unitPrice = unitPrice
        self.totalPrice = totalPrice
        self.category = category
    }
    
    // Convenience initializer for backward compatibility
    init(
        name: String,
        price: Double,
        quantity: Int? = nil
    ) {
        self.id = UUID()
        self.name = name
        self.quantity = quantity
        self.unitPrice = quantity != nil ? price / Double(quantity!) : price
        self.totalPrice = price
        self.category = nil
    }
}

// MARK: - Receipt Processing Status
enum ReceiptProcessingStatus: String, Codable, CaseIterable {
    case processing = "PROCESSING"
    case completed = "COMPLETED"
    case failed = "FAILED"
    case pending = "PENDING"
    
    var displayName: String {
        switch self {
        case .processing:
            return "Processing"
        case .completed:
            return "Completed"
        case .failed:
            return "Failed"
        case .pending:
            return "Pending"
        }
    }
    
    var color: String {
        switch self {
        case .processing:
            return "blue"
        case .completed:
            return "green"
        case .failed:
            return "red"
        case .pending:
            return "orange"
        }
    }
}

// MARK: - Receipt Processing Result
struct ReceiptProcessingResult: Codable {
    let id: String
    let status: ReceiptProcessingStatus
    let receiptData: ReceiptData?
    let errorMessage: String?
    let processingTime: Double?
    let createdAt: Date
    let updatedAt: Date?
    
    var isSuccessful: Bool {
        return status == .completed && receiptData != nil
    }
}

// MARK: - Receipt Validation
extension ReceiptData {
    var isValid: Bool {
        return !merchantName.isEmpty && 
               totalAmount > 0 && 
               confidence >= 0.0 && 
               confidence <= 1.0
    }
    
    func validate() throws {
        guard !merchantName.isEmpty else {
            throw ReceiptValidationError.emptyMerchantName
        }
        
        guard totalAmount > 0 else {
            throw ReceiptValidationError.invalidTotalAmount
        }
        
        guard confidence >= 0.0 && confidence <= 1.0 else {
            throw ReceiptValidationError.invalidConfidence
        }
        
        // Validate items
        for item in items {
            try item.validate()
        }
    }
}

extension ReceiptItem {
    var isValid: Bool {
        return !name.isEmpty && totalPrice >= 0
    }
    
    func validate() throws {
        guard !name.isEmpty else {
            throw ReceiptValidationError.emptyItemName
        }
        
        guard totalPrice >= 0 else {
            throw ReceiptValidationError.invalidItemPrice
        }
        
        if let quantity = quantity, quantity <= 0 {
            throw ReceiptValidationError.invalidItemQuantity
        }
        
        if let unitPrice = unitPrice, unitPrice < 0 {
            throw ReceiptValidationError.invalidItemUnitPrice
        }
    }
}

// MARK: - Receipt Validation Errors
enum ReceiptValidationError: LocalizedError {
    case emptyMerchantName
    case invalidTotalAmount
    case invalidConfidence
    case emptyItemName
    case invalidItemPrice
    case invalidItemQuantity
    case invalidItemUnitPrice
    
    var errorDescription: String? {
        switch self {
        case .emptyMerchantName:
            return "Merchant name cannot be empty"
        case .invalidTotalAmount:
            return "Total amount must be greater than 0"
        case .invalidConfidence:
            return "Confidence must be between 0 and 1"
        case .emptyItemName:
            return "Item name cannot be empty"
        case .invalidItemPrice:
            return "Item price cannot be negative"
        case .invalidItemQuantity:
            return "Item quantity must be greater than 0"
        case .invalidItemUnitPrice:
            return "Item unit price cannot be negative"
        }
    }
}

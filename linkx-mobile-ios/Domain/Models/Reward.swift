//
//  Reward.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation

// MARK: - Reward Model
struct Reward: Codable, Identifiable {
    let id: String
    let title: String
    let description: String
    let category: RewardCategory
    let tokenCost: Double
    let originalPrice: Double?
    let discountPercentage: Double?
    let imageUrl: String?
    let thumbnailUrl: String?
    let isActive: Bool
    let isLimited: Bool
    let totalQuantity: Int?
    let remainingQuantity: Int?
    let validFrom: Date?
    let validUntil: Date?
    let terms: String?
    let partnerId: String?
    let partnerName: String?
    let partnerLogo: String?
    let redemptionInstructions: String?
    let metadata: [String: AnyCodable]?
    let tags: [String]?
    let popularity: Int
    let totalRedemptions: Int
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var displayTokenCost: String {
        return String(format: "%.0f LXT", tokenCost)
    }
    
    var displayOriginalPrice: String? {
        guard let originalPrice = originalPrice else { return nil }
        return String(format: "%.0f VND", originalPrice)
    }
    
    var displayDiscount: String? {
        guard let discountPercentage = discountPercentage else { return nil }
        return String(format: "%.0f%% OFF", discountPercentage * 100)
    }
    
    var isAvailable: Bool {
        guard isActive else { return false }
        
        // Check date validity
        let now = Date()
        if let validFrom = validFrom, now < validFrom { return false }
        if let validUntil = validUntil, now > validUntil { return false }
        
        // Check quantity
        if isLimited, let remaining = remainingQuantity, remaining <= 0 { return false }
        
        return true
    }
    
    var availabilityStatus: String {
        if !isActive { return "Inactive" }
        if !isAvailable { return "Unavailable" }
        
        if isLimited, let remaining = remainingQuantity {
            if remaining <= 0 { return "Out of Stock" }
            if remaining <= 10 { return "Limited Stock" }
        }
        
        return "Available"
    }
    
    var statusColor: String {
        switch availabilityStatus {
        case "Available":
            return "green"
        case "Limited Stock":
            return "orange"
        case "Out of Stock", "Unavailable", "Inactive":
            return "red"
        default:
            return "gray"
        }
    }
}

// MARK: - Reward Category
enum RewardCategory: String, Codable, CaseIterable {
    case food = "FOOD"
    case shopping = "SHOPPING"
    case entertainment = "ENTERTAINMENT"
    case travel = "TRAVEL"
    case services = "SERVICES"
    case digital = "DIGITAL"
    case voucher = "VOUCHER"
    case cashback = "CASHBACK"
    case other = "OTHER"
    
    var displayName: String {
        switch self {
        case .food:
            return "Food & Beverage"
        case .shopping:
            return "Shopping"
        case .entertainment:
            return "Entertainment"
        case .travel:
            return "Travel"
        case .services:
            return "Services"
        case .digital:
            return "Digital"
        case .voucher:
            return "Voucher"
        case .cashback:
            return "Cashback"
        case .other:
            return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .food:
            return "fork.knife"
        case .shopping:
            return "bag"
        case .entertainment:
            return "tv"
        case .travel:
            return "airplane"
        case .services:
            return "wrench.and.screwdriver"
        case .digital:
            return "iphone"
        case .voucher:
            return "ticket"
        case .cashback:
            return "dollarsign.circle"
        case .other:
            return "gift"
        }
    }
    
    var color: String {
        switch self {
        case .food:
            return "orange"
        case .shopping:
            return "blue"
        case .entertainment:
            return "purple"
        case .travel:
            return "green"
        case .services:
            return "indigo"
        case .digital:
            return "cyan"
        case .voucher:
            return "pink"
        case .cashback:
            return "yellow"
        case .other:
            return "gray"
        }
    }
}

// MARK: - Redemption Model
struct Redemption: Codable, Identifiable {
    let id: String
    let userId: String
    let rewardId: String
    let tokenCost: Double
    let redemptionCode: String
    let status: RedemptionStatus
    let blockchainTxHash: String?
    let expiresAt: Date
    let redeemedAt: Date?
    let usedAt: Date?
    let partnerConfirmation: String?
    let notes: String?
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var isExpired: Bool {
        return Date() > expiresAt
    }
    
    var isUsable: Bool {
        return status == .active && !isExpired
    }
    
    var displayExpiryDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: expiresAt)
    }
}

// MARK: - Redemption Status
enum RedemptionStatus: String, Codable, CaseIterable {
    case pending = "PENDING"
    case active = "ACTIVE"
    case used = "USED"
    case expired = "EXPIRED"
    case cancelled = "CANCELLED"
    
    var displayName: String {
        switch self {
        case .pending:
            return "Pending"
        case .active:
            return "Active"
        case .used:
            return "Used"
        case .expired:
            return "Expired"
        case .cancelled:
            return "Cancelled"
        }
    }
    
    var color: String {
        switch self {
        case .pending:
            return "orange"
        case .active:
            return "green"
        case .used:
            return "blue"
        case .expired:
            return "red"
        case .cancelled:
            return "gray"
        }
    }
}

// MARK: - Reward List Response
struct RewardListResponse: Codable {
    let rewards: [Reward]
    let total: Int
    let page: Int
    let limit: Int
    let hasMore: Bool
}

// MARK: - Redemption Request
struct RedeemRewardRequest: Codable {
    let rewardId: String
    let quantity: Int?
}

// MARK: - Redemption List Response
struct RedemptionListResponse: Codable {
    let redemptions: [RedemptionWithReward]
    let total: Int
    let page: Int
    let limit: Int
    let hasMore: Bool
}

struct RedemptionWithReward: Codable, Identifiable {
    let id: String
    let redemption: Redemption
    let reward: Reward
}

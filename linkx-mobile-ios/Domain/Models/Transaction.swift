//
//  Transaction.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation

// MARK: - Transaction Model
struct Transaction: Codable, Identifiable {
    let id: String
    let userId: String?  // Make userId optional since backend doesn't return it
    let type: TransactionType
    let amountVnd: Double?
    let amountLxt: Double
    let merchantId: String?
    let merchantCommission: Double?
    let commissionRate: Double?
    let platformProfit: Double?
    let blockchainTxHash: String?
    let status: TransactionStatus
    let description: String?
    let metadata: [String: AnyCodable]?
    let errorMessage: String?
    let retryCount: Int?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var displayAmount: String {
        return String(format: "%.2f LXT", amountLxt)
    }
    
    var displayAmountVnd: String? {
        guard let amountVnd = amountVnd else { return nil }
        return String(format: "%.0f VND", amountVnd)
    }
    
    var statusColor: String {
        switch status {
        case .pending:
            return "orange"
        case .completed:
            return "green"
        case .failed:
            return "red"
        }
    }
    
    var typeIcon: String {
        switch type {
        case .earn:
            return "plus.circle.fill"
        case .redeem:
            return "minus.circle.fill"
        case .transferIn:
            return "arrow.down.circle.fill"
        case .transferOut:
            return "arrow.up.circle.fill"
        }
    }
    
    var typeDisplayName: String {
        switch type {
        case .earn:
            return "Earned"
        case .redeem:
            return "Redeemed"
        case .transferIn:
            return "Received"
        case .transferOut:
            return "Sent"
        }
    }
}

// MARK: - Transaction Type
enum TransactionType: String, Codable, CaseIterable {
    case earn = "EARN"
    case redeem = "REDEEM"
    case transferIn = "TRANSFER_IN"
    case transferOut = "TRANSFER_OUT"
    
    var displayName: String {
        switch self {
        case .earn:
            return "Earn Tokens"
        case .redeem:
            return "Redeem Reward"
        case .transferIn:
            return "Transfer In"
        case .transferOut:
            return "Transfer Out"
        }
    }
}

// MARK: - Transaction Status
enum TransactionStatus: String, Codable, CaseIterable {
    case pending = "PENDING"
    case completed = "COMPLETED"
    case failed = "FAILED"
    
    var displayName: String {
        switch self {
        case .pending:
            return "Pending"
        case .completed:
            return "Completed"
        case .failed:
            return "Failed"
        }
    }
}

// MARK: - Transaction DTOs
struct EarnTokensRequest: Codable {
    let merchantId: String
    let amountVnd: Double
    let description: String?
}

struct TransferTokensRequest: Codable {
    let recipientAddress: String
    let amountLxt: Double
    let description: String?
}

struct TransactionListResponse: Codable {
    let transactions: [Transaction]
    let total: Int
    let page: Int
    let limit: Int
    let hasMore: Bool
}

// Backend response format for transaction history (OpenAPI spec)
struct TransactionHistoryDto: Codable {
    let transactions: [TransactionResponseDto]
    let total: Int
    let limit: Int
    let offset: Int
}

// Backend transaction response format (OpenAPI spec)
struct TransactionResponseDto: Codable {
    let id: String
    let type: String
    let amountVnd: Double?
    let amountLxt: Double
    let merchantId: String?
    let merchantCommission: Double?
    let commissionRate: Double?
    let platformProfit: Double?
    let status: String
    let blockchainTxHash: String?
    let description: String?
    let createdAt: String // ISO date string
    let updatedAt: String? // ISO date string

    // Convert to internal Transaction model
    func toTransaction() -> Transaction {
        let dateFormatter = ISO8601DateFormatter()

        return Transaction(
            id: id,
            userId: nil,
            type: TransactionType(rawValue: type) ?? .earn,
            amountVnd: amountVnd,
            amountLxt: amountLxt,
            merchantId: merchantId,
            merchantCommission: merchantCommission,
            commissionRate: commissionRate,
            platformProfit: platformProfit,
            blockchainTxHash: blockchainTxHash,
            status: TransactionStatus(rawValue: status) ?? .pending,
            description: description,
            metadata: nil,
            errorMessage: nil,
            retryCount: nil,
            createdAt: dateFormatter.date(from: createdAt) ?? Date(),
            updatedAt: updatedAt != nil ? dateFormatter.date(from: updatedAt!) : nil
        )
    }
}

// Legacy model for backward compatibility
struct TransactionHistoryResponse: Codable {
    let transactions: [Transaction]
    let total: Int
    let limit: Int
    let offset: Int
}

// Generic API response for debugging
struct GenericAPIResponse: Codable {
    let success: Bool?
    let data: [String: String]?  // Simplified for now
    let message: String?
    let timestamp: String?
    let requestId: String?
}

// MARK: - AnyCodable for metadata
struct AnyCodable: Codable {
    let value: Any
    
    init<T>(_ value: T?) {
        self.value = value ?? ()
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if container.decodeNil() {
            self.value = ()
        } else if let bool = try? container.decode(Bool.self) {
            self.value = bool
        } else if let int = try? container.decode(Int.self) {
            self.value = int
        } else if let double = try? container.decode(Double.self) {
            self.value = double
        } else if let string = try? container.decode(String.self) {
            self.value = string
        } else if let array = try? container.decode([AnyCodable].self) {
            self.value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            self.value = dictionary.mapValues { $0.value }
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "AnyCodable value cannot be decoded")
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case is Void:
            try container.encodeNil()
        case let bool as Bool:
            try container.encode(bool)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let array as [Any]:
            let anyArray = array.map { AnyCodable($0) }
            try container.encode(anyArray)
        case let dictionary as [String: Any]:
            let anyDictionary = dictionary.mapValues { AnyCodable($0) }
            try container.encode(anyDictionary)
        default:
            let context = EncodingError.Context(codingPath: container.codingPath, debugDescription: "AnyCodable value cannot be encoded")
            throw EncodingError.invalidValue(value, context)
        }
    }
}

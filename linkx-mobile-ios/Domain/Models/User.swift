//
//  User.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation

// MARK: - User Model
struct User: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let phone: String?
    let walletAddress: String
    let role: UserRole
    let isActive: Bool
    let avatar: String?
    let dateOfBirth: Date?
    let lastLoginAt: Date?
    let createdAt: Date
    let updatedAt: Date?
    
    // Business information for MERCHANT users
    let businessName: String?
    let businessId: String?
    let category: String?
    let businessPhone: String?
    let website: String?
    let businessDescription: String?
    let commissionRate: Double?
    let totalTransactions: Int?
    let totalCommissionPaid: Double?
    let businessStatus: String?
    let onboardedAt: Date?
    
    // Computed properties
    var displayName: String {
        if let firstName = firstName, let lastName = lastName {
            return "\(firstName) \(lastName)"
        }
        return email
    }
    
    var initials: String {
        let components = displayName.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.map { String($0) }
        return initials.prefix(2).joined().uppercased()
    }
    
    var isMerchant: Bool {
        return role == .merchant
    }
    
    var isAdmin: Bool {
        return role == .admin
    }
}

// MARK: - User Role
enum UserRole: String, Codable, CaseIterable {
    case user = "USER"
    case merchant = "MERCHANT"
    case admin = "ADMIN"
    
    var displayName: String {
        switch self {
        case .user:
            return "User"
        case .merchant:
            return "Merchant"
        case .admin:
            return "Admin"
        }
    }
}

// MARK: - Auth DTOs
struct LoginRequest: Codable {
    let email: String
    let password: String
}

struct RegisterRequest: Codable {
    let email: String
    let password: String
    let phone: String?
    let firstName: String?
    let lastName: String?
}

// MARK: - Auth Tokens
struct AuthTokens: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case tokenType = "token_type"
        case expiresIn = "expires_in"
    }
}

struct AuthResponse: Codable {
    let access_token: String
    let user: User
    let tokens: AuthTokens?

    enum CodingKeys: String, CodingKey {
        case access_token
        case user
        case tokens
    }
}

// MARK: - User Profile Update
struct UpdateProfileRequest: Codable {
    let firstName: String?
    let lastName: String?
    let phone: String?
    let dateOfBirth: Date?
    let avatar: String?
}

// MARK: - Password Change
struct ChangePasswordRequest: Codable {
    let currentPassword: String
    let newPassword: String
}

// MARK: - Wallet Balance
struct WalletBalance: Codable {
    let balance: Double
    let walletAddress: String
    let balanceVnd: Double

    // Computed properties for backward compatibility
    var lxtBalance: Double {
        return balance
    }

    var vndEquivalent: Double {
        return balanceVnd
    }

    var displayBalance: String {
        return balance.formatAsToken()
    }

    var displayVndEquivalent: String {
        return balanceVnd.formatAsCurrency()
    }
}

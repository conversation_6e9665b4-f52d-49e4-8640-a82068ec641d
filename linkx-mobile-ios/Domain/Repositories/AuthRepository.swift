//
//  AuthRepository.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Combine
import UIKit

// MARK: - Backend Response Models
struct BackendAuthResponse: Codable {
    let access_token: String
    let refresh_token: String?
    let user: BackendUser
}

struct BackendUser: Codable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let phone: String?
    let avatar: String?
    let walletAddress: String
    let role: String
    let isEmailVerified: Bool?
}

// MARK: - API v1 Response Models
struct APIv1Response<T: Codable>: Codable {
    let success: Bool
    let data: T
    let timestamp: String?
    let requestId: String?
}

struct APIv1AuthData: Codable {
    let access_token: String
    let user: APIv1User
}

struct APIv1User: Codable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let phone: String?
    let avatar: String?
    let walletAddress: String
    let role: String
}

// MARK: - Auth Repository Protocol
protocol AuthRepositoryProtocol {
    func login(email: String, password: String) async throws -> AuthResponse
    func register(request: RegisterRequest) async throws -> AuthResponse
    func logout() async throws
    func refreshToken() async throws -> AuthResponse
    func getCurrentUser() async throws -> User
    func updateProfile(request: UpdateProfileRequest) async throws -> User
    func changePassword(request: ChangePasswordRequest) async throws
    func deleteAccount() async throws
}

// MARK: - Auth Repository Implementation
class AuthRepository: AuthRepositoryProtocol {
    private let apiClient = APIClient.shared
    private let tokenManager = TokenManager.shared

    // MARK: - Helper Methods
    private func getDeviceInfo() -> [String: Any] {
        return [
            "platform": "iOS",
            "deviceModel": UIDevice.current.model,
            "systemVersion": UIDevice.current.systemVersion,
            "appVersion": AppConstants.AppInfo.fullVersion,
            "deviceId": UIDevice.current.identifierForVendor?.uuidString ?? "",
            "locale": Locale.current.identifier,
            "timezone": TimeZone.current.identifier
        ]
    }
    
    // MARK: - Login
    func login(email: String, password: String) async throws -> AuthResponse {
        let request = LoginRequest(email: email, password: password)

        // API v1 returns wrapped response
        let apiResponse: APIv1Response<APIv1AuthData> = try await apiClient.request(
            endpoint: APIEndpoints.Auth.login,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: APIv1Response<APIv1AuthData>.self,
            requiresAuth: false
        )

        // Extract data from wrapped response
        let authData = apiResponse.data

        // Convert API v1 response to our AuthResponse format
        let userRole = UserRole(rawValue: authData.user.role) ?? .user
        let user = User(
            id: authData.user.id,
            email: authData.user.email,
            firstName: authData.user.firstName,
            lastName: authData.user.lastName,
            phone: authData.user.phone,
            walletAddress: authData.user.walletAddress,
            role: userRole,
            isActive: true,
            avatar: authData.user.avatar,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            commissionRate: nil,
            totalTransactions: nil,
            totalCommissionPaid: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        let tokens = AuthTokens(
            accessToken: authData.access_token,
            refreshToken: nil, // API v1 doesn't return refresh token in this response
            tokenType: "Bearer",
            expiresIn: 3600 // Default 1 hour
        )

        let authResponse = AuthResponse(access_token: authData.access_token, user: user, tokens: tokens)

        // Save token and user data
        tokenManager.saveToken(tokens.accessToken)
        tokenManager.saveUser(user)

        // Log successful login
        Logger.shared.logAuthentication("login", success: true)

        return authResponse
    }
    
    // MARK: - Register
    func register(request: RegisterRequest) async throws -> AuthResponse {
        // API v1 returns wrapped response
        let apiResponse: APIv1Response<APIv1AuthData> = try await apiClient.request(
            endpoint: APIEndpoints.Auth.register,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: APIv1Response<APIv1AuthData>.self,
            requiresAuth: false
        )

        // Extract data from wrapped response
        let authData = apiResponse.data

        // Convert API v1 response to our AuthResponse format
        let userRole = UserRole(rawValue: authData.user.role) ?? .user
        let user = User(
            id: authData.user.id,
            email: authData.user.email,
            firstName: authData.user.firstName,
            lastName: authData.user.lastName,
            phone: authData.user.phone,
            walletAddress: authData.user.walletAddress,
            role: userRole,
            isActive: true,
            avatar: authData.user.avatar,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            commissionRate: nil,
            totalTransactions: nil,
            totalCommissionPaid: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        let tokens = AuthTokens(
            accessToken: authData.access_token,
            refreshToken: nil, // API v1 doesn't return refresh token in this response
            tokenType: "Bearer",
            expiresIn: 3600 // Default 1 hour
        )

        let authResponse = AuthResponse(access_token: authData.access_token, user: user, tokens: tokens)

        // Save token and user data
        tokenManager.saveToken(tokens.accessToken)
        tokenManager.saveUser(user)

        // Log successful registration
        Logger.shared.logAuthentication("register", success: true)

        return authResponse
    }
    
    // MARK: - Logout
    func logout() async throws {
        // Call logout endpoint to invalidate token on server
        do {
            let _: EmptyResponse = try await apiClient.request(
                endpoint: APIEndpoints.Auth.logout,
                method: .POST,
                responseType: EmptyResponse.self
            )
        } catch {
            // Continue with local logout even if server call fails
            print("Server logout failed: \(error)")
        }

        // Clear ALL local data using DataCleanupManager
        DataCleanupManager.shared.clearAuthenticationData()
    }
    
    // MARK: - Refresh Token
    func refreshToken() async throws -> AuthResponse {
        let response: AuthResponse = try await apiClient.request(
            endpoint: APIEndpoints.Auth.refreshToken,
            method: .POST,
            responseType: AuthResponse.self
        )
        
        // Update token and user data
        tokenManager.saveToken(response.access_token)
        tokenManager.saveUser(response.user)
        
        return response
    }
    
    // MARK: - Get Current User
    func getCurrentUser() async throws -> User {
        let user: User = try await apiClient.request(
            endpoint: APIEndpoints.Users.profile,
            method: .GET,
            responseType: User.self
        )
        
        // Update cached user data
        tokenManager.saveUser(user)
        
        return user
    }
    
    // MARK: - Update Profile
    func updateProfile(request: UpdateProfileRequest) async throws -> User {
        let user: User = try await apiClient.request(
            endpoint: APIEndpoints.Users.updateProfile,
            method: .PUT,
            parameters: try request.toDictionary(),
            responseType: User.self
        )
        
        // Update cached user data
        tokenManager.saveUser(user)
        
        return user
    }
    
    // MARK: - Change Password
    func changePassword(request: ChangePasswordRequest) async throws {
        let _: EmptyResponse = try await apiClient.request(
            endpoint: APIEndpoints.Auth.changePassword,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: EmptyResponse.self
        )
    }
    
    // MARK: - Delete Account
    func deleteAccount() async throws {
        let _: EmptyResponse = try await apiClient.request(
            endpoint: APIEndpoints.Users.deleteAccount,
            method: .DELETE,
            responseType: EmptyResponse.self
        )
        
        // Clear local data
        tokenManager.clearToken()
    }
}

// MARK: - Helper Extensions
extension Encodable {
    func toDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to convert to dictionary"])
        }
        return dictionary
    }
}



// MARK: - Auth State Manager
class AuthStateManager: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    
    private let authRepository: AuthRepositoryProtocol
    private let tokenManager = TokenManager.shared
    
    init(authRepository: AuthRepositoryProtocol = AuthRepository()) {
        self.authRepository = authRepository
        checkAuthState()

        // Ensure UI updates immediately
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }
    
    private func checkAuthState() {
        isAuthenticated = tokenManager.isLoggedIn
        currentUser = tokenManager.getUser(User.self)
        print("🔐 AuthStateManager: checkAuthState - isAuthenticated: \(isAuthenticated)")

        // Debug: Print current token
        if let token = tokenManager.getToken() {
            print("🔐 AuthStateManager: Current token: \(String(token.prefix(50)))...")
        } else {
            print("🔐 AuthStateManager: No token found")
        }

        if let user = currentUser {
            print("🔐 AuthStateManager: checkAuthState - currentUser: ID: \(user.id), Role: \(user.role), Name: \(user.displayName)")
        } else {
            print("🔐 AuthStateManager: checkAuthState - currentUser is nil")
        }
    }
    
    @MainActor
    func login(email: String, password: String) async throws {
        isLoading = true
        defer { isLoading = false }

        // Clear any cached user data before fresh login
        DataCleanupManager.shared.clearCachedUserData()
        print("🔐 AuthStateManager: Cleared cached user data before login")

        let response = try await authRepository.login(email: email, password: password)

        print("🔐 AuthStateManager: Setting isAuthenticated = true")
        isAuthenticated = true
        currentUser = response.user
        print("🔐 AuthStateManager: isAuthenticated is now \(isAuthenticated)")
        print("🔐 AuthStateManager: currentUser set to - ID: \(response.user.id), Role: \(response.user.role), Name: \(response.user.displayName)")
    }
    
    @MainActor
    func register(request: RegisterRequest) async throws {
        isLoading = true
        defer { isLoading = false }

        // Clear any cached user data before fresh registration
        DataCleanupManager.shared.clearCachedUserData()
        print("🔐 AuthStateManager: Cleared cached user data before registration")

        let response = try await authRepository.register(request: request)

        isAuthenticated = true
        currentUser = response.user
    }
    
    @MainActor
    func logout() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            try await authRepository.logout()
        } catch {
            print("Logout error: \(error)")
        }
        
        isAuthenticated = false
        currentUser = nil
    }
    
    @MainActor
    func refreshUserData() async {
        guard isAuthenticated else { return }

        do {
            currentUser = try await authRepository.getCurrentUser()
        } catch {
            print("Failed to refresh user data: \(error)")
            // If refresh fails due to invalid token, logout
            if error is NetworkError {
                await logout()
            }
        }
    }

    @MainActor
    func clearCacheAndRecheck() {
        print("🔐 AuthStateManager: Clearing cache and rechecking auth state")
        // Clear cached user data but keep token
        let currentToken = tokenManager.getToken()
        tokenManager.clearToken()
        if let token = currentToken {
            tokenManager.saveToken(token)
        }
        checkAuthState()
    }

    @MainActor
    func updateTokenAndUser(token: String, user: User) {
        print("🔐 AuthStateManager: Updating token and user data")
        print("🔐 AuthStateManager: New user - ID: \(user.id), Role: \(user.role), Name: \(user.displayName)")

        tokenManager.saveToken(token)
        tokenManager.saveUser(user)

        isAuthenticated = true
        currentUser = user

        print("🔐 AuthStateManager: Token and user updated successfully")
    }

    @MainActor
    func forceRecheck() {
        print("🔐 AuthStateManager: Force rechecking auth state")
        checkAuthState()
    }

    @MainActor
    func forceClearAllData() {
        print("🔐 AuthStateManager: Force clearing ALL user data")
        DataCleanupManager.shared.clearAllUserData()
        isAuthenticated = false
        currentUser = nil
        checkAuthState()
        print("🔐 AuthStateManager: All data cleared and state reset")
    }
}

//
//  RewardRepository.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Combine

// MARK: - Reward Repository Protocol
protocol RewardRepositoryProtocol {
    func getRewards(page: Int, limit: Int, category: RewardCategory?, searchQuery: String?) async throws -> RewardListResponse
    func getReward(id: String) async throws -> Reward
    func redeemReward(request: RedeemRewardRequest) async throws -> Redemption
    func getRedemptions(page: Int, limit: Int, status: RedemptionStatus?) async throws -> RedemptionListResponse
    func getRedemption(id: String) async throws -> RedemptionWithReward
    func getFeaturedRewards() async throws -> [Reward]
    func getPopularRewards() async throws -> [Reward]
}

// MARK: - Reward Repository Implementation
class RewardRepository: RewardRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    // MARK: - Get Rewards
    func getRewards(page: Int = 1, limit: Int = 20, category: RewardCategory? = nil, searchQuery: String? = nil) async throws -> RewardListResponse {
        var parameters: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let category = category {
            parameters["category"] = category.rawValue
        }
        
        if let searchQuery = searchQuery, !searchQuery.isEmpty {
            parameters["search"] = searchQuery
        }
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Rewards.list,
            method: .GET,
            parameters: parameters,
            responseType: RewardListResponse.self
        )
    }
    
    // MARK: - Get Single Reward
    func getReward(id: String) async throws -> Reward {
        return try await apiClient.request(
            endpoint: APIEndpoints.Rewards.details(id: id),
            method: .GET,
            responseType: Reward.self
        )
    }
    
    // MARK: - Redeem Reward
    func redeemReward(request: RedeemRewardRequest) async throws -> Redemption {
        return try await apiClient.request(
            endpoint: APIEndpoints.Rewards.redeem(id: request.rewardId),
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: Redemption.self
        )
    }
    
    // MARK: - Get Redemptions
    func getRedemptions(page: Int = 1, limit: Int = 20, status: RedemptionStatus? = nil) async throws -> RedemptionListResponse {
        var parameters: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let status = status {
            parameters["status"] = status.rawValue
        }
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Rewards.redemptions,
            method: .GET,
            parameters: parameters,
            responseType: RedemptionListResponse.self
        )
    }
    
    // MARK: - Get Single Redemption
    func getRedemption(id: String) async throws -> RedemptionWithReward {
        return try await apiClient.request(
            endpoint: APIEndpoints.Rewards.redemption(id: id),
            method: .GET,
            responseType: RedemptionWithReward.self
        )
    }
    
    // MARK: - Get Featured Rewards
    func getFeaturedRewards() async throws -> [Reward] {
        let response: RewardListResponse = try await apiClient.request(
            endpoint: APIEndpoints.Rewards.featured,
            method: .GET,
            responseType: RewardListResponse.self
        )
        return response.rewards
    }

    // MARK: - Get Popular Rewards
    func getPopularRewards() async throws -> [Reward] {
        let response: RewardListResponse = try await apiClient.request(
            endpoint: APIEndpoints.Rewards.popular,
            method: .GET,
            responseType: RewardListResponse.self
        )
        return response.rewards
    }
}

// MARK: - Reward Manager
@MainActor
class RewardManager: ObservableObject {
    @Published var rewards: [Reward] = []
    @Published var featuredRewards: [Reward] = []
    @Published var popularRewards: [Reward] = []
    @Published var redemptions: [RedemptionWithReward] = []
    @Published var selectedCategory: RewardCategory?
    @Published var searchQuery: String = ""
    @Published var isLoading = false
    @Published var error: Error?
    
    private let repository: RewardRepositoryProtocol
    private var currentPage = 1
    private var hasMorePages = true
    
    init(repository: RewardRepositoryProtocol = RewardRepository()) {
        self.repository = repository
    }
    
    // MARK: - Load Rewards
    func loadRewards(refresh: Bool = false) async {
        if refresh {
            currentPage = 1
            hasMorePages = true
            rewards.removeAll()
        }
        
        guard hasMorePages && !isLoading else { return }
        
        isLoading = true
        error = nil
        
        do {
            let response = try await repository.getRewards(
                page: currentPage,
                limit: AppConstants.Pagination.defaultLimit,
                category: selectedCategory,
                searchQuery: searchQuery.isEmpty ? nil : searchQuery
            )
            
            if refresh {
                rewards = response.rewards
            } else {
                rewards.append(contentsOf: response.rewards)
            }
            
            hasMorePages = response.hasMore
            currentPage += 1
            
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    
    // MARK: - Load Featured Rewards
    func loadFeaturedRewards() async {
        isLoading = true
        error = nil

        do {
            featuredRewards = try await repository.getFeaturedRewards()
        } catch {
            self.error = error
        }

        isLoading = false
    }
    
    // MARK: - Load Popular Rewards
    func loadPopularRewards() async {
        do {
            popularRewards = try await repository.getPopularRewards()
        } catch {
            self.error = error
        }
    }
    
    // MARK: - Load Redemptions
    func loadRedemptions(refresh: Bool = false, status: RedemptionStatus? = nil) async {
        if refresh {
            redemptions.removeAll()
        }
        
        isLoading = true
        error = nil
        
        do {
            let response = try await repository.getRedemptions(
                page: 1,
                limit: 50,
                status: status
            )
            
            redemptions = response.redemptions
            
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    
    // MARK: - Redeem Reward
    func redeemReward(rewardId: String, quantity: Int = 1) async -> Bool {
        isLoading = true
        error = nil
        
        do {
            let request = RedeemRewardRequest(rewardId: rewardId, quantity: quantity)
            let redemption = try await repository.redeemReward(request: request)
            
            // Add new redemption to the list
            if let reward = rewards.first(where: { $0.id == rewardId }) {
                let redemptionWithReward = RedemptionWithReward(
                    id: redemption.id,
                    redemption: redemption,
                    reward: reward
                )
                redemptions.insert(redemptionWithReward, at: 0)
            }
            
            isLoading = false
            return true
            
        } catch {
            self.error = error
            isLoading = false
            return false
        }
    }
    
    // MARK: - Search Rewards
    func searchRewards(query: String) async {
        searchQuery = query
        await loadRewards(refresh: true)
    }
    
    // MARK: - Filter by Category
    func filterByCategory(_ category: RewardCategory?) async {
        selectedCategory = category
        await loadRewards(refresh: true)
    }
    
    // MARK: - Get Available Rewards
    var availableRewards: [Reward] {
        return rewards.filter { $0.isAvailable }
    }
    
    // MARK: - Get Rewards by Category
    func getRewards(for category: RewardCategory) -> [Reward] {
        return rewards.filter { $0.category == category }
    }
    
    // MARK: - Get Active Redemptions
    var activeRedemptions: [RedemptionWithReward] {
        return redemptions.filter { $0.redemption.status == .active }
    }
    
    // MARK: - Get Used Redemptions
    var usedRedemptions: [RedemptionWithReward] {
        return redemptions.filter { $0.redemption.status == .used }
    }
    
    // MARK: - Get Expired Redemptions
    var expiredRedemptions: [RedemptionWithReward] {
        return redemptions.filter { $0.redemption.status == .expired }
    }
    
    // MARK: - Refresh All Data
    func refreshAll() async {
        await loadRewards(refresh: true)
        await loadFeaturedRewards()
        await loadPopularRewards()
        await loadRedemptions(refresh: true)
    }
    
    // MARK: - Clear Search
    func clearSearch() async {
        searchQuery = ""
        await loadRewards(refresh: true)
    }
    
    // MARK: - Clear Category Filter
    func clearCategoryFilter() async {
        selectedCategory = nil
        await loadRewards(refresh: true)
    }
    
    // MARK: - Clear Error
    func clearError() {
        error = nil
    }
    
    // MARK: - Get Reward by ID
    func getReward(id: String) -> Reward? {
        return rewards.first { $0.id == id }
    }
    
    // MARK: - Get Redemption by ID
    func getRedemption(id: String) -> RedemptionWithReward? {
        return redemptions.first { $0.id == id }
    }
}

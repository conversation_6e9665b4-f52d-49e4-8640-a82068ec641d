//
//  TransactionRepository.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Combine

// MARK: - Transaction Repository Protocol
protocol TransactionRepositoryProtocol {
    func getTransactions(page: Int, limit: Int, type: TransactionType?) async throws -> TransactionListResponse
    func getTransaction(id: String) async throws -> Transaction
    func earnTokens(request: EarnTokensRequest) async throws -> Transaction
    func transferTokens(request: TransferTokensRequest) async throws -> Transaction
    func getBalance() async throws -> WalletBalance
    func getTransactionStats() async throws -> TransactionStats
}

// MARK: - Transaction Repository Implementation
class TransactionRepository: TransactionRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    // MARK: - Get Transactions
    func getTransactions(page: Int = 1, limit: Int = 20, type: TransactionType? = nil) async throws -> TransactionListResponse {
        var parameters: [String: Any] = [
            "limit": limit,
            "offset": (page - 1) * limit
        ]

        if let type = type {
            parameters["type"] = type.rawValue
        }

        // Use the correct backend endpoint for transaction history
        print("🔍 [TransactionRepository] Calling /transactions/history with parameters: \(parameters)")

        do {
            // Try with generic response wrapper first
            let response = try await apiClient.request(
                endpoint: APIEndpoints.Transactions.history,
                method: .GET,
                parameters: parameters,
                responseType: GenericAPIResponse.self
            )

            print("🔍 [TransactionRepository] API response structure: \(response)")

            // For now, return empty response until we understand the API structure
            return TransactionListResponse(
                transactions: [],
                total: 0,
                page: page,
                limit: 20,
                hasMore: false
            )
        } catch {
            print("❌ [TransactionRepository] Error calling /transactions/history: \(error)")

            // Log more details about the error
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("❌ [TransactionRepository] Missing key '\(key.stringValue)' at path: \(context.codingPath)")
                case .typeMismatch(let type, let context):
                    print("❌ [TransactionRepository] Type mismatch for \(type) at path: \(context.codingPath)")
                case .valueNotFound(let type, let context):
                    print("❌ [TransactionRepository] Value not found for \(type) at path: \(context.codingPath)")
                case .dataCorrupted(let context):
                    print("❌ [TransactionRepository] Data corrupted at path: \(context.codingPath)")
                @unknown default:
                    print("❌ [TransactionRepository] Unknown decoding error: \(decodingError)")
                }
            }

            throw error
        }
    }
    
    // MARK: - Get Single Transaction
    func getTransaction(id: String) async throws -> Transaction {
        return try await apiClient.request(
            endpoint: APIEndpoints.Transactions.details(id: id),
            method: .GET,
            responseType: Transaction.self
        )
    }
    
    // MARK: - Earn Tokens
    func earnTokens(request: EarnTokensRequest) async throws -> Transaction {
        return try await apiClient.request(
            endpoint: APIEndpoints.Transactions.earn,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: Transaction.self
        )
    }

    // MARK: - Transfer Tokens
    func transferTokens(request: TransferTokensRequest) async throws -> Transaction {
        return try await apiClient.request(
            endpoint: APIEndpoints.Transactions.transfer,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: Transaction.self
        )
    }
    
    // MARK: - Get Wallet Balance
    func getBalance() async throws -> WalletBalance {
        return try await apiClient.request(
            endpoint: APIEndpoints.Wallet.balance, // Use consistent endpoint
            method: .GET,
            responseType: WalletBalance.self
        )
    }
    
    // MARK: - Get Transaction Statistics
    func getTransactionStats() async throws -> TransactionStats {
        return try await apiClient.request(
            endpoint: APIEndpoints.Analytics.transactionStats,
            method: .GET,
            responseType: TransactionStats.self
        )
    }
}



// MARK: - Transaction Statistics Model
struct TransactionStats: Codable {
    let totalEarned: Double
    let totalRedeemed: Double
    let totalTransferred: Double
    let transactionCount: Int
    let averageTransaction: Double
    let monthlyStats: [MonthlyTransactionStats]
    
    var netBalance: Double {
        return totalEarned - totalRedeemed - totalTransferred
    }
    
    var displayTotalEarned: String {
        return totalEarned.formatAsToken()
    }
    
    var displayTotalRedeemed: String {
        return totalRedeemed.formatAsToken()
    }
    
    var displayNetBalance: String {
        return netBalance.formatAsToken()
    }
}

// MARK: - Monthly Transaction Stats
struct MonthlyTransactionStats: Codable, Identifiable {
    let id = UUID()
    let month: String
    let year: Int
    let totalEarned: Double
    let totalRedeemed: Double
    let transactionCount: Int
    
    private enum CodingKeys: String, CodingKey {
        case month, year, totalEarned, totalRedeemed, transactionCount
    }
    
    var displayMonth: String {
        return "\(month) \(year)"
    }
    
    var netAmount: Double {
        return totalEarned - totalRedeemed
    }
}

// MARK: - Transaction Manager
@MainActor
class TransactionManager: ObservableObject {
    static let shared = TransactionManager()

    @Published var transactions: [Transaction] = []
    @Published var balance: WalletBalance?
    @Published var stats: TransactionStats?
    @Published var isLoading = false
    @Published var error: Error?

    private let repository: TransactionRepositoryProtocol
    private var currentPage = 1
    private var hasMorePages = true

    init(repository: TransactionRepositoryProtocol = TransactionRepository()) {
        self.repository = repository
    }
    
    // MARK: - Load Transactions
    func loadTransactions(refresh: Bool = false) async {
        if refresh {
            currentPage = 1
            hasMorePages = true
            transactions.removeAll()
        }
        
        guard hasMorePages && !isLoading else { return }
        
        isLoading = true
        error = nil
        
        do {
            let response = try await repository.getTransactions(
                page: currentPage,
                limit: AppConstants.Pagination.defaultLimit,
                type: nil
            )
            
            if refresh {
                transactions = response.transactions
            } else {
                transactions.append(contentsOf: response.transactions)
            }
            
            hasMorePages = response.hasMore
            currentPage += 1
            
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    
    // MARK: - Load Balance
    func loadBalance() async {
        do {
            balance = try await repository.getBalance()
        } catch {
            self.error = error
        }
    }
    
    // MARK: - Load Statistics
    func loadStats() async {
        do {
            stats = try await repository.getTransactionStats()
        } catch {
            self.error = error
        }
    }
    
    // MARK: - Earn Tokens
    func earnTokens(merchantId: String, amountVnd: Double, description: String? = nil) async -> Bool {
        isLoading = true
        error = nil
        
        do {
            let request = EarnTokensRequest(
                merchantId: merchantId,
                amountVnd: amountVnd,
                description: description
            )
            
            let transaction = try await repository.earnTokens(request: request)
            
            // Add new transaction to the beginning of the list
            transactions.insert(transaction, at: 0)
            
            // Refresh balance
            await loadBalance()
            
            isLoading = false
            return true
            
        } catch {
            self.error = error
            isLoading = false
            return false
        }
    }
    
    // MARK: - Transfer Tokens
    func transferTokens(recipientAddress: String, amountLxt: Double, description: String? = nil) async -> Bool {
        isLoading = true
        error = nil
        
        do {
            let request = TransferTokensRequest(
                recipientAddress: recipientAddress,
                amountLxt: amountLxt,
                description: description
            )
            
            let transaction = try await repository.transferTokens(request: request)
            
            // Add new transaction to the beginning of the list
            transactions.insert(transaction, at: 0)
            
            // Refresh balance
            await loadBalance()
            
            isLoading = false
            return true
            
        } catch {
            self.error = error
            isLoading = false
            return false
        }
    }
    
    // MARK: - Refresh All Data
    func refreshAll() async {
        await loadTransactions(refresh: true)
        await loadBalance()
        await loadStats()
    }
    
    // MARK: - Filter Transactions
    func filterTransactions(by type: TransactionType?) -> [Transaction] {
        guard let type = type else { return transactions }
        return transactions.filter { $0.type == type }
    }
    
    // MARK: - Get Transaction by ID
    func getTransaction(id: String) -> Transaction? {
        return transactions.first { $0.id == id }
    }
    
    // MARK: - Clear Error
    func clearError() {
        error = nil
    }
}

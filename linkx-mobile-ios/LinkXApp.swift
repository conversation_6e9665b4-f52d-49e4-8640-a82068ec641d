//
//  LinkXApp.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import UserNotifications

@main
struct LinkXApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var configService = ConfigurationService.shared
    @StateObject private var notificationService = NotificationService.shared
    @StateObject private var syncService = SyncService.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView(authViewModel: authViewModel)
                .environmentObject(configService)
                .environmentObject(notificationService)
                .environmentObject(syncService)
                .onAppear {
                    setupApp()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                    handleAppBecomeActive()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
                    handleAppEnterBackground()
                }
                .errorAlert()
        }
    }
    
    private func setupApp() {
        // Load configuration
        Task {
            await configService.loadConfiguration()
        }

        // Setup analytics
        AnalyticsService.shared.startNewSession()

        // Track app launch
        let launchTime = Date().timeIntervalSince(appDelegate.launchTime)
        AnalyticsService.shared.trackAppLaunch(launchTime: launchTime)

        // Request notification permissions if needed
        Task {
            await NotificationService.shared.requestPermission()
        }

        // Start sync service if user is authenticated
        if authViewModel.isAuthenticated {
            syncService.startPeriodicSync()
        }
    }
    
    private func handleAppBecomeActive() {
        // Update badge count
        NotificationService.shared.updateBadgeCount()
        
        // Resume sync if authenticated
        if authViewModel.isAuthenticated {
            syncService.startPeriodicSync()
        }
        
        // Track app foreground
        AnalyticsService.shared.track(event: "app_foreground")
    }
    
    private func handleAppEnterBackground() {
        // Stop sync service
        syncService.stopPeriodicSync()
        
        // Track app background
        AnalyticsService.shared.track(event: "app_background")
    }
}

// MARK: - App Delegate
class AppDelegate: NSObject, UIApplicationDelegate {
    let launchTime = Date()
    
    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        // Setup logging
        Logger.shared.info("App launched", category: .general)
        
        // Setup appearance
        setupAppearance()
        
        // Register for remote notifications
        // TODO: Enable when push notifications are properly configured
        // application.registerForRemoteNotifications()
        
        return true
    }
    
    // MARK: - Remote Notifications
    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        NotificationService.shared.setDeviceToken(deviceToken)
    }
    
    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        Logger.shared.error("Failed to register for remote notifications", error: error, category: .general)
    }
    
    // MARK: - Background App Refresh
    func application(
        _ application: UIApplication,
        performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        Task {
            await SyncService.shared.performBackgroundSync()
            completionHandler(.newData)
        }
    }
    
    // MARK: - App Lifecycle
    func applicationWillTerminate(_ application: UIApplication) {
        Logger.shared.info("App will terminate", category: .general)
        AnalyticsService.shared.endSession()
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        Logger.shared.info("App entered background", category: .general)
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        Logger.shared.info("App will enter foreground", category: .general)
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        Logger.shared.info("App became active", category: .general)
    }
    
    // MARK: - Setup
    private func setupAppearance() {
        // Configure navigation bar appearance
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithOpaqueBackground()
        navBarAppearance.backgroundColor = UIColor(AppConstants.Colors.surface)
        navBarAppearance.titleTextAttributes = [
            .foregroundColor: UIColor(AppConstants.Colors.textPrimary)
        ]
        
        UINavigationBar.appearance().standardAppearance = navBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
        
        // Hide default tab bar since we're using custom modern tab bar
        UITabBar.appearance().isHidden = true
        
        // Configure tint colors
        UIView.appearance().tintColor = UIColor(AppConstants.Colors.primary)
    }
}

// MARK: - Content View
struct ContentView: View {
    @ObservedObject var authViewModel: AuthViewModel
    @EnvironmentObject var configService: ConfigurationService
    @State private var showMaintenanceView = false
    @State private var showUpdateView = false
    @State private var isInitializing = true
    @State private var forceRefresh = false

    init(authViewModel: AuthViewModel) {
        self.authViewModel = authViewModel
    }

    var body: some View {
        Group {
            if isInitializing {
                // Show loading screen while checking auth state
                LoadingView()
                    .onAppear {
                        print("🔐 ContentView: Showing LoadingView - checking auth state")
                        print("🔐 ContentView: Current state - isInitializing: \(isInitializing), isAuthenticated: \(authViewModel.isAuthenticated), currentUser: \(authViewModel.currentUser?.displayName ?? "nil")")
                    }
            } else if showMaintenanceView {
                MaintenanceView()
                    .onAppear {
                        print("🔐 ContentView: Showing MaintenanceView")
                    }
            } else if showUpdateView {
                UpdateRequiredView()
                    .onAppear {
                        print("🔐 ContentView: Showing UpdateRequiredView")
                    }
            } else if authViewModel.isAuthenticated {
                RoleBasedTabView()
                    .environmentObject(authViewModel)
                    .onAppear {
                        print("🔐 ContentView: Showing RoleBasedTabView - user is authenticated")
                        print("🔐 ContentView: Current state - isInitializing: \(isInitializing), isAuthenticated: \(authViewModel.isAuthenticated), currentUser: \(authViewModel.currentUser?.displayName ?? "nil")")
                    }
            } else {
                AuthenticationView()
                    .environmentObject(authViewModel)
                    .onAppear {
                        print("🔐 ContentView: Showing AuthenticationView - user is NOT authenticated")
                        print("🔐 ContentView: Current state - isInitializing: \(isInitializing), isAuthenticated: \(authViewModel.isAuthenticated), currentUser: \(authViewModel.currentUser?.displayName ?? "nil")")
                    }
            }
        }
        .onReceive(authViewModel.$isAuthenticated) { isAuth in
            print("🔐 ContentView: Received isAuthenticated change = \(isAuth)")
            // Stop showing loading after auth state is determined
            // Use a small delay to ensure state is stable
            if isInitializing {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isInitializing = false
                    print("🔐 ContentView: Auth state initialized, hiding loading")
                }
            }
            // Force UI update when authentication state changes
            DispatchQueue.main.async {
                forceRefresh.toggle()
                print("🔐 ContentView: Forced UI refresh, forceRefresh = \(forceRefresh)")
            }
        }
        .onReceive(authViewModel.$currentUser) { user in
            print("🔐 ContentView: Received currentUser change = \(user?.displayName ?? "nil")")
        }
        .onReceive(configService.$maintenanceMode) { maintenance in
            showMaintenanceView = maintenance?.isCurrentlyActive == true &&
                                 maintenance?.isVersionAllowed == false
        }
        .onReceive(configService.$appConfig) { config in
            showUpdateView = config?.isForceUpdateRequired == true
        }
    }
}

// MARK: - Maintenance View
struct MaintenanceView: View {
    @EnvironmentObject var configService: ConfigurationService
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "wrench.and.screwdriver.fill")
                .font(.system(size: 80))
                .foregroundColor(AppConstants.Colors.warning)
            
            VStack(spacing: 16) {
                Text(configService.maintenanceMode?.title ?? "Maintenance Mode")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                
                Text(configService.maintenanceMode?.message ?? "We're currently performing maintenance. Please try again later.")
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            if let endTime = configService.maintenanceMode?.endTime {
                Text("Expected completion: \(endTime.formatted(date: .abbreviated, time: .shortened))")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textTertiary)
            }
            
            Spacer()
            
            Button("Retry") {
                Task {
                    await configService.loadConfiguration()
                }
            }
            .primaryButtonStyle()
            .padding(.horizontal, 32)
        }
        .background(AppConstants.Colors.background.ignoresSafeArea())
    }
}

// MARK: - Update Required View
struct UpdateRequiredView: View {
    @EnvironmentObject var configService: ConfigurationService
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "arrow.clockwise.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(AppConstants.Colors.primary)
            
            VStack(spacing: 16) {
                Text("Update Required")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("A new version of LinkX is required to continue. Please update to the latest version from the App Store.")
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            Spacer()
            
            Button("Update Now") {
                if let url = URL(string: AppConstants.URLs.appStore) {
                    UIApplication.shared.open(url)
                }
            }
            .primaryButtonStyle()
            .padding(.horizontal, 32)
        }
        .background(AppConstants.Colors.background.ignoresSafeArea())
    }
}

// MARK: - Main Tab View
struct MainTabView: View {
    @StateObject private var notificationsViewModel = NotificationsViewModel()

    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Home", icon: "house.circle", selectedIcon: "house.circle.fill"),
            TabItem(id: 1, title: "Wallet", icon: "creditcard.circle", selectedIcon: "creditcard.circle.fill"),
            TabItem(id: 2, title: "Rewards", icon: "star.circle", selectedIcon: "star.circle.fill"),
            TabItem(id: 3, title: "History", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 4, title: "Profile", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }

    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            Group {
                switch selectedTab {
                case 0:
                    HomeView()
                        .environmentObject(notificationsViewModel)
                case 1:
                    WalletView()
                case 2:
                    RewardsView()
                case 3:
                    TransactionHistoryView()
                case 4:
                    ProfileView()
                default:
                    HomeView()
                        .environmentObject(notificationsViewModel)
                }
            }
        }
    }
}

// MARK: - Authentication View
struct AuthenticationView: View {
    var body: some View {
        NavigationView {
            LoginView()
        }
    }
}

// MARK: - Loading View
struct LoadingView: View {
    var body: some View {
        ZStack {
            AppConstants.Colors.background
                .ignoresSafeArea()

            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(AppConstants.Colors.primary)

                Text("LinkX")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.primary)

                Text("Loading...")
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
    }
}

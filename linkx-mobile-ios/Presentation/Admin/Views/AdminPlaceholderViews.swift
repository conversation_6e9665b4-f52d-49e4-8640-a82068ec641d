//
//  AdminPlaceholderViews.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

// MARK: - Admin Users View
struct AdminUsersView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var adminViewModel = AdminViewModel()
    @State private var searchText = ""
    @State private var selectedRole: String?
    @State private var selectedStatus: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and filters
                searchAndFiltersSection
                
                // Users list
                usersListSection
            }
            .navigationTitle("Manage Users")
            .navigationBarTitleDisplayMode(.large)
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .task {
            await adminViewModel.loadUsers()
        }
    }
    
    private var searchAndFiltersSection: some View {
        VStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Search users...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            
            // Filter buttons
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    AdminFilterButton(title: "All", isSelected: selectedRole == nil) {
                        selectedRole = nil
                    }

                    AdminFilterButton(title: "Users", isSelected: selectedRole == "USER") {
                        selectedRole = "USER"
                    }

                    AdminFilterButton(title: "Merchants", isSelected: selectedRole == "MERCHANT") {
                        selectedRole = "MERCHANT"
                    }

                    AdminFilterButton(title: "Admins", isSelected: selectedRole == "ADMIN") {
                        selectedRole = "ADMIN"
                    }
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(AppConstants.Colors.background)
    }
    
    private var usersListSection: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(filteredUsers, id: \.id) { user in
                    AdminUserRow(user: user) { userId, isActive in
                        Task {
                            await adminViewModel.updateUserStatus(userId: userId, isActive: isActive)
                        }
                    }
                }
                
                if adminViewModel.isLoading {
                    ProgressView()
                        .padding()
                }
                
                if filteredUsers.isEmpty && !adminViewModel.isLoading {
                    Text("No users found")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 40)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private var filteredUsers: [User] {
        var users = adminViewModel.users
        
        if let role = selectedRole {
            users = users.filter { $0.role.rawValue == role }
        }
        
        if !searchText.isEmpty {
            users = users.filter { user in
                user.displayName.localizedCaseInsensitiveContains(searchText) ||
                user.email.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return users
    }
}

// MARK: - Admin Merchants View
struct AdminMerchantsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var adminViewModel = AdminViewModel()
    @State private var selectedStatus: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Status filters
                statusFiltersSection
                
                // Merchants list
                merchantsListSection
            }
            .navigationTitle("Manage Merchants")
            .navigationBarTitleDisplayMode(.large)
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .task {
            await adminViewModel.loadMerchantUsers()
        }
    }
    
    private var statusFiltersSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                AdminFilterButton(title: "All", isSelected: selectedStatus == nil) {
                    selectedStatus = nil
                }

                AdminFilterButton(title: "Pending", isSelected: selectedStatus == "PENDING") {
                    selectedStatus = "PENDING"
                }

                AdminFilterButton(title: "Active", isSelected: selectedStatus == "ACTIVE") {
                    selectedStatus = "ACTIVE"
                }

                AdminFilterButton(title: "Suspended", isSelected: selectedStatus == "SUSPENDED") {
                    selectedStatus = "SUSPENDED"
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
        .background(AppConstants.Colors.background)
    }
    
    private var merchantsListSection: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(filteredMerchants, id: \.id) { merchant in
                    AdminMerchantRow(
                        merchantUser: merchant,
                        onApprove: { userId in
                            Task {
                                await adminViewModel.approveMerchantUser(userId: userId)
                            }
                        },
                        onSuspend: { userId in
                            Task {
                                await adminViewModel.suspendMerchantUser(userId: userId)
                            }
                        }
                    )
                }
                
                if adminViewModel.isLoading {
                    ProgressView()
                        .padding()
                }
                
                if filteredMerchants.isEmpty && !adminViewModel.isLoading {
                    Text("No merchants found")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 40)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private var filteredMerchants: [MerchantUser] {
        var merchants = adminViewModel.merchantUsers
        
        if let status = selectedStatus {
            merchants = merchants.filter { $0.businessInfo?.status == status }
        }
        
        return merchants
    }
}

// MARK: - Admin Rewards View
struct AdminRewardsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var rewardManager = RewardManager()
    @State private var showCreateReward = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(rewardManager.rewards, id: \.id) { reward in
                        AdminRewardRow(reward: reward)
                    }
                    
                    if rewardManager.isLoading {
                        ProgressView()
                            .padding()
                    }
                    
                    if rewardManager.rewards.isEmpty && !rewardManager.isLoading {
                        Text("No rewards found")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .padding(.vertical, 40)
                    }
                }
                .padding(.horizontal, 16)
            }
            .navigationTitle("Manage Rewards")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showCreateReward = true
                    }) {
                        Image(systemName: "plus")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .task {
            await rewardManager.loadRewards(refresh: true)
        }
        .sheet(isPresented: $showCreateReward) {
            CreateRewardPlaceholderView()
        }
    }
}

// MARK: - Supporting Components
struct AdminFilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? AppConstants.Colors.primary : Color.white)
                )
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AdminRewardRow: View {
    let reward: Reward
    
    var body: some View {
        HStack(spacing: 12) {
            AsyncImage(url: URL(string: reward.imageUrl ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppConstants.Colors.primary.opacity(0.1))
                    .overlay(
                        Image(systemName: "star.fill")
                            .foregroundColor(AppConstants.Colors.primary)
                    )
            }
            .frame(width: 50, height: 50)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            VStack(alignment: .leading, spacing: 4) {
                Text(reward.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(reward.displayTokenCost)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                HStack {
                    Text(reward.category.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(.blue.opacity(0.1))
                        )
                    
                    Spacer()
                    
                    Text(reward.isActive ? "Active" : "Inactive")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(reward.isActive ? .green : .red)
                }
            }
            
            Spacer()
        }
        .padding(12)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 1)
    }
}

struct CreateRewardView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Create Reward")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding()
                
                Text("Reward creation form will be implemented here")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
            }
            .navigationTitle("New Reward")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Save reward
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// MARK: - Create Reward Placeholder View
struct CreateRewardPlaceholderView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "gift.fill")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)

                Text("Create New Reward")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("This feature will allow admins to create new rewards for users to redeem.")
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)

                Spacer()
            }
            .padding()
            .navigationTitle("Create Reward")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

//
//  AuthViewModel.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import SwiftUI
import Combine

@MainActor
class AuthViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showError = false
    
    // Login form
    @Published var loginEmail = "<EMAIL>"  // Default for development
    @Published var loginPassword = "Customer123!"       // Default for development
    @Published var rememberMe = false
    
    // Register form
    @Published var registerEmail = ""
    @Published var registerPassword = ""
    @Published var confirmPassword = ""
    @Published var firstName = ""
    @Published var lastName = ""
    @Published var phone = ""
    @Published var acceptTerms = false
    
    // Biometric
    @Published var biometricEnabled = false
    @Published var biometricType = ""
    
    // MARK: - Dependencies
    private let authStateManager: AuthStateManager
    private let keychainManager = KeychainManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var isLoginFormValid: Bool {
        return loginEmail.isValidEmail && loginPassword.isValidPassword
    }
    
    var isRegisterFormValid: Bool {
        return registerEmail.isValidEmail &&
               registerPassword.isValidPassword &&
               confirmPassword == registerPassword &&
               !firstName.isEmpty &&
               !lastName.isEmpty &&
               acceptTerms
    }
    
    var passwordsMatch: Bool {
        return registerPassword == confirmPassword
    }
    
    // MARK: - Initialization
    init(authStateManager: AuthStateManager = AuthStateManager()) {
        self.authStateManager = authStateManager
        setupBindings()
        checkBiometricAvailability()
        loadSavedCredentials()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Bind auth state
        authStateManager.$isAuthenticated
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isAuth in
                print("🔐 AuthViewModel: Received isAuthenticated = \(isAuth)")
                self?.isAuthenticated = isAuth
            }
            .store(in: &cancellables)
        
        authStateManager.$currentUser
            .receive(on: DispatchQueue.main)
            .sink { [weak self] user in
                print("🔐 AuthViewModel: Received currentUser = \(String(describing: user))")
                if let user = user {
                    print("🔐 AuthViewModel: User details - ID: \(user.id), Role: \(user.role), Name: \(user.displayName)")
                }
                self?.currentUser = user
            }
            .store(in: &cancellables)
        
        authStateManager.$isLoading
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
    }
    
    private func checkBiometricAvailability() {
        biometricEnabled = keychainManager.isBiometricAvailable()
        biometricType = keychainManager.biometricTypeString()
    }
    
    private func loadSavedCredentials() {
        if rememberMe {
            // Load saved email if remember me was enabled
            loginEmail = UserDefaults.standard.string(forKey: "saved_email") ?? ""
        }
    }
    
    // MARK: - Authentication Actions
    func login() async {
        guard isLoginFormValid else {
            showErrorMessage("Please check your email and password")
            return
        }
        
        do {
            try await authStateManager.login(email: loginEmail, password: loginPassword)
            
            // Save credentials if remember me is enabled
            if rememberMe {
                UserDefaults.standard.set(loginEmail, forKey: "saved_email")
                
                // Optionally save to keychain for biometric login
                if biometricEnabled {
                    try? keychainManager.saveBiometric(
                        key: "saved_credentials",
                        string: "\(loginEmail):\(loginPassword)",
                        reason: "Save credentials for quick login"
                    )
                }
            }
            
            clearLoginForm()
            
        } catch {
            showErrorMessage(error.localizedDescription)
        }
    }
    
    func register() async {
        guard isRegisterFormValid else {
            showErrorMessage("Please fill in all required fields correctly")
            return
        }
        
        do {
            let request = RegisterRequest(
                email: registerEmail,
                password: registerPassword,
                phone: phone.isEmpty ? nil : phone,
                firstName: firstName,
                lastName: lastName
            )
            
            try await authStateManager.register(request: request)
            clearRegisterForm()
            
        } catch {
            showErrorMessage(error.localizedDescription)
        }
    }
    
    func loginWithBiometric() async {
        guard biometricEnabled else {
            showErrorMessage("Biometric authentication not available")
            return
        }
        
        do {
            let credentials = try await keychainManager.loadBiometricString(
                key: "saved_credentials",
                reason: "Login with \(biometricType)"
            )
            
            let components = credentials.components(separatedBy: ":")
            guard components.count == 2 else {
                showErrorMessage("Invalid saved credentials")
                return
            }
            
            loginEmail = components[0]
            loginPassword = components[1]
            
            await login()
            
        } catch {
            if let keychainError = error as? KeychainError {
                switch keychainError {
                case .userCancel:
                    // User cancelled, don't show error
                    break
                case .biometricLockout:
                    showErrorMessage("Biometric authentication is locked. Please use your passcode.")
                case .authenticationFailed:
                    showErrorMessage("Biometric authentication failed")
                default:
                    showErrorMessage(keychainError.localizedDescription)
                }
            } else {
                showErrorMessage("Biometric login failed")
            }
        }
    }
    
    func logout() async {
        await authStateManager.logout()
        clearAllForms()
        
        // Clear saved credentials if needed
        if !rememberMe {
            UserDefaults.standard.removeObject(forKey: "saved_email")
            try? keychainManager.delete(key: "saved_credentials")
        }
    }
    
    func refreshUserData() async {
        await authStateManager.refreshUserData()
    }
    
    // MARK: - Form Management
    func clearLoginForm() {
        loginPassword = ""
        if !rememberMe {
            loginEmail = ""
        }
    }
    
    func clearRegisterForm() {
        registerEmail = ""
        registerPassword = ""
        confirmPassword = ""
        firstName = ""
        lastName = ""
        phone = ""
        acceptTerms = false
    }
    
    func clearAllForms() {
        clearLoginForm()
        clearRegisterForm()
        loginEmail = ""
    }
    
    // MARK: - Error Handling
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
        
        // Auto-hide error after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.hideError()
        }
    }
    
    func hideError() {
        showError = false
        errorMessage = nil
    }
    
    // MARK: - Validation Helpers
    func validateEmail(_ email: String) -> String? {
        if email.isEmpty {
            return "Email is required"
        } else if !email.isValidEmail {
            return "Please enter a valid email address"
        }
        return nil
    }
    
    func validatePassword(_ password: String) -> String? {
        if password.isEmpty {
            return "Password is required"
        } else if !password.isValidPassword {
            return "Password must be at least \(AppConstants.Validation.minPasswordLength) characters"
        }
        return nil
    }
    
    func validateConfirmPassword() -> String? {
        if confirmPassword.isEmpty {
            return "Please confirm your password"
        } else if !passwordsMatch {
            return "Passwords do not match"
        }
        return nil
    }
    
    func validateName(_ name: String, fieldName: String) -> String? {
        if name.isEmpty {
            return "\(fieldName) is required"
        } else if name.count < AppConstants.Validation.minNameLength {
            return "\(fieldName) must be at least \(AppConstants.Validation.minNameLength) characters"
        }
        return nil
    }
    
    func validatePhone(_ phone: String) -> String? {
        if !phone.isEmpty && !phone.isValidPhone {
            return "Please enter a valid phone number"
        }
        return nil
    }
    
    // MARK: - Biometric Settings
    func toggleBiometricLogin() {
        biometricEnabled.toggle()
        UserDefaults.standard.set(biometricEnabled, forKey: AppConstants.StorageKeys.biometricEnabled)
        
        if !biometricEnabled {
            // Clear saved biometric credentials
            try? keychainManager.delete(key: "saved_credentials")
        }
    }
    
    // MARK: - Social Login (Future Implementation)
    func loginWithGoogle() async {
        // TODO: Implement Google Sign-In
        showErrorMessage("Google Sign-In coming soon")
    }
    
    func loginWithApple() async {
        // TODO: Implement Apple Sign-In
        showErrorMessage("Apple Sign-In coming soon")
    }
    
    func loginWithFacebook() async {
        // TODO: Implement Facebook Login
        showErrorMessage("Facebook Login coming soon")
    }
}

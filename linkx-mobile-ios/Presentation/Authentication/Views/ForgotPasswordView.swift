//
//  ForgotPasswordView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var email = ""
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var errorMessage: String?
    @State private var showError = false
    @FocusState private var isEmailFocused: Bool
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // Form
                    formSection
                    
                    // Reset Button
                    resetButtonSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 40)
            }
            .navigationTitle("Reset Password")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Password reset instructions have been sent to your email address.")
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = nil
            }
        } message: {
            Text(errorMessage ?? "An error occurred")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "lock.rotation")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.primary)
            
            Text("Forgot Password?")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Enter your email address and we'll send you instructions to reset your password.")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
        }
    }
    
    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: AppConstants.UI.itemSpacing) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Email Address")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                TextField("Enter your email", text: $email)
                    .textFieldStyle()
                    .keyboardType(.emailAddress)
                    .textContentType(.emailAddress)
                    .autocapitalization(.none)
                    .focused($isEmailFocused)
                    .onSubmit {
                        Task {
                            await resetPassword()
                        }
                    }
                
                if !email.isEmpty && !email.isValidEmail {
                    Text("Please enter a valid email address")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
        }
        .cardStyle()
        .padding(.horizontal, 4)
    }
    
    // MARK: - Reset Button Section
    private var resetButtonSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                Task {
                    await resetPassword()
                }
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    
                    Text(isLoading ? "Sending..." : "Send Reset Instructions")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
            }
            .primaryButtonStyle()
            .disabled(!email.isValidEmail || isLoading)
            .opacity(email.isValidEmail ? 1.0 : 0.6)
            
            // Back to Login
            Button("Back to Sign In") {
                dismiss()
            }
            .font(.subheadline)
            .foregroundColor(AppConstants.Colors.primary)
        }
    }
    
    // MARK: - Reset Password Action
    private func resetPassword() async {
        guard email.isValidEmail else {
            showErrorMessage("Please enter a valid email address")
            return
        }
        
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            
            // TODO: Implement actual password reset API call
            // let response = try await APIClient.shared.request(
            //     endpoint: APIEndpoints.Auth.forgotPassword,
            //     method: .POST,
            //     parameters: ["email": email],
            //     responseType: EmptyResponse.self
            // )
            
            showSuccess = true
            
        } catch {
            showErrorMessage(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    // MARK: - Error Handling
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - Preview
#Preview {
    ForgotPasswordView()
}

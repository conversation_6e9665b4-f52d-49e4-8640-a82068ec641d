//
//  BadgeView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

struct BadgeView: View {
    let count: Int
    let maxCount: Int
    let backgroundColor: Color
    let textColor: Color
    let size: BadgeSize
    
    enum BadgeSize {
        case small
        case medium
        case large
        
        var diameter: CGFloat {
            switch self {
            case .small: return 16
            case .medium: return 20
            case .large: return 24
            }
        }
        
        var fontSize: CGFloat {
            switch self {
            case .small: return 10
            case .medium: return 12
            case .large: return 14
            }
        }
        
        var padding: CGFloat {
            switch self {
            case .small: return 4
            case .medium: return 6
            case .large: return 8
            }
        }
    }
    
    init(
        count: Int,
        maxCount: Int = 99,
        backgroundColor: Color = .red,
        textColor: Color = .white,
        size: BadgeSize = .medium
    ) {
        self.count = count
        self.maxCount = maxCount
        self.backgroundColor = backgroundColor
        self.textColor = textColor
        self.size = size
    }
    
    var body: some View {
        if count > 0 {
            Text(displayText)
                .font(.system(size: size.fontSize, weight: .bold))
                .foregroundColor(textColor)
                .padding(.horizontal, size.padding)
                .padding(.vertical, size.padding / 2)
                .background(backgroundColor)
                .clipShape(Capsule())
                .overlay(
                    Capsule()
                        .stroke(Color.white, lineWidth: 1)
                )
                .shadow(color: backgroundColor.opacity(0.3), radius: 2, x: 0, y: 1)
                .scaleEffect(1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: count)
        }
    }
    
    private var displayText: String {
        if count > maxCount {
            return "\(maxCount)+"
        } else {
            return "\(count)"
        }
    }
}

struct NotificationBadgeView: View {
    let count: Int
    
    var body: some View {
        BadgeView(
            count: count,
            backgroundColor: AppConstants.Colors.error,
            textColor: .white,
            size: .small
        )
    }
}

struct IconWithBadge: View {
    let iconName: String
    let badgeCount: Int
    let iconColor: Color
    let iconSize: CGFloat
    
    init(
        iconName: String,
        badgeCount: Int = 0,
        iconColor: Color = AppConstants.Colors.textPrimary,
        iconSize: CGFloat = 24
    ) {
        self.iconName = iconName
        self.badgeCount = badgeCount
        self.iconColor = iconColor
        self.iconSize = iconSize
    }
    
    var body: some View {
        ZStack(alignment: .topTrailing) {
            Image(systemName: iconName)
                .font(.system(size: iconSize))
                .foregroundColor(iconColor)
            
            if badgeCount > 0 {
                NotificationBadgeView(count: badgeCount)
                    .offset(x: 8, y: -8)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        HStack(spacing: 20) {
            Text("Small")
            BadgeView(count: 5, size: .small)
            BadgeView(count: 99, size: .small)
            BadgeView(count: 100, size: .small)
        }
        
        HStack(spacing: 20) {
            Text("Medium")
            BadgeView(count: 5, size: .medium)
            BadgeView(count: 99, size: .medium)
            BadgeView(count: 100, size: .medium)
        }
        
        HStack(spacing: 20) {
            Text("Large")
            BadgeView(count: 5, size: .large)
            BadgeView(count: 99, size: .large)
            BadgeView(count: 100, size: .large)
        }
        
        Divider()
        
        HStack(spacing: 30) {
            IconWithBadge(iconName: "bell", badgeCount: 0)
            IconWithBadge(iconName: "bell", badgeCount: 3)
            IconWithBadge(iconName: "bell", badgeCount: 99)
            IconWithBadge(iconName: "bell", badgeCount: 100)
        }
        
        HStack(spacing: 30) {
            IconWithBadge(iconName: "message", badgeCount: 1)
            IconWithBadge(iconName: "cart", badgeCount: 5)
            IconWithBadge(iconName: "heart", badgeCount: 12)
        }
    }
    .padding()
    .background(AppConstants.Colors.background)
}

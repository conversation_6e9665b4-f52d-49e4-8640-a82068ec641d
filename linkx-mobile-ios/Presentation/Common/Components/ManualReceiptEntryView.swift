//
//  ManualReceiptEntryView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 22/7/25.
//

import SwiftUI

struct ManualReceiptEntryView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var merchantName = ""
    @State private var totalAmount = ""
    @State private var receiptDate = Date()
    @State private var notes = ""
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @FocusState private var focusedField: Field?
    
    let onReceiptSubmitted: (ReceiptData) -> Void
    
    enum Field {
        case merchantName, totalAmount, notes
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "receipt")
                            .font(.system(size: 50))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Enter Receipt Details")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Manually enter your receipt information to earn LinkX tokens")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Form
                    VStack(spacing: AppConstants.UI.itemSpacing) {
                        // Merchant Name
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Merchant Name")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Enter merchant name", text: $merchantName)
                                .textFieldStyle()
                                .focused($focusedField, equals: .merchantName)
                                .onSubmit {
                                    focusedField = .totalAmount
                                }
                        }
                        
                        // Total Amount
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Total Amount (VND)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Enter total amount", text: $totalAmount)
                                .textFieldStyle()
                                .keyboardType(.decimalPad)
                                .focused($focusedField, equals: .totalAmount)
                                .onSubmit {
                                    focusedField = .notes
                                }
                        }
                        
                        // Receipt Date
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Receipt Date")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            DatePicker("Select date", selection: $receiptDate, displayedComponents: [.date])
                                .datePickerStyle(.compact)
                                .accentColor(AppConstants.Colors.primary)
                        }
                        
                        // Notes (Optional)
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Notes (Optional)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Add any additional notes", text: $notes, axis: .vertical)
                                .textFieldStyle()
                                .lineLimit(3...6)
                                .focused($focusedField, equals: .notes)
                        }
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                    
                    // Submit Button
                    Button(action: {
                        submitReceipt()
                    }) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            } else {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.title3)
                            }
                            
                            Text(isLoading ? "Processing..." : "Submit Receipt")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            LinearGradient(
                                colors: [AppConstants.Colors.primary, AppConstants.Colors.primary.opacity(0.8)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                        .disabled(isLoading || !isFormValid)
                        .opacity(isFormValid ? 1.0 : 0.6)
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                    .padding(.top, 20)
                    
                    Spacer(minLength: 50)
                }
            }
            .navigationTitle("Manual Entry")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .onTapGesture {
            // Dismiss keyboard when tapping outside
            focusedField = nil
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !merchantName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !totalAmount.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        Double(totalAmount) != nil &&
        Double(totalAmount) ?? 0 > 0
    }
    
    // MARK: - Helper Methods
    private func submitReceipt() {
        guard isFormValid else { return }
        
        isLoading = true
        
        // Create receipt data
        let amount = Double(totalAmount) ?? 0.0
        let receiptData = ReceiptData(
            id: "manual-\(UUID().uuidString)",
            merchantName: merchantName.trimmingCharacters(in: .whitespacesAndNewlines),
            merchantAddress: nil,
            date: receiptDate,
            totalAmount: amount,
            subtotal: nil,
            taxAmount: nil,
            items: [], // No items for manual entry
            paymentMethod: nil,
            receiptNumber: nil,
            confidence: 1.0, // Manual entry has 100% confidence
            estimatedTokens: amount * 0.0001 // 0.01% of VND amount as tokens
        )
        
        // Simulate processing delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
            onReceiptSubmitted(receiptData)
        }
    }
}



// MARK: - Preview
#Preview {
    ManualReceiptEntryView { receiptData in
        print("Receipt submitted: \(receiptData)")
    }
}

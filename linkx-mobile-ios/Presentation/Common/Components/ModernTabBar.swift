import SwiftUI

// MARK: - Tab Item Model
struct TabItem {
    let id: Int
    let title: String
    let icon: String
    let selectedIcon: String
    let badgeCount: Int

    init(id: Int, title: String, icon: String, selectedIcon: String? = nil, badgeCount: Int = 0) {
        self.id = id
        self.title = title
        self.icon = icon
        self.selectedIcon = selectedIcon ?? icon
        self.badgeCount = badgeCount
    }
}

// MARK: - Modern Tab Bar
struct ModernTabBar: View {
    @Binding var selectedTab: Int
    let tabs: [TabItem]

    @State private var tabFrames: [CGRect] = []
    @Namespace private var tabSelection

    var body: some View {
        ZStack {
            // Background with enhanced blur effect
            RoundedRectangle(cornerRadius: 32)
                .fill(.ultraThinMaterial)
                .overlay(
                    // Inner glow effect
                    RoundedRectangle(cornerRadius: 32)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white.opacity(0.4),
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )
                .shadow(color: Color.black.opacity(0.08), radius: 25, x: 0, y: 12)
                .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
                .frame(height: 92)

            // Tab items
            HStack(spacing: 0) {
                ForEach(tabs, id: \.id) { tab in
                    ModernTabItem(
                        tab: tab,
                        isSelected: selectedTab == tab.id,
                        namespace: tabSelection
                    ) {
                        // Haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()

                        withAnimation(.spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0.2)) {
                            selectedTab = tab.id
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .padding(.horizontal, 16)
    }
}

// MARK: - Modern Tab Item
struct ModernTabItem: View {
    let tab: TabItem
    let isSelected: Bool
    let namespace: Namespace.ID
    let action: () -> Void

    @State private var isPressed = false
    @State private var bounceEffect = false

    var body: some View {
        Button(action: {
            // Trigger bounce animation
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                bounceEffect.toggle()
            }

            // Delay the action slightly for better UX
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                action()
            }
        }) {
            VStack(spacing: 6) {
                ZStack {
                    // Selection background with enhanced styling
                    if isSelected {
                        RoundedRectangle(cornerRadius: 18)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        AppConstants.Colors.primary.opacity(0.9),
                                        AppConstants.Colors.primaryDark
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 52, height: 52)
                            .matchedGeometryEffect(id: "selectedTab", in: namespace)
                            .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 12, x: 0, y: 6)
                            .shadow(color: AppConstants.Colors.primary.opacity(0.2), radius: 4, x: 0, y: 2)
                            .overlay(
                                // Inner highlight
                                RoundedRectangle(cornerRadius: 18)
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                    } else {
                        // Subtle background for unselected items
                        RoundedRectangle(cornerRadius: 18)
                            .fill(Color.clear)
                            .frame(width: 52, height: 52)
                    }

                    // Icon with enhanced animations and badge
                    ZStack(alignment: .topTrailing) {
                        Image(systemName: isSelected ? tab.selectedIcon : tab.icon)
                            .font(.system(size: 22, weight: .semibold))
                            .foregroundColor(isSelected ? .white : AppConstants.Colors.textSecondary)
                            .scaleEffect(isSelected ? 1.0 : 0.9)
                            .scaleEffect(bounceEffect ? 1.1 : 1.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isSelected)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: bounceEffect)

                        // Badge
                        if tab.badgeCount > 0 {
                            BadgeView(
                                count: tab.badgeCount,
                                backgroundColor: AppConstants.Colors.error,
                                textColor: .white,
                                size: .small
                            )
                            .offset(x: 8, y: -8)
                        }
                    }
                }
                .frame(width: 52, height: 52)

                // Label with improved styling
                Text(tab.title)
                    .font(.system(size: 12, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                    .scaleEffect(isSelected ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.25), value: isSelected)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.92 : 1.0)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.15)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Modern Tab View Container
struct ModernTabView<Content: View>: View {
    @State private var selectedTab = 0
    let tabs: [TabItem]
    let content: (Int) -> Content
    
    init(tabs: [TabItem], @ViewBuilder content: @escaping (Int) -> Content) {
        self.tabs = tabs
        self.content = content
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Content
                content(selectedTab)
                    .padding(.bottom, 92 + geometry.safeAreaInsets.bottom)

                // Modern Tab Bar
                VStack {
                    Spacer()
                    ModernTabBar(selectedTab: $selectedTab, tabs: tabs)
                        .padding(.bottom, geometry.safeAreaInsets.bottom)
                }
            }
        }
        .ignoresSafeArea(.container, edges: .bottom)
        .onChange(of: selectedTab) { _, newTab in
            // Track tab selection
            let tabNames = ["home", "wallet", "rewards", "history", "profile"]
            if newTab < tabNames.count {
                AnalyticsService.shared.trackScreenView(screenName: tabNames[newTab])
            }
        }
    }
}

// MARK: - Preview
#if DEBUG
struct ModernTabBar_Previews: PreviewProvider {
    static var previews: some View {
        let sampleTabs = [
            TabItem(id: 0, title: "Home", icon: "house.circle", selectedIcon: "house.circle.fill"),
            TabItem(id: 1, title: "Wallet", icon: "creditcard.circle", selectedIcon: "creditcard.circle.fill"),
            TabItem(id: 2, title: "Rewards", icon: "star.circle", selectedIcon: "star.circle.fill"),
            TabItem(id: 3, title: "History", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 4, title: "Profile", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]

        Group {
            // Light mode preview
            VStack {
                Spacer()
                ModernTabBar(selectedTab: .constant(0), tabs: sampleTabs)
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.98, green: 0.98, blue: 1.0),
                        Color(red: 0.95, green: 0.97, blue: 1.0)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .previewDevice("iPhone 16 Pro")
            .previewDisplayName("Light Mode")

            // Different selected tab
            VStack {
                Spacer()
                ModernTabBar(selectedTab: .constant(2), tabs: sampleTabs)
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.98, green: 0.98, blue: 1.0),
                        Color(red: 0.95, green: 0.97, blue: 1.0)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .previewDevice("iPhone 16 Pro")
            .previewDisplayName("Rewards Selected")
        }
    }
}
#endif

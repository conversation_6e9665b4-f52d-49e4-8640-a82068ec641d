//
//  QRScannerView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import AVFoundation
import CoreImage
import CoreImage.CIFilterBuiltins

struct QRScannerView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var scannedCode: String?
    @State private var showManualEntry = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var cameraPermissionGranted = false
    @State private var showPermissionAlert = false
    let onCodeScanned: ((String) -> Void)?

    init(onCodeScanned: ((String) -> Void)? = nil) {
        self.onCodeScanned = onCodeScanned
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Camera View
                if cameraPermissionGranted {
                    CameraPreviewView { code in
                        handleScannedCode(code)
                    }
                } else {
                    CameraPermissionView {
                        requestCameraPermission()
                    }
                }

                // Overlay
                VStack {
                    // Top Instructions
                    VStack(spacing: 8) {
                        Text("Scan QR Code")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Point your camera at the merchant's QR code")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 60)
                    .padding(.horizontal, 20)
                    
                    Spacer()
                    
                    // Scanning Frame
                    ScanningFrameView()
                    
                    Spacer()
                    
                    // Bottom Actions
                    VStack(spacing: 16) {
                        Button("Enter Code Manually") {
                            showManualEntry = true
                        }
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(.white.opacity(0.2))
                        .cornerRadius(20)
                        
                        Button("Cancel") {
                            dismiss()
                        }
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(.bottom, 40)
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showManualEntry) {
            ManualCodeEntryView()
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Camera Permission Required", isPresented: $showPermissionAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) {
                dismiss()
            }
        } message: {
            Text("Please allow camera access in Settings to scan QR codes.")
        }
        .onAppear {
            requestCameraPermission()
        }
    }

    // MARK: - Helper Methods
    private func requestCameraPermission() {
        let status = AVCaptureDevice.authorizationStatus(for: .video)

        switch status {
        case .authorized:
            DispatchQueue.main.async {
                self.cameraPermissionGranted = true
                print("✅ Camera permission already granted")
            }
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.cameraPermissionGranted = true
                        print("✅ Camera permission granted")
                    } else {
                        self.showPermissionAlert = true
                        print("❌ Camera permission denied")
                    }
                }
            }
        case .denied, .restricted:
            DispatchQueue.main.async {
                self.showPermissionAlert = true
                print("❌ Camera permission denied or restricted")
            }
        @unknown default:
            DispatchQueue.main.async {
                self.showPermissionAlert = true
                print("❌ Unknown camera permission status")
            }
        }
    }

    private func handleScannedCode(_ code: String) {
        // Prevent multiple scans
        guard scannedCode != code else { return }
        scannedCode = code

        HapticManager.shared.trigger(.success)

        // Call the callback if provided
        onCodeScanned?(code)

        // Dismiss after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            dismiss()
        }
    }
}

// MARK: - Camera Preview
struct CameraPreviewView: UIViewRepresentable {
    let onCodeScanned: (String) -> Void

    func makeUIView(context: Context) -> CameraPreview {
        let preview = CameraPreview()
        preview.delegate = context.coordinator
        return preview
    }

    func updateUIView(_ uiView: CameraPreview, context: Context) {
        // Ensure camera is running when view updates
        uiView.startScanning()
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(onCodeScanned: onCodeScanned)
    }

    static func dismantleUIView(_ uiView: CameraPreview, coordinator: Coordinator) {
        uiView.stopScanning()
    }

    class Coordinator: NSObject, AVCaptureMetadataOutputObjectsDelegate {
        let onCodeScanned: (String) -> Void

        init(onCodeScanned: @escaping (String) -> Void) {
            self.onCodeScanned = onCodeScanned
        }

        func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {
            if let metadataObject = metadataObjects.first {
                guard let readableObject = metadataObject as? AVMetadataMachineReadableCodeObject else { return }
                guard let stringValue = readableObject.stringValue else { return }

                DispatchQueue.main.async {
                    self.onCodeScanned(stringValue)
                }
            }
        }
    }
}

// MARK: - Camera Preview UIView
class CameraPreview: UIView {
    var captureSession: AVCaptureSession!
    var previewLayer: AVCaptureVideoPreviewLayer!
    weak var delegate: AVCaptureMetadataOutputObjectsDelegate?
    private var isSetup = false

    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .black // Set background to black to avoid white screen
        setupCamera()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        backgroundColor = .black
        setupCamera()
    }

    private func setupCamera() {
        captureSession = AVCaptureSession()

        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video) else {
            print("❌ Failed to get video capture device")
            return
        }

        let videoInput: AVCaptureDeviceInput

        do {
            videoInput = try AVCaptureDeviceInput(device: videoCaptureDevice)
        } catch {
            print("❌ Failed to create video input: \(error)")
            return
        }

        if captureSession.canAddInput(videoInput) {
            captureSession.addInput(videoInput)
        } else {
            print("❌ Cannot add video input to capture session")
            return
        }

        let metadataOutput = AVCaptureMetadataOutput()

        if captureSession.canAddOutput(metadataOutput) {
            captureSession.addOutput(metadataOutput)

            metadataOutput.setMetadataObjectsDelegate(delegate, queue: DispatchQueue.main)
            metadataOutput.metadataObjectTypes = [.qr]
        } else {
            print("❌ Cannot add metadata output to capture session")
            return
        }

        setupPreviewLayer()
    }

    private func setupPreviewLayer() {
        previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        previewLayer.frame = bounds
        previewLayer.videoGravity = .resizeAspectFill
        layer.addSublayer(previewLayer)

        print("✅ Preview layer setup complete")

        DispatchQueue.global(qos: .background).async {
            self.captureSession.startRunning()
            print("✅ Capture session started")
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        previewLayer?.frame = bounds
        print("📐 Layout updated - bounds: \(bounds)")
    }

    func startScanning() {
        guard let captureSession = captureSession else {
            print("❌ Capture session is nil")
            return
        }

        if !captureSession.isRunning {
            DispatchQueue.global(qos: .background).async {
                captureSession.startRunning()
                print("✅ Camera scanning started")
            }
        }
    }

    func stopScanning() {
        guard let captureSession = captureSession else { return }

        if captureSession.isRunning {
            captureSession.stopRunning()
            print("🛑 Camera scanning stopped")
        }
    }
}

// MARK: - Camera Permission View
struct CameraPermissionView: View {
    let onRequestPermission: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "camera.fill")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.6))

            Text("Camera Access Required")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text("Please allow camera access to scan QR codes")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            Button("Allow Camera Access") {
                onRequestPermission()
            }
            .font(.headline)
            .foregroundColor(.black)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(.white)
            .cornerRadius(25)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black)
    }
}

// MARK: - Scanning Frame
struct ScanningFrameView: View {
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        ZStack {
            // Frame corners
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.white, lineWidth: 3)
                .frame(width: 250, height: 250)
            
            // Corner indicators
            VStack {
                HStack {
                    CornerIndicator()
                    Spacer()
                    CornerIndicator()
                        .rotationEffect(.degrees(90))
                }
                Spacer()
                HStack {
                    CornerIndicator()
                        .rotationEffect(.degrees(-90))
                    Spacer()
                    CornerIndicator()
                        .rotationEffect(.degrees(180))
                }
            }
            .frame(width: 250, height: 250)
            
            // Scanning line animation
            Rectangle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [.clear, .white, .clear]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(width: 250, height: 2)
                .offset(y: animationOffset)
                .onAppear {
                    withAnimation(
                        Animation.linear(duration: 2)
                            .repeatForever(autoreverses: true)
                    ) {
                        animationOffset = 125
                    }
                }
        }
    }
}

// MARK: - Corner Indicator
struct CornerIndicator: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Rectangle()
                .fill(Color.white)
                .frame(width: 20, height: 3)
            
            Rectangle()
                .fill(Color.white)
                .frame(width: 3, height: 20)
        }
    }
}

// MARK: - Manual Code Entry
struct ManualCodeEntryView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var merchantCode = ""
    @State private var amount = ""
    @State private var isLoading = false
    @FocusState private var focusedField: ManualEntryField?
    
    enum ManualEntryField {
        case merchantCode, amount
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "keyboard")
                            .font(.system(size: 50))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Enter Merchant Code")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Enter the merchant code and purchase amount manually")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Form
                    VStack(spacing: AppConstants.UI.itemSpacing) {
                        // Merchant Code
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Merchant Code")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Enter merchant code", text: $merchantCode)
                                .textFieldStyle()
                                .textContentType(.none)
                                .autocapitalization(.allCharacters)
                                .focused($focusedField, equals: .merchantCode)
                                .onSubmit {
                                    focusedField = .amount
                                }
                        }
                        
                        // Amount
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Purchase Amount (VND)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Enter amount", text: $amount)
                                .textFieldStyle()
                                .keyboardType(.numberPad)
                                .focused($focusedField, equals: .amount)
                            
                            if let amountValue = Double(amount), amountValue > 0 {
                                let lxtAmount = amountValue / AppConstants.Transaction.exchangeRate
                                Text("≈ \(String(format: "%.2f", lxtAmount)) LXT")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                    }
                    .cardStyle()
                    .padding(.horizontal, 4)
                    
                    // Submit Button
                    Button(action: {
                        Task {
                            await processTransaction()
                        }
                    }) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            }
                            
                            Text(isLoading ? "Processing..." : "Earn Tokens")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .primaryButtonStyle()
                    .disabled(!isFormValid || isLoading)
                    .opacity(isFormValid ? 1.0 : 0.6)
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .navigationTitle("Manual Entry")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
        }
    }
    
    private var isFormValid: Bool {
        return !merchantCode.isEmpty && 
               !amount.isEmpty && 
               Double(amount) != nil && 
               Double(amount)! > 0
    }
    
    private func processTransaction() async {
        guard let amountValue = Double(amount) else { return }
        
        isLoading = true
        
        // Simulate API call
        try? await Task.sleep(nanoseconds: 2_000_000_000)
        
        // TODO: Implement actual transaction processing
        // let success = await transactionManager.earnTokens(
        //     merchantId: merchantCode,
        //     amountVnd: amountValue
        // )
        
        isLoading = false
        dismiss()
    }
}

// MARK: - Transfer Tokens View (Placeholder)
struct TransferTokensView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var recipientAddress = ""
    @State private var amount = ""
    @State private var note = ""
    @State private var isLoading = false
    @FocusState private var focusedField: TransferField?
    
    enum TransferField {
        case address, amount, note
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "arrow.up.circle")
                            .font(.system(size: 50))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Send Tokens")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Transfer LXT tokens to another wallet")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Form
                    VStack(spacing: AppConstants.UI.itemSpacing) {
                        // Recipient Address
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Recipient Address")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            HStack {
                                TextField("Enter wallet address", text: $recipientAddress)
                                    .textFieldStyle()
                                    .focused($focusedField, equals: .address)
                                    .onSubmit {
                                        focusedField = .amount
                                    }
                                
                                Button(action: {
                                    // TODO: Implement QR scanner for address
                                }) {
                                    Image(systemName: "qrcode.viewfinder")
                                        .font(.title2)
                                        .foregroundColor(AppConstants.Colors.primary)
                                }
                            }
                        }
                        
                        // Amount
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Amount (LXT)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Enter amount", text: $amount)
                                .textFieldStyle()
                                .keyboardType(.decimalPad)
                                .focused($focusedField, equals: .amount)
                                .onSubmit {
                                    focusedField = .note
                                }
                            
                            if let amountValue = Double(amount), amountValue > 0 {
                                let vndAmount = amountValue * AppConstants.Transaction.exchangeRate
                                Text("≈ \(String(format: "%.0f", vndAmount)) VND")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }
                        }
                        
                        // Note (Optional)
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Note (Optional)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Add a note", text: $note)
                                .textFieldStyle()
                                .focused($focusedField, equals: .note)
                        }
                    }
                    .cardStyle()
                    .padding(.horizontal, 4)
                    
                    // Send Button
                    Button(action: {
                        Task {
                            await sendTokens()
                        }
                    }) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            }
                            
                            Text(isLoading ? "Sending..." : "Send Tokens")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .primaryButtonStyle()
                    .disabled(!isFormValid || isLoading)
                    .opacity(isFormValid ? 1.0 : 0.6)
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .navigationTitle("Send Tokens")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
        }
    }
    
    private var isFormValid: Bool {
        return !recipientAddress.isEmpty && 
               !amount.isEmpty && 
               Double(amount) != nil && 
               Double(amount)! > 0
    }
    
    private func sendTokens() async {
        guard let amountValue = Double(amount) else { return }
        
        isLoading = true
        
        // Simulate API call
        try? await Task.sleep(nanoseconds: 2_000_000_000)
        
        // TODO: Implement actual token transfer
        // let success = await transactionManager.transferTokens(
        //     recipientAddress: recipientAddress,
        //     amountLxt: amountValue,
        //     description: note.isEmpty ? nil : note
        // )
        
        isLoading = false
        dismiss()
    }
}

// MARK: - Preview
#Preview {
    QRScannerView()
}

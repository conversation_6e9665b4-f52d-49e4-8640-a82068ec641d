//
//  ReceiptScannerView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 22/7/25.
//

import SwiftUI
import AVFoundation
import Foundation
import UIKit
import PhotosUI

struct ReceiptScannerView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var capturedImage: UIImage?
    @State private var showImagePicker = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var cameraPermissionGranted = false
    @State private var showPermissionAlert = false
    @State private var isProcessing = false
    @State private var showManualEntry = false
    @State private var selectedSource: ImageSource = .camera
    
    let onReceiptProcessed: ((ReceiptData) -> Void)?
    
    enum ImageSource {
        case camera
        case photoLibrary
    }
    
    init(onReceiptProcessed: ((ReceiptData) -> Void)? = nil) {
        self.onReceiptProcessed = onReceiptProcessed
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Camera View
                if cameraPermissionGranted && selectedSource == .camera {
                    ReceiptCameraPreviewView { image in
                        handleCapturedImage(image)
                    }
                } else if selectedSource == .camera {
                    ReceiptCameraPermissionView {
                        requestCameraPermission()
                    }
                } else {
                    // Photo library placeholder
                    Color.black
                        .ignoresSafeArea()
                }
                
                // Processing overlay
                if isProcessing {
                    ProcessingOverlay()
                }
                
                // Main overlay
                VStack {
                    // Top Instructions
                    VStack(spacing: 12) {
                        Text("Scan Receipt")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("Take a photo of your receipt to earn LinkX tokens")
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }
                    .padding(.top, 80)
                    .padding(.horizontal, 24)
                    
                    Spacer()
                    
                    // Scanning Frame
                    ReceiptFrameView()
                    
                    Spacer()
                    
                    // Bottom Actions
                    VStack(spacing: 24) {
                        // Main Action Buttons
                        HStack(spacing: 40) {
                            // Camera Button
                            Button(action: {
                                if cameraPermissionGranted {
                                    capturePhoto()
                                } else {
                                    selectedSource = .camera
                                    requestCameraPermission()
                                }
                            }) {
                                VStack(spacing: 8) {
                                    ZStack {
                                        Circle()
                                            .fill(.white.opacity(0.2))
                                            .frame(width: 70, height: 70)

                                        Circle()
                                            .stroke(.white.opacity(0.3), lineWidth: 2)
                                            .frame(width: 70, height: 70)

                                        Image(systemName: "camera.fill")
                                            .font(.system(size: 30))
                                            .foregroundColor(.white)
                                    }

                                    Text("Chụp")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                }
                            }

                            // Photos Button
                            Button(action: {
                                selectedSource = .photoLibrary
                                showImagePicker = true
                            }) {
                                VStack(spacing: 8) {
                                    ZStack {
                                        Circle()
                                            .fill(.white.opacity(0.2))
                                            .frame(width: 70, height: 70)

                                        Circle()
                                            .stroke(.white.opacity(0.3), lineWidth: 2)
                                            .frame(width: 70, height: 70)

                                        Image(systemName: "photo.fill")
                                            .font(.system(size: 30))
                                            .foregroundColor(.white)
                                    }

                                    Text("Photos")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                }
                            }
                        }

                        // Secondary Actions
                        HStack(spacing: 32) {
                            Button("Manual Entry") {
                                showManualEntry = true
                            }
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.8))

                            Button("Cancel") {
                                dismiss()
                            }
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.8))
                        }
                    }
                    .padding(.bottom, 50)
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $capturedImage) { image in
                if let image = image {
                    handleCapturedImage(image)
                }
            }
        }
        .sheet(isPresented: $showManualEntry) {
            ManualReceiptEntryView { receiptData in
                onReceiptProcessed?(receiptData)
                dismiss()
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Camera Permission Required", isPresented: $showPermissionAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) {
                dismiss()
            }
        } message: {
            Text("Please allow camera access in Settings to take photos of receipts.")
        }
        .onAppear {
            if selectedSource == .camera {
                requestCameraPermission()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func requestCameraPermission() {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            cameraPermissionGranted = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    cameraPermissionGranted = granted
                    if !granted {
                        showPermissionAlert = true
                    }
                }
            }
        case .denied, .restricted:
            showPermissionAlert = true
        @unknown default:
            showPermissionAlert = true
        }
    }
    
    private func capturePhoto() {
        // This will trigger the camera to take a photo
        // The actual capture is handled by the camera preview view
        selectedSource = .camera
        if !cameraPermissionGranted {
            requestCameraPermission()
        }
    }

    private func handleCapturedImage(_ image: UIImage) {
        capturedImage = image
        processReceipt(image)
    }
    
    private func processReceipt(_ image: UIImage) {
        isProcessing = true
        
        Task {
            do {
                let receiptData = try await OCRService.shared.processReceipt(image: image)
                await MainActor.run {
                    isProcessing = false
                    onReceiptProcessed?(receiptData)
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isProcessing = false
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }
}

// MARK: - Receipt Frame View
struct ReceiptFrameView: View {
    var body: some View {
        ZStack {
            // Main frame
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.white.opacity(0.8), lineWidth: 3)
                .frame(width: 300, height: 380)

            // Corner indicators
            VStack {
                HStack {
                    ReceiptCornerIndicator()
                    Spacer()
                    ReceiptCornerIndicator()
                        .rotationEffect(.degrees(90))
                }
                Spacer()
                HStack {
                    ReceiptCornerIndicator()
                        .rotationEffect(.degrees(-90))
                    Spacer()
                    ReceiptCornerIndicator()
                        .rotationEffect(.degrees(180))
                }
            }
            .frame(width: 300, height: 380)
            .padding(12)

            // Center guide text
            VStack(spacing: 8) {
                Image(systemName: "receipt")
                    .font(.system(size: 40))
                    .foregroundColor(.white.opacity(0.6))

                Text("Position receipt here")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
                    .fontWeight(.medium)
            }
        }
    }
}

// MARK: - Receipt Corner Indicator
struct ReceiptCornerIndicator: View {
    var body: some View {
        VStack(spacing: 0) {
            Rectangle()
                .fill(Color.white)
                .frame(width: 3, height: 20)

            HStack(spacing: 0) {
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 20, height: 3)
                Spacer()
            }
        }
        .frame(width: 20, height: 20)
    }
}

// MARK: - Processing Overlay
struct ProcessingOverlay: View {
    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)
                
                Text("Processing Receipt...")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text("Please wait while we extract the receipt information")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
        }
    }
}

// Receipt models are now defined in Domain/Models/Receipt.swift

// MARK: - Receipt Camera Preview for Receipt
struct ReceiptCameraPreviewView: UIViewRepresentable {
    let onImageCaptured: (UIImage) -> Void

    func makeUIView(context: Context) -> ReceiptCameraPreview {
        let preview = ReceiptCameraPreview()
        preview.delegate = context.coordinator
        return preview
    }

    func updateUIView(_ uiView: ReceiptCameraPreview, context: Context) {
        uiView.startScanning()
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(onImageCaptured: onImageCaptured)
    }

    static func dismantleUIView(_ uiView: ReceiptCameraPreview, coordinator: Coordinator) {
        uiView.stopScanning()
    }

    class Coordinator: NSObject, CameraPreviewDelegate {
        let onImageCaptured: (UIImage) -> Void

        init(onImageCaptured: @escaping (UIImage) -> Void) {
            self.onImageCaptured = onImageCaptured
        }

        func didCaptureImage(_ image: UIImage) {
            onImageCaptured(image)
        }
    }
}

// MARK: - Camera Preview Delegate
protocol CameraPreviewDelegate: AnyObject {
    func didCaptureImage(_ image: UIImage)
}

// MARK: - Receipt Camera Preview UIView
class ReceiptCameraPreview: UIView {
    weak var delegate: CameraPreviewDelegate?
    private var captureSession: AVCaptureSession?
    private var previewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput: AVCapturePhotoOutput?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCamera()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCamera()
    }

    private func setupCamera() {
        captureSession = AVCaptureSession()
        guard let captureSession = captureSession else { return }

        captureSession.sessionPreset = .photo

        guard let backCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            print("❌ Unable to access back camera")
            return
        }

        do {
            let input = try AVCaptureDeviceInput(device: backCamera)
            photoOutput = AVCapturePhotoOutput()

            if captureSession.canAddInput(input) && captureSession.canAddOutput(photoOutput!) {
                captureSession.addInput(input)
                captureSession.addOutput(photoOutput!)

                previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
                previewLayer?.videoGravity = .resizeAspectFill
                previewLayer?.frame = bounds

                if let previewLayer = previewLayer {
                    layer.addSublayer(previewLayer)
                }

                // Add tap gesture to capture photo
                let tapGesture = UITapGestureRecognizer(target: self, action: #selector(capturePhoto))
                addGestureRecognizer(tapGesture)

                print("✅ Camera setup completed")
            }
        } catch {
            print("❌ Error setting up camera: \(error)")
        }
    }

    @objc private func capturePhoto() {
        guard let photoOutput = photoOutput else { return }

        let settings = AVCapturePhotoSettings()
        photoOutput.capturePhoto(with: settings, delegate: self)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        previewLayer?.frame = bounds
    }

    func startScanning() {
        guard let captureSession = captureSession else { return }

        if !captureSession.isRunning {
            DispatchQueue.global(qos: .background).async {
                captureSession.startRunning()
            }
        }
    }

    func stopScanning() {
        guard let captureSession = captureSession else { return }

        if captureSession.isRunning {
            captureSession.stopRunning()
        }
    }
}

// MARK: - Photo Capture Delegate
extension ReceiptCameraPreview: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            print("❌ Error processing captured photo")
            return
        }

        delegate?.didCaptureImage(image)
    }
}

// MARK: - Image Picker
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    let onImageSelected: (UIImage?) -> Void

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
                parent.onImageSelected(image)
            }
            picker.dismiss(animated: true)
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onImageSelected(nil)
            picker.dismiss(animated: true)
        }
    }
}

// MARK: - Receipt Camera Permission View
struct ReceiptCameraPermissionView: View {
    let onRequestPermission: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "camera.fill")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.6))

            Text("Camera Access Required")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text("Please allow camera access to take photos of receipts")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            Button("Allow Camera Access") {
                onRequestPermission()
            }
            .font(.headline)
            .foregroundColor(.black)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(.white)
            .cornerRadius(25)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black)
    }
}

// MARK: - Preview
#Preview {
    ReceiptScannerView()
}

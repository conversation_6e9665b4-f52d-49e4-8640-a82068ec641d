//
//  RewardCardView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct RewardCardView: View {
    let reward: Reward
    @State private var showDetail = false
    
    var body: some View {
        Button(action: {
            showDetail = true
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // Reward Image
                AsyncImage(url: URL(string: reward.imageUrl ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(AppConstants.Colors.surfaceSecondary)
                        .overlay(
                            Image(systemName: reward.category.icon)
                                .font(.title)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        )
                }
                .frame(height: 120)
                .clipped()
                .cornerRadius(8)
                
                // Reward Info
                VStack(alignment: .leading, spacing: 6) {
                    // Title
                    Text(reward.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    // Partner
                    if let partnerName = reward.partnerName {
                        Text(partnerName)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .lineLimit(1)
                    }
                    
                    // Price and Discount
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text(reward.displayTokenCost)
                                .font(.subheadline)
                                .fontWeight(.bold)
                                .foregroundColor(AppConstants.Colors.primary)
                            
                            if let originalPrice = reward.displayOriginalPrice {
                                Text(originalPrice)
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .strikethrough()
                            }
                        }
                        
                        Spacer()
                        
                        if let discount = reward.displayDiscount {
                            Text(discount)
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(AppConstants.Colors.error)
                                .cornerRadius(4)
                        }
                    }
                    
                    // Availability Status
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color(reward.statusColor))
                            .frame(width: 6, height: 6)
                        
                        Text(reward.availabilityStatus)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        // Category Badge
                        Text(reward.category.displayName)
                            .font(.caption)
                            .foregroundColor(Color(reward.category.color))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(reward.category.color).opacity(0.1))
                            .cornerRadius(4)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 12)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .sheet(isPresented: $showDetail) {
            RewardDetailView(reward: reward)
        }
    }
}

// MARK: - Reward Detail View (Placeholder)
struct RewardDetailView: View {
    let reward: Reward
    @Environment(\.dismiss) private var dismiss
    @StateObject private var rewardManager = RewardManager()
    @State private var showRedemption = false
    @State private var isRedeeming = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Reward Image
                    AsyncImage(url: URL(string: reward.imageUrl ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(AppConstants.Colors.surfaceSecondary)
                            .overlay(
                                Image(systemName: reward.category.icon)
                                    .font(.system(size: 40))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            )
                    }
                    .frame(height: 200)
                    .clipped()
                    .cornerRadius(AppConstants.UI.cornerRadius)
                    
                    VStack(alignment: .leading, spacing: 16) {
                        // Title and Partner
                        VStack(alignment: .leading, spacing: 8) {
                            Text(reward.title)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            if let partnerName = reward.partnerName {
                                Text("by \(partnerName)")
                                    .font(.subheadline)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }
                        }
                        
                        // Description
                        Text(reward.description)
                            .font(.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        // Price Info
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Token Cost")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                
                                Text(reward.displayTokenCost)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                            
                            Spacer()
                            
                            if let originalPrice = reward.displayOriginalPrice {
                                VStack(alignment: .trailing, spacing: 4) {
                                    Text("Original Price")
                                        .font(.caption)
                                        .foregroundColor(AppConstants.Colors.textSecondary)
                                    
                                    Text(originalPrice)
                                        .font(.subheadline)
                                        .foregroundColor(AppConstants.Colors.textSecondary)
                                        .strikethrough()
                                }
                            }
                        }
                        .padding()
                        .background(AppConstants.Colors.surfaceSecondary)
                        .cornerRadius(AppConstants.UI.cornerRadius)
                        
                        // Terms
                        if let terms = reward.terms {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Terms & Conditions")
                                    .font(.headline)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Text(terms)
                                    .font(.subheadline)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }
                        }
                        
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                }
            }
            .navigationTitle("Reward Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .safeAreaInset(edge: .bottom) {
                // Redeem Button
                VStack(spacing: 12) {
                    if !reward.isAvailable {
                        Text(reward.availabilityStatus)
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.error)
                            .padding()
                    }
                    
                    Button(action: {
                        Task {
                            await redeemReward()
                        }
                    }) {
                        HStack {
                            if isRedeeming {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            }
                            
                            Text(isRedeeming ? "Redeeming..." : "Redeem Now")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .primaryButtonStyle()
                    .disabled(!reward.isAvailable || isRedeeming)
                    .opacity(reward.isAvailable ? 1.0 : 0.6)
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                }
                .background(AppConstants.Colors.background)
            }
        }
        .alert("Success", isPresented: $showRedemption) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Reward redeemed successfully! Check your redemptions for the code.")
        }
    }
    
    private func redeemReward() async {
        isRedeeming = true
        
        let success = await rewardManager.redeemReward(rewardId: reward.id)
        
        if success {
            showRedemption = true
        }
        
        isRedeeming = false
    }
}

// MARK: - Preview
#Preview {
    RewardCardView(
        reward: Reward(
            id: "1",
            title: "Free Coffee",
            description: "Get a free medium coffee at any participating location",
            category: .food,
            tokenCost: 25,
            originalPrice: 45000,
            discountPercentage: 1.0,
            imageUrl: nil,
            thumbnailUrl: nil,
            isActive: true,
            isLimited: false,
            totalQuantity: nil,
            remainingQuantity: nil,
            validFrom: nil,
            validUntil: nil,
            terms: "Valid for 30 days from redemption",
            partnerId: "partner1",
            partnerName: "Starbucks",
            partnerLogo: nil,
            redemptionInstructions: "Show this code to the cashier",
            metadata: nil,
            tags: ["coffee", "beverage"],
            popularity: 85,
            totalRedemptions: 1250,
            createdAt: Date(),
            updatedAt: nil
        )
    )
    .frame(width: 200)
    .padding()
}

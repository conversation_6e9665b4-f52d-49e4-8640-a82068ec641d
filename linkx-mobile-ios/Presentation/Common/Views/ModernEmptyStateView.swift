//
//  ModernEmptyStateView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

struct ModernEmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let actionTitle: String?
    let action: (() -> Void)?
    let gradientColors: [Color]
    
    @State private var isAnimating = false
    
    init(
        icon: String,
        title: String,
        subtitle: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil,
        gradientColors: [Color] = [AppConstants.Colors.primary.opacity(0.1), AppConstants.Colors.secondary.opacity(0.1)]
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.actionTitle = actionTitle
        self.action = action
        self.gradientColors = gradientColors
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // Animated Icon Container
            ZStack {
                // Background gradient circle
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: gradientColors),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .scaleEffect(isAnimating ? 1.1 : 1.0)
                    .animation(
                        Animation.easeInOut(duration: 2.0)
                            .repeatForever(autoreverses: true),
                        value: isAnimating
                    )
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 40, weight: .light))
                    .foregroundColor(AppConstants.Colors.primary)
                    .scaleEffect(isAnimating ? 1.05 : 1.0)
                    .animation(
                        Animation.easeInOut(duration: 2.0)
                            .repeatForever(autoreverses: true),
                        value: isAnimating
                    )
            }
            
            // Text content
            VStack(spacing: 12) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                
                Text(subtitle)
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .padding(.horizontal, 16)
            }
            
            // Action button (if provided)
            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.subheadline)
                        Text(actionTitle)
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDark]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .buttonStyle(PlainButtonStyle())
                .scaleEffect(isAnimating ? 1.02 : 1.0)
                .animation(
                    Animation.easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true),
                    value: isAnimating
                )
            }
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 40)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(AppConstants.Colors.surface)
                .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
        )
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Specialized Empty States

struct TransactionEmptyState: View {
    let action: (() -> Void)?
    
    var body: some View {
        ModernEmptyStateView(
            icon: "creditcard.and.123",
            title: "No Transactions Yet",
            subtitle: "Start your LinkX journey by earning tokens with our merchant partners",
            actionTitle: "Start Earning",
            action: action,
            gradientColors: [AppConstants.Colors.primary.opacity(0.15), AppConstants.Colors.accent.opacity(0.1)]
        )
    }
}

struct RewardsEmptyState: View {
    let action: (() -> Void)?
    
    var body: some View {
        ModernEmptyStateView(
            icon: "gift.circle",
            title: "No Featured Rewards",
            subtitle: "Amazing rewards are coming soon! Check back later for exclusive offers",
            actionTitle: "Browse All Rewards",
            action: action,
            gradientColors: [AppConstants.Colors.secondary.opacity(0.15), AppConstants.Colors.primary.opacity(0.1)]
        )
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 40) {
        TransactionEmptyState {
            print("Start earning tapped")
        }
        
        RewardsEmptyState {
            print("Browse rewards tapped")
        }
    }
    .padding()
    .background(AppConstants.Colors.background)
}

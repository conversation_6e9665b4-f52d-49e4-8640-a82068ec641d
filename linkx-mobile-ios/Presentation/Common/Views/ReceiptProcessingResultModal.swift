import SwiftUI
import Foundation

struct ReceiptProcessingResultModal: View {
    let result: ReceiptData
    let onDismiss: () -> Void
    let onSaveTransaction: (ReceiptData) -> Void
    
    @State private var isEditing = false
    @State private var editedResult: ReceiptData
    
    init(result: ReceiptData, onDismiss: @escaping () -> Void, onSaveTransaction: @escaping (ReceiptData) -> Void) {
        self.result = result
        self.onDismiss = onDismiss
        self.onSaveTransaction = onSaveTransaction
        self._editedResult = State(initialValue: result)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Receipt Processed")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Review and confirm the extracted information")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    // Merchant Information
                    GroupBox("Merchant Information") {
                        VStack(alignment: .leading, spacing: 12) {
                            InfoRow(
                                title: "Name",
                                value: editedResult.merchantName,
                                isEditing: isEditing
                            ) { newValue in
                                editedResult = ReceiptData(
                                    id: editedResult.id,
                                    merchantName: newValue,
                                    merchantAddress: editedResult.merchantAddress,
                                    date: editedResult.date,
                                    totalAmount: editedResult.totalAmount,
                                    subtotal: editedResult.subtotal,
                                    taxAmount: editedResult.taxAmount,
                                    items: editedResult.items,
                                    paymentMethod: editedResult.paymentMethod,
                                    receiptNumber: editedResult.receiptNumber,
                                    confidence: editedResult.confidence,
                                    estimatedTokens: editedResult.estimatedTokens,
                                    currency: editedResult.currency,
                                    rawText: editedResult.rawText
                                )
                            }
                            
                            InfoRow(
                                title: "Address",
                                value: editedResult.merchantAddress ?? "Not detected",
                                isEditing: isEditing
                            ) { newValue in
                                editedResult = ReceiptData(
                                    id: editedResult.id,
                                    merchantName: editedResult.merchantName,
                                    merchantAddress: newValue.isEmpty ? nil : newValue,
                                    date: editedResult.date,
                                    totalAmount: editedResult.totalAmount,
                                    subtotal: editedResult.subtotal,
                                    taxAmount: editedResult.taxAmount,
                                    items: editedResult.items,
                                    paymentMethod: editedResult.paymentMethod,
                                    receiptNumber: editedResult.receiptNumber,
                                    confidence: editedResult.confidence,
                                    estimatedTokens: editedResult.estimatedTokens,
                                    currency: editedResult.currency,
                                    rawText: editedResult.rawText
                                )
                            }
                        }
                    }
                    
                    // Transaction Details
                    GroupBox("Transaction Details") {
                        VStack(alignment: .leading, spacing: 12) {
                            InfoRow(
                                title: "Total Amount",
                                value: String(format: "%.2f", editedResult.totalAmount),
                                isEditing: isEditing,
                                keyboardType: .decimalPad
                            ) { newValue in
                                editedResult = ReceiptData(
                                    id: editedResult.id,
                                    merchantName: editedResult.merchantName,
                                    merchantAddress: editedResult.merchantAddress,
                                    date: editedResult.date,
                                    totalAmount: Double(newValue) ?? editedResult.totalAmount,
                                    subtotal: editedResult.subtotal,
                                    taxAmount: editedResult.taxAmount,
                                    items: editedResult.items,
                                    paymentMethod: editedResult.paymentMethod,
                                    receiptNumber: editedResult.receiptNumber,
                                    confidence: editedResult.confidence,
                                    estimatedTokens: editedResult.estimatedTokens,
                                    currency: editedResult.currency,
                                    rawText: editedResult.rawText
                                )
                            }
                            
                            InfoRow(
                                title: "Currency",
                                value: editedResult.currency,
                                isEditing: isEditing
                            ) { newValue in
                                editedResult = ReceiptData(
                                    id: editedResult.id,
                                    merchantName: editedResult.merchantName,
                                    merchantAddress: editedResult.merchantAddress,
                                    date: editedResult.date,
                                    totalAmount: editedResult.totalAmount,
                                    subtotal: editedResult.subtotal,
                                    taxAmount: editedResult.taxAmount,
                                    items: editedResult.items,
                                    paymentMethod: editedResult.paymentMethod,
                                    receiptNumber: editedResult.receiptNumber,
                                    confidence: editedResult.confidence,
                                    estimatedTokens: editedResult.estimatedTokens,
                                    currency: newValue,
                                    rawText: editedResult.rawText
                                )
                            }
                            
                            InfoRow(
                                title: "Date",
                                value: formatDate(editedResult.date),
                                isEditing: false
                            ) { _ in }
                            
                            if let tax = editedResult.taxAmount {
                                InfoRow(
                                    title: "Tax",
                                    value: String(format: "%.2f", tax),
                                    isEditing: isEditing,
                                    keyboardType: .decimalPad
                                ) { newValue in
                                    editedResult = ReceiptData(
                                        id: editedResult.id,
                                        merchantName: editedResult.merchantName,
                                        merchantAddress: editedResult.merchantAddress,
                                        date: editedResult.date,
                                        totalAmount: editedResult.totalAmount,
                                        subtotal: editedResult.subtotal,
                                        taxAmount: Double(newValue),
                                        items: editedResult.items,
                                        paymentMethod: editedResult.paymentMethod,
                                        receiptNumber: editedResult.receiptNumber,
                                        confidence: editedResult.confidence,
                                        estimatedTokens: editedResult.estimatedTokens,
                                        currency: editedResult.currency,
                                        rawText: editedResult.rawText
                                    )
                                }
                            }
                        }
                    }
                    
                    // Items (if available)
                    if !editedResult.items.isEmpty {
                        GroupBox("Items") {
                            VStack(alignment: .leading, spacing: 8) {
                                ForEach(editedResult.items.indices, id: \.self) { index in
                                    let item = editedResult.items[index]
                                    HStack {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(item.name)
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                            
                                            if let quantity = item.quantity {
                                                Text("Qty: \(quantity)")
                                                    .font(.caption)
                                                    .foregroundColor(.secondary)
                                            }
                                        }
                                        
                                        Spacer()
                                        
                                        Text(String(format: "%.2f", item.totalPrice))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                    }
                                    .padding(.vertical, 4)
                                    
                                    if index < editedResult.items.count - 1 {
                                        Divider()
                                    }
                                }
                            }
                        }
                    }
                    
                    // Confidence Score
                    GroupBox("Processing Confidence") {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Confidence Score")
                                    .font(.subheadline)
                                
                                Spacer()
                                
                                Text("\(Int(editedResult.confidence * 100))%")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(confidenceColor)
                            }
                            
                            ProgressView(value: editedResult.confidence)
                                .tint(confidenceColor)
                            
                            Text(confidenceDescription)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Receipt Result")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onDismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Button(isEditing ? "Done" : "Edit") {
                            isEditing.toggle()
                        }
                        
                        Button("Save") {
                            onSaveTransaction(editedResult)
                        }
                        .fontWeight(.semibold)
                        .disabled(editedResult.totalAmount <= 0)
                    }
                }
            }
        }
    }
    
    private var confidenceColor: Color {
        if editedResult.confidence >= 0.8 {
            return .green
        } else if editedResult.confidence >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
    
    private var confidenceDescription: String {
        if editedResult.confidence >= 0.8 {
            return "High confidence - Information is likely accurate"
        } else if editedResult.confidence >= 0.6 {
            return "Medium confidence - Please review the information"
        } else {
            return "Low confidence - Please verify all information carefully"
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    let isEditing: Bool
    let keyboardType: UIKeyboardType
    let onValueChange: (String) -> Void
    
    @State private var editingValue: String
    
    init(
        title: String,
        value: String,
        isEditing: Bool,
        keyboardType: UIKeyboardType = .default,
        onValueChange: @escaping (String) -> Void
    ) {
        self.title = title
        self.value = value
        self.isEditing = isEditing
        self.keyboardType = keyboardType
        self.onValueChange = onValueChange
        self._editingValue = State(initialValue: value)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .textCase(.uppercase)
            
            if isEditing {
                TextField(title, text: $editingValue)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(keyboardType)
                    .onChange(of: editingValue) { newValue in
                        onValueChange(newValue)
                    }
            } else {
                Text(value)
                    .font(.subheadline)
            }
        }
    }
}

#Preview {
    ReceiptProcessingResultModal(
        result: ReceiptData(
            id: "sample-id",
            merchantName: "Sample Store",
            merchantAddress: "123 Main St, City",
            date: Date(),
            totalAmount: 25.99,
            subtotal: 23.91,
            taxAmount: 2.08,
            items: [
                ReceiptItem(
                    name: "Coffee",
                    quantity: 2,
                    unitPrice: 2.25,
                    totalPrice: 4.50
                ),
                ReceiptItem(
                    name: "Sandwich",
                    quantity: 1,
                    unitPrice: 8.99,
                    totalPrice: 8.99
                )
            ],
            paymentMethod: "Credit Card",
            receiptNumber: "RCP-1234",
            confidence: 0.85,
            estimatedTokens: 0.0026,
            currency: "USD",
            rawText: "Sample receipt text"
        ),
        onDismiss: {},
        onSaveTransaction: { _ in }
    )
}

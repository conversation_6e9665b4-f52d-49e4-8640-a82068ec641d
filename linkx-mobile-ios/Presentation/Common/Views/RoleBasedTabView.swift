//
//  RoleBasedTabView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

// MARK: - Role-Based Tab View
struct RoleBasedTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    var body: some View {
        Group {
            if let user = authViewModel.currentUser {
                switch user.role {
                case .admin:
                    AdminTabView()
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing AdminTabView for ADMIN user - ID: \(user.id), Name: \(user.displayName)")
                        }
                case .merchant:
                    MerchantTabView()
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing MerchantTabView for MERCHANT user - ID: \(user.id), Name: \(user.displayName)")
                        }
                case .user:
                    UserTabView()
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing UserTabView for USER user - ID: \(user.id), Name: \(user.displayName)")
                        }
                }
            } else {
                // Fallback to user view if no user data
                UserTabView()
                    .environmentObject(notificationsViewModel)
                    .onAppear {
                        print("🔐 RoleBasedTabView: No user found - showing fallback UserTabView")
                    }
            }
        }
        .onAppear {
            print("🔐 RoleBasedTabView: onAppear - currentUser: \(String(describing: authViewModel.currentUser))")
            if let user = authViewModel.currentUser {
                print("🔐 RoleBasedTabView: onAppear - User role: \(user.role)")
            }
        }
    }
}

// MARK: - Admin Tab View
struct AdminTabView: View {
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Dashboard", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 1, title: "Users", icon: "person.2.circle", selectedIcon: "person.2.circle.fill"),
            TabItem(id: 2, title: "Merchants", icon: "building.2.circle", selectedIcon: "building.2.circle.fill"),
            TabItem(id: 3, title: "Rewards", icon: "star.circle", selectedIcon: "star.circle.fill"),
            TabItem(id: 4, title: "Profile", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }
    
    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            Group {
                switch selectedTab {
                case 0:
                    AdminDashboardView()
                        .environmentObject(notificationsViewModel)
                case 1:
                    AdminUsersView()
                        .environmentObject(notificationsViewModel)
                case 2:
                    AdminMerchantsView()
                        .environmentObject(notificationsViewModel)
                case 3:
                    AdminRewardsView()
                        .environmentObject(notificationsViewModel)
                case 4:
                    ProfileView()
                        .environmentObject(notificationsViewModel)
                default:
                    AdminDashboardView()
                        .environmentObject(notificationsViewModel)
                }
            }
        }
    }
}

// MARK: - Merchant Tab View
struct MerchantTabView: View {
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Dashboard", icon: "chart.line.uptrend.xyaxis.circle", selectedIcon: "chart.line.uptrend.xyaxis.circle.fill"),
            TabItem(id: 1, title: "Wallet", icon: "creditcard.circle", selectedIcon: "creditcard.circle.fill"),
            TabItem(id: 2, title: "Payments", icon: "banknote.circle", selectedIcon: "banknote.circle.fill"),
            TabItem(id: 3, title: "Analytics", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 4, title: "Profile", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }
    
    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            Group {
                switch selectedTab {
                case 0:
                    MerchantDashboardView()
                        .environmentObject(notificationsViewModel)
                case 1:
                    WalletView()
                        .environmentObject(notificationsViewModel)
                case 2:
                    MerchantPaymentsView()
                        .environmentObject(notificationsViewModel)
                case 3:
                    MerchantAnalyticsView()
                        .environmentObject(notificationsViewModel)
                case 4:
                    ProfileView()
                        .environmentObject(notificationsViewModel)
                default:
                    MerchantDashboardView()
                        .environmentObject(notificationsViewModel)
                }
            }
        }
    }
}

// MARK: - User Tab View (Original Layout)
struct UserTabView: View {
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Home", icon: "house.circle", selectedIcon: "house.circle.fill"),
            TabItem(id: 1, title: "Wallet", icon: "creditcard.circle", selectedIcon: "creditcard.circle.fill"),
            TabItem(id: 2, title: "Rewards", icon: "star.circle", selectedIcon: "star.circle.fill"),
            TabItem(id: 3, title: "History", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 4, title: "Profile", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }
    
    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            Group {
                switch selectedTab {
                case 0:
                    HomeView()
                        .environmentObject(notificationsViewModel)
                case 1:
                    WalletView()
                case 2:
                    RewardsView()
                case 3:
                    TransactionHistoryView()
                case 4:
                    ProfileView()
                default:
                    HomeView()
                        .environmentObject(notificationsViewModel)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    RoleBasedTabView()
        .environmentObject(AuthViewModel())
}

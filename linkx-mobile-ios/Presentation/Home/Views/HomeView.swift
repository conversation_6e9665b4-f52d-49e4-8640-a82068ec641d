//
//  HomeView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct HomeView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var transactionManager = TransactionManager.shared
    @StateObject private var rewardManager = RewardManager()
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    @State private var showReceiptScanner = false
    @State private var showingNotifications = false
    @State private var showTransferTokens = false
    @State private var showMerchants = false
    @State private var refreshing = false
    @State private var showReceiptResultModal = false
    @State private var processedReceiptData: ReceiptData?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header with user greeting
                    headerSection
                    
                    // Wallet Balance Card
                    walletBalanceCard
                    
                    // Quick Actions
                    quickActionsSection
                    
                    // Recent Transactions
                    recentTransactionsSection
                    
                    // Featured Rewards
                    featuredRewardsSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 10)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
        }
        .task {
            await loadInitialData()
        }
        .sheet(isPresented: $showReceiptScanner) {
            ReceiptScannerView { receiptData in
                handleReceiptProcessed(receiptData)
            }
        }
        .sheet(isPresented: $showTransferTokens) {
            TransferTokensView()
        }
        .sheet(isPresented: $showMerchants) {
            MerchantsView()
        }
        .sheet(isPresented: $showingNotifications) {
            NotificationsView()
                .environmentObject(notificationsViewModel)
        }
        .sheet(isPresented: $showReceiptResultModal) {
            if let receiptData = processedReceiptData {
                ReceiptProcessingResultModal(
                    result: receiptData,
                    onDismiss: {
                        showReceiptResultModal = false
                        processedReceiptData = nil
                    },
                    onSaveTransaction: { receiptData in
                        // Handle saving transaction
                        showReceiptResultModal = false
                        processedReceiptData = nil

                        // Refresh data to show updated transactions
                        Task {
                            await refreshData()
                        }
                    }
                )
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Welcome back,")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(authViewModel.currentUser?.displayName ?? "User")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            Spacer()
            
            // Notification Button
            Button(action: {
                showingNotifications = true
            }) {
                ZStack {
                    IconWithBadge(
                        iconName: "bell",
                        badgeCount: notificationsViewModel.unreadCount,
                        iconColor: AppConstants.Colors.textPrimary,
                        iconSize: 20
                    )
                }
                .frame(width: 44, height: 44)
                .background(AppConstants.Colors.surface)
                .cornerRadius(22)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
            }
        }
    }
    
    // MARK: - Wallet Balance Card
    private var walletBalanceCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total Balance")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text(transactionManager.balance?.displayBalance ?? "0.00 LXT")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("≈ \(transactionManager.balance?.displayVndEquivalent ?? "0 VND")")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                // LinkX Logo
                Image(systemName: "link.circle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            // Balance Actions
            HStack(spacing: 16) {
                Button(action: {
                    showReceiptScanner = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "qrcode.viewfinder")
                            .font(.subheadline)
                        Text("Earn")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 36)
                    .background(.white)
                    .cornerRadius(18)
                }
                
                Button(action: {
                    showTransferTokens = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.up.circle")
                            .font(.subheadline)
                        Text("Send")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 36)
                    .background(.white.opacity(0.2))
                    .cornerRadius(18)
                }
            }
        }
        .padding(20)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDark]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                QuickActionCard(
                    icon: "receipt",
                    title: "Scan Receipt",
                    subtitle: "Earn tokens",
                    color: AppConstants.Colors.primary
                ) {
                    showReceiptScanner = true
                }
                
                QuickActionCard(
                    icon: "gift",
                    title: "Rewards",
                    subtitle: "Redeem prizes",
                    color: AppConstants.Colors.secondary
                ) {
                    // TODO: Navigate to rewards
                }
                
                QuickActionCard(
                    icon: "building.2",
                    title: "Merchants",
                    subtitle: "Find partners",
                    color: AppConstants.Colors.accent
                ) {
                    showMerchants = true
                }
                
                QuickActionCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Analytics",
                    subtitle: "View stats",
                    color: AppConstants.Colors.info
                ) {
                    // TODO: Navigate to analytics
                }
            }
        }
    }
    
    // MARK: - Recent Transactions Section
    private var recentTransactionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Transactions")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("View All") {
                    // TODO: Navigate to transactions
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            if transactionManager.isLoading && transactionManager.transactions.isEmpty {
                TransactionLoadingState()
            } else if transactionManager.transactions.isEmpty {
                TransactionEmptyState {
                    showReceiptScanner = true
                }
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(transactionManager.transactions.prefix(5))) { transaction in
                        TransactionRowView(transaction: transaction)
                    }
                }
                .cardStyle()
            }
        }
    }
    
    // MARK: - Featured Rewards Section
    private var featuredRewardsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Featured Rewards")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("View All") {
                    // TODO: Navigate to rewards
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            if rewardManager.isLoading && rewardManager.featuredRewards.isEmpty {
                RewardLoadingState()
            } else if rewardManager.featuredRewards.isEmpty {
                RewardsEmptyState {
                    // TODO: Navigate to rewards
                }
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyHStack(spacing: 12) {
                        ForEach(rewardManager.featuredRewards) { reward in
                            RewardCardView(reward: reward)
                                .frame(width: 200)
                        }
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                }
                .padding(.horizontal, -AppConstants.UI.screenPadding)
            }
        }
    }
    
    // MARK: - Data Loading
    private func loadInitialData() async {
        await transactionManager.loadBalance()
        await transactionManager.loadTransactions()
        await rewardManager.loadFeaturedRewards()
    }
    
    private func refreshData() async {
        refreshing = true
        await transactionManager.refreshAll()
        await rewardManager.loadFeaturedRewards()
        refreshing = false
    }

    // MARK: - Receipt Processing
    private func handleReceiptProcessed(_ receiptData: ReceiptData) {
        Logger.shared.info("Receipt processed: \(receiptData.merchantName), Amount: \(receiptData.totalAmount)", category: .general)

        // Close the receipt scanner
        showReceiptScanner = false

        // Store receipt data and show modal
        processedReceiptData = receiptData
        showReceiptResultModal = true

        // Refresh data to show updated balance and transactions
        Task {
            await refreshData()
        }
    }




}

// MARK: - Quick Action Card
struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
            .background(AppConstants.Colors.surface)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    HomeView()
        .environmentObject(AuthViewModel())
}

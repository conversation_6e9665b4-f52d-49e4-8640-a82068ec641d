//
//  MerchantViewModel.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import Foundation
import SwiftUI

// MARK: - Merchant Wallet Info Model
struct MerchantWalletInfo: Codable {
    let walletAddress: String
    let tokenBalance: Double
    let totalCommissionsPaid: Double
    let totalTransactions: Int
}

// MARK: - Merchant Payment Model
struct MerchantPayment: Codable, Identifiable {
    let id: String
    let type: PaymentType
    let amount: Double
    let description: String?
    let status: PaymentStatus
    let createdAt: Date
    
    enum PaymentType: String, Codable {
        case commissionPayment = "COMMISSION_PAYMENT"
        case settlementPayment = "SETTLEMENT_PAYMENT"
        case walletTopup = "WALLET_TOPUP"
        case walletWithdrawal = "WALLET_WITHDRAWAL"
        
        var displayName: String {
            switch self {
            case .commissionPayment:
                return "Commission"
            case .settlementPayment:
                return "Settlement"
            case .walletTopup:
                return "Top Up"
            case .walletWithdrawal:
                return "Withdrawal"
            }
        }
        
        var iconName: String {
            switch self {
            case .commissionPayment:
                return "percent"
            case .settlementPayment:
                return "banknote"
            case .walletTopup:
                return "arrow.down.circle"
            case .walletWithdrawal:
                return "arrow.up.circle"
            }
        }
        
        var iconColor: Color {
            switch self {
            case .commissionPayment:
                return .blue
            case .settlementPayment:
                return .green
            case .walletTopup:
                return .green
            case .walletWithdrawal:
                return .orange
            }
        }
    }
    
    enum PaymentStatus: String, Codable {
        case pending = "PENDING"
        case completed = "COMPLETED"
        case failed = "FAILED"
        
        var displayName: String {
            switch self {
            case .pending:
                return "Pending"
            case .completed:
                return "Completed"
            case .failed:
                return "Failed"
            }
        }
        
        var color: Color {
            switch self {
            case .pending:
                return .orange
            case .completed:
                return .green
            case .failed:
                return .red
            }
        }
    }
}

// MARK: - Merchant Repository Protocol
protocol MerchantRepositoryProtocol {
    func getWalletInfo() async throws -> MerchantWalletInfo
    func getPaymentHistory(type: MerchantPayment.PaymentType?, limit: Int, offset: Int) async throws -> MerchantPaymentListResponse
}

// MARK: - Merchant Repository Implementation
class MerchantRepository: MerchantRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    func getWalletInfo() async throws -> MerchantWalletInfo {
        return try await apiClient.request(
            endpoint: APIEndpoints.Merchants.dashboardWallet,
            method: .GET,
            responseType: MerchantWalletInfo.self
        )
    }
    
    func getPaymentHistory(type: MerchantPayment.PaymentType? = nil, limit: Int = 20, offset: Int = 0) async throws -> MerchantPaymentListResponse {
        var parameters: [String: Any] = [
            "limit": limit,
            "offset": offset
        ]
        
        if let type = type {
            parameters["type"] = type.rawValue
        }
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Merchants.dashboardPayments,
            method: .GET,
            parameters: parameters,
            responseType: MerchantPaymentListResponse.self
        )
    }
}

// MARK: - Merchant ViewModel
@MainActor
class MerchantViewModel: ObservableObject {
    @Published var walletInfo: MerchantWalletInfo?
    @Published var recentPayments: [MerchantPayment] = []
    @Published var allPayments: [MerchantPayment] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let repository: MerchantRepositoryProtocol
    private let logger = Logger.shared
    private var currentPage = 1
    private var hasMorePages = true
    
    init(repository: MerchantRepositoryProtocol = MerchantRepository()) {
        self.repository = repository
    }
    
    // MARK: - Dashboard Data
    func loadDashboardData() async {
        isLoading = true
        error = nil
        
        do {
            async let walletTask = repository.getWalletInfo()
            async let paymentsTask = repository.getPaymentHistory(type: nil, limit: 10, offset: 0)
            
            let (wallet, payments) = try await (walletTask, paymentsTask)
            
            self.walletInfo = wallet
            self.recentPayments = payments.payments
            
            logger.info("Merchant dashboard data loaded successfully", category: .general)
            
        } catch {
            self.error = error
            logger.error("Failed to load merchant dashboard data", error: error, category: .general)
        }
        
        isLoading = false
    }
    
    func refreshDashboardData() async {
        currentPage = 1
        hasMorePages = true
        await loadDashboardData()
    }
    
    // MARK: - Payment History
    func loadPaymentHistory(type: MerchantPayment.PaymentType? = nil, refresh: Bool = false) async {
        if refresh {
            currentPage = 1
            hasMorePages = true
            allPayments.removeAll()
        }
        
        guard hasMorePages && !isLoading else { return }
        
        isLoading = true
        error = nil
        
        do {
            let response = try await repository.getPaymentHistory(
                type: type,
                limit: 20,
                offset: (currentPage - 1) * 20
            )
            
            if refresh {
                allPayments = response.payments
            } else {
                allPayments.append(contentsOf: response.payments)
            }
            
            hasMorePages = response.payments.count == 20
            currentPage += 1
            
            logger.info("Loaded \(response.payments.count) payments", category: .general)
            
        } catch {
            self.error = error
            logger.error("Failed to load payment history", error: error, category: .general)
        }
        
        isLoading = false
    }
    
    // MARK: - Analytics Data
    func calculateMonthlyRevenue() -> Double {
        let calendar = Calendar.current
        let now = Date()
        let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
        
        return recentPayments
            .filter { $0.createdAt >= startOfMonth && $0.status == .completed }
            .reduce(0) { $0 + $1.amount }
    }
    
    func calculateDailyAverage() -> Double {
        let calendar = Calendar.current
        let now = Date()
        let daysInMonth = calendar.range(of: .day, in: .month, for: now)?.count ?? 30
        
        let monthlyRevenue = calculateMonthlyRevenue()
        return monthlyRevenue / Double(daysInMonth)
    }
    
    func getTopPaymentTypes() -> [(MerchantPayment.PaymentType, Double)] {
        let groupedPayments = Dictionary(grouping: recentPayments.filter { $0.status == .completed }) { $0.type }
        
        return groupedPayments.map { (type, payments) in
            let total = payments.reduce(0) { $0 + $1.amount }
            return (type, total)
        }.sorted { $0.1 > $1.1 }
    }
}

// MARK: - Supporting Models
struct MerchantPaymentListResponse: Codable {
    let payments: [MerchantPayment]
    let total: Int
}

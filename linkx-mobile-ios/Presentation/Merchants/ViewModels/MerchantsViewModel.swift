import Foundation
import SwiftUI

@MainActor
class MerchantsViewModel: ObservableObject {
    @Published var merchants: [Merchant] = []
    @Published var filteredMerchants: [Merchant] = []
    @Published var searchText: String = ""
    @Published var selectedCategory: MerchantCategory? = nil
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var showingSearch: Bool = false
    
    // Categories for filtering
    let categories: [MerchantCategory] = MerchantCategory.allCases
    
    init() {
        loadDemoData()
    }
    
    // MARK: - Demo Data
    private func loadDemoData() {
        merchants = [
            Merchant(
                id: "1",
                name: "Highlands Coffee",
                businessId: "HC001",
                email: "<EMAIL>",
                phone: "+84 28 3825 6789",
                category: .restaurant,
                status: .active,
                commissionRate: 0.02,
                minimumCommission: 1000,
                maximumCommission: 50000,
                description: "Premium coffee chain with Vietnamese and international flavors",
                website: "https://highlands.vn",
                logoUrl: "https://via.placeholder.com/100x100?text=HC",
                address: MerchantAddress(
                    street: "123 Nguyen Hue Street",
                    city: "Ho Chi Minh City",
                    state: "Ho Chi Minh",
                    zipCode: "700000",
                    country: "Vietnam",
                    latitude: 10.7769,
                    longitude: 106.7009
                ),
                operatingHours: [
                    OperatingHour(dayOfWeek: 1, openTime: "06:00", closeTime: "22:00", isClosed: false),
                    OperatingHour(dayOfWeek: 2, openTime: "06:00", closeTime: "22:00", isClosed: false),
                    OperatingHour(dayOfWeek: 3, openTime: "06:00", closeTime: "22:00", isClosed: false),
                    OperatingHour(dayOfWeek: 4, openTime: "06:00", closeTime: "22:00", isClosed: false),
                    OperatingHour(dayOfWeek: 5, openTime: "06:00", closeTime: "22:00", isClosed: false),
                    OperatingHour(dayOfWeek: 6, openTime: "07:00", closeTime: "23:00", isClosed: false),
                    OperatingHour(dayOfWeek: 0, openTime: "07:00", closeTime: "23:00", isClosed: false)
                ],
                socialMedia: nil,
                walletAddress: "0x1234567890abcdef",
                apiKey: nil,
                webhookUrl: nil,
                totalTransactions: 1250,
                totalCommissionPaid: 125000,
                totalSettlementReceived: 2500000,
                metadata: nil,
                notes: nil,
                lastTransactionAt: Date().addingTimeInterval(-3600),
                onboardedAt: Date().addingTimeInterval(-86400 * 30),
                createdAt: Date().addingTimeInterval(-86400 * 30),
                updatedAt: Date().addingTimeInterval(-3600)
            ),
            
            Merchant(
                id: "2",
                name: "Circle K",
                businessId: "CK002",
                email: "<EMAIL>",
                phone: "+84 28 3456 7890",
                category: .retail,
                status: .active,
                commissionRate: 0.015,
                minimumCommission: 500,
                maximumCommission: 25000,
                description: "24/7 convenience store chain",
                website: "https://circlek.vn",
                logoUrl: "https://via.placeholder.com/100x100?text=CK",
                address: MerchantAddress(
                    street: "456 Le Loi Boulevard",
                    city: "Ho Chi Minh City",
                    state: "Ho Chi Minh",
                    zipCode: "700000",
                    country: "Vietnam",
                    latitude: 10.7756,
                    longitude: 106.7019
                ),
                operatingHours: [
                    OperatingHour(dayOfWeek: 1, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 2, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 3, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 4, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 5, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 6, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 0, openTime: "00:00", closeTime: "23:59", isClosed: false)
                ],
                socialMedia: nil,
                walletAddress: "0xabcdef1234567890",
                apiKey: nil,
                webhookUrl: nil,
                totalTransactions: 2100,
                totalCommissionPaid: 189000,
                totalSettlementReceived: 4200000,
                metadata: nil,
                notes: nil,
                lastTransactionAt: Date().addingTimeInterval(-1800),
                onboardedAt: Date().addingTimeInterval(-86400 * 45),
                createdAt: Date().addingTimeInterval(-86400 * 45),
                updatedAt: Date().addingTimeInterval(-1800)
            ),
            
            Merchant(
                id: "3",
                name: "CGV Cinemas",
                businessId: "CGV003",
                email: "<EMAIL>",
                phone: "+84 28 2345 6789",
                category: .entertainment,
                status: .active,
                commissionRate: 0.025,
                minimumCommission: 2000,
                maximumCommission: 100000,
                description: "Premium cinema experience with latest movies",
                website: "https://cgv.vn",
                logoUrl: "https://via.placeholder.com/100x100?text=CGV",
                address: MerchantAddress(
                    street: "789 Dong Khoi Street",
                    city: "Ho Chi Minh City",
                    state: "Ho Chi Minh",
                    zipCode: "700000",
                    country: "Vietnam",
                    latitude: 10.7740,
                    longitude: 106.7030
                ),
                operatingHours: [
                    OperatingHour(dayOfWeek: 1, openTime: "09:00", closeTime: "23:30", isClosed: false),
                    OperatingHour(dayOfWeek: 2, openTime: "09:00", closeTime: "23:30", isClosed: false),
                    OperatingHour(dayOfWeek: 3, openTime: "09:00", closeTime: "23:30", isClosed: false),
                    OperatingHour(dayOfWeek: 4, openTime: "09:00", closeTime: "23:30", isClosed: false),
                    OperatingHour(dayOfWeek: 5, openTime: "09:00", closeTime: "23:30", isClosed: false),
                    OperatingHour(dayOfWeek: 6, openTime: "08:00", closeTime: "24:00", isClosed: false),
                    OperatingHour(dayOfWeek: 0, openTime: "08:00", closeTime: "24:00", isClosed: false)
                ],
                socialMedia: nil,
                walletAddress: "0x9876543210fedcba",
                apiKey: nil,
                webhookUrl: nil,
                totalTransactions: 850,
                totalCommissionPaid: 212500,
                totalSettlementReceived: 8500000,
                metadata: nil,
                notes: nil,
                lastTransactionAt: Date().addingTimeInterval(-7200),
                onboardedAt: Date().addingTimeInterval(-86400 * 60),
                createdAt: Date().addingTimeInterval(-86400 * 60),
                updatedAt: Date().addingTimeInterval(-7200)
            ),
            
            Merchant(
                id: "4",
                name: "Shopee Express",
                businessId: "SE004",
                email: "<EMAIL>",
                phone: "+84 28 1234 5678",
                category: .ecommerce,
                status: .active,
                commissionRate: 0.018,
                minimumCommission: 800,
                maximumCommission: 30000,
                description: "Fast delivery and e-commerce platform",
                website: "https://shopee.vn",
                logoUrl: "https://via.placeholder.com/100x100?text=SP",
                address: MerchantAddress(
                    street: "321 Vo Van Tan Street",
                    city: "Ho Chi Minh City",
                    state: "Ho Chi Minh",
                    zipCode: "700000",
                    country: "Vietnam",
                    latitude: 10.7829,
                    longitude: 106.6934
                ),
                operatingHours: [
                    OperatingHour(dayOfWeek: 1, openTime: "08:00", closeTime: "18:00", isClosed: false),
                    OperatingHour(dayOfWeek: 2, openTime: "08:00", closeTime: "18:00", isClosed: false),
                    OperatingHour(dayOfWeek: 3, openTime: "08:00", closeTime: "18:00", isClosed: false),
                    OperatingHour(dayOfWeek: 4, openTime: "08:00", closeTime: "18:00", isClosed: false),
                    OperatingHour(dayOfWeek: 5, openTime: "08:00", closeTime: "18:00", isClosed: false),
                    OperatingHour(dayOfWeek: 6, openTime: "09:00", closeTime: "17:00", isClosed: false),
                    OperatingHour(dayOfWeek: 0, openTime: "Closed", closeTime: "Closed", isClosed: true)
                ],
                socialMedia: nil,
                walletAddress: "0xfedcba0987654321",
                apiKey: nil,
                webhookUrl: nil,
                totalTransactions: 3200,
                totalCommissionPaid: 576000,
                totalSettlementReceived: 32000000,
                metadata: nil,
                notes: nil,
                lastTransactionAt: Date().addingTimeInterval(-900),
                onboardedAt: Date().addingTimeInterval(-86400 * 90),
                createdAt: Date().addingTimeInterval(-86400 * 90),
                updatedAt: Date().addingTimeInterval(-900)
            ),
            
            Merchant(
                id: "5",
                name: "Grab Services",
                businessId: "GR005",
                email: "<EMAIL>",
                phone: "+84 28 9876 5432",
                category: .services,
                status: .active,
                commissionRate: 0.02,
                minimumCommission: 1000,
                maximumCommission: 40000,
                description: "Ride-hailing and delivery services",
                website: "https://grab.com/vn",
                logoUrl: "https://via.placeholder.com/100x100?text=GR",
                address: MerchantAddress(
                    street: "654 Nguyen Thi Minh Khai",
                    city: "Ho Chi Minh City",
                    state: "Ho Chi Minh",
                    zipCode: "700000",
                    country: "Vietnam",
                    latitude: 10.7891,
                    longitude: 106.6917
                ),
                operatingHours: [
                    OperatingHour(dayOfWeek: 1, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 2, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 3, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 4, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 5, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 6, openTime: "00:00", closeTime: "23:59", isClosed: false),
                    OperatingHour(dayOfWeek: 0, openTime: "00:00", closeTime: "23:59", isClosed: false)
                ],
                socialMedia: nil,
                walletAddress: "0x1357924680abcdef",
                apiKey: nil,
                webhookUrl: nil,
                totalTransactions: 4500,
                totalCommissionPaid: 900000,
                totalSettlementReceived: 45000000,
                metadata: nil,
                notes: nil,
                lastTransactionAt: Date().addingTimeInterval(-300),
                onboardedAt: Date().addingTimeInterval(-86400 * 120),
                createdAt: Date().addingTimeInterval(-86400 * 120),
                updatedAt: Date().addingTimeInterval(-300)
            )
        ]
        
        filteredMerchants = merchants
    }
    
    // MARK: - Search & Filter Functions
    func searchMerchants() {
        filterMerchants()
    }
    
    func filterByCategory(_ category: MerchantCategory?) {
        selectedCategory = category
        filterMerchants()
    }
    
    private func filterMerchants() {
        var filtered = merchants
        
        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { merchant in
                merchant.name.localizedCaseInsensitiveContains(searchText) ||
                merchant.description?.localizedCaseInsensitiveContains(searchText) == true ||
                merchant.displayAddress.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Filter by category
        if let category = selectedCategory {
            filtered = filtered.filter { $0.category == category }
        }
        
        filteredMerchants = filtered
    }
    
    func clearFilters() {
        searchText = ""
        selectedCategory = nil
        filteredMerchants = merchants
    }
    
    // MARK: - Merchant Actions
    func refreshMerchants() {
        isLoading = true
        
        // Simulate API call
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.loadDemoData()
            self.isLoading = false
        }
    }
    
    func getMerchant(by id: String) -> Merchant? {
        return merchants.first { $0.id == id }
    }
    
    // MARK: - Computed Properties
    var activeMerchantsCount: Int {
        merchants.filter { $0.isActive }.count
    }
    
    var categoryCounts: [MerchantCategory: Int] {
        var counts: [MerchantCategory: Int] = [:]
        for category in categories {
            counts[category] = merchants.filter { $0.category == category }.count
        }
        return counts
    }
}

import SwiftUI

struct MerchantSearchView: View {
    @ObservedObject var viewModel: MerchantsViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var localSearchText = ""
    @State private var selectedCategory: MerchantCategory? = nil
    @State private var showingFilters = false
    @State private var selectedMerchant: Merchant?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search header
                searchHeaderSection
                
                // Filters section
                if showingFilters {
                    filtersSection
                        .transition(.slide)
                }
                
                // Search results
                searchResultsSection
            }
            .navigationTitle("Search Merchants")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingFilters.toggle()
                        }
                    }) {
                        Image(systemName: showingFilters ? "line.3.horizontal.decrease.circle.fill" : "line.3.horizontal.decrease.circle")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
        }
        .sheet(item: $selectedMerchant) { merchant in
            MerchantDetailView(merchant: merchant)
        }
        .onAppear {
            localSearchText = viewModel.searchText
            selectedCategory = viewModel.selectedCategory
        }
    }
    
    // MARK: - Search Header Section
    private var searchHeaderSection: some View {
        VStack(spacing: 16) {
            // Main search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search by name, category, or location...", text: $localSearchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onSubmit {
                        performSearch()
                    }
                
                if !localSearchText.isEmpty {
                    Button(action: {
                        localSearchText = ""
                        performSearch()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            
            // Quick search suggestions
            if localSearchText.isEmpty {
                quickSearchSuggestions
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Quick Search Suggestions
    private var quickSearchSuggestions: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Search")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(["Coffee", "Restaurant", "Retail", "Entertainment", "Services"], id: \.self) { suggestion in
                        Button(action: {
                            localSearchText = suggestion
                            performSearch()
                        }) {
                            Text(suggestion)
                                .font(.subheadline)
                                .foregroundColor(AppConstants.Colors.primary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(AppConstants.Colors.primary, lineWidth: 1)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
    
    // MARK: - Filters Section
    private var filtersSection: some View {
        VStack(spacing: 16) {
            Divider()
            
            VStack(alignment: .leading, spacing: 16) {
                // Category filter
                VStack(alignment: .leading, spacing: 12) {
                    Text("Category")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            // All categories
                            MerchantFilterChip(
                                title: "All",
                                isSelected: selectedCategory == nil
                            ) {
                                selectedCategory = nil
                                performSearch()
                            }

                            // Individual categories
                            ForEach(MerchantCategory.allCases, id: \.self) { category in
                                MerchantFilterChip(
                                    title: category.displayName,
                                    isSelected: selectedCategory == category
                                ) {
                                    selectedCategory = category
                                    performSearch()
                                }
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                }
                
                // Action buttons
                HStack(spacing: 16) {
                    Button(action: clearFilters) {
                        Text("Clear All")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.secondary, lineWidth: 1)
                            )
                    }
                    
                    Spacer()
                    
                    Button(action: performSearch) {
                        Text("Apply Filters")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(AppConstants.Colors.primary)
                            )
                    }
                }
            }
            .padding(.horizontal, 16)
            
            Divider()
        }
        .background(Color(.systemGray6))
    }
    
    // MARK: - Search Results Section
    private var searchResultsSection: some View {
        Group {
            if viewModel.isLoading {
                VStack {
                    Spacer()
                    ProgressView("Searching...")
                        .progressViewStyle(CircularProgressViewStyle())
                    Spacer()
                }
            } else if viewModel.filteredMerchants.isEmpty {
                emptyResultsView
            } else {
                searchResultsList
            }
        }
    }
    
    // MARK: - Empty Results View
    private var emptyResultsView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("No merchants found")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("Try different keywords or adjust your filters")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button(action: clearFilters) {
                Text("Clear Search")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(8)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Search Results List
    private var searchResultsList: some View {
        VStack(spacing: 0) {
            // Results header
            HStack {
                Text("\(viewModel.filteredMerchants.count) merchants found")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            
            // Results list
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.filteredMerchants) { merchant in
                        SearchResultCard(merchant: merchant) {
                            selectedMerchant = merchant
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
        }
    }
    
    // MARK: - Actions
    private func performSearch() {
        viewModel.searchText = localSearchText
        viewModel.selectedCategory = selectedCategory
        viewModel.searchMerchants()
    }
    
    private func clearFilters() {
        localSearchText = ""
        selectedCategory = nil
        viewModel.clearFilters()
    }
}

// MARK: - Merchant Filter Chip
struct MerchantFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? AppConstants.Colors.primary : Color(.systemGray6))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Search Result Card
struct SearchResultCard: View {
    let merchant: Merchant
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Logo
                AsyncImage(url: URL(string: merchant.logoUrl ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "building.2")
                                .foregroundColor(.secondary)
                        )
                }
                .frame(width: 60, height: 60)
                .cornerRadius(8)
                
                // Info
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(merchant.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Circle()
                            .fill(merchant.isActive ? Color.green : Color.orange)
                            .frame(width: 8, height: 8)
                    }
                    
                    Text(merchant.category.displayName)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if let description = merchant.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    HStack {
                        Text(merchant.displayAddress)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text(merchant.displayCommissionRate)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct MerchantSearchView_Previews: PreviewProvider {
    static var previews: some View {
        MerchantSearchView(viewModel: MerchantsViewModel())
    }
}

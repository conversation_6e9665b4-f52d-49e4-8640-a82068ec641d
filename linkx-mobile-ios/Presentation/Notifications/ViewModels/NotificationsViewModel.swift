import Foundation
import SwiftUI
import UserNotifications

@MainActor
class NotificationsViewModel: ObservableObject {
    @Published var notifications: [AppNotification] = []
    @Published var filteredNotifications: [AppNotification] = []
    @Published var selectedCategory: NotificationCategory = .all
    @Published var selectedType: NotificationType? = nil
    @Published var isLoading = false
    @Published var searchText = ""
    @Published var showingSettings = false
    @Published var notificationSettings = NotificationSettings()
    @Published var unreadCount = 0
    
    private let notificationService = NotificationService.shared
    
    init() {
        loadNotifications()
        loadNotificationSettings()
        requestNotificationPermission()
    }
    
    // MARK: - Data Loading
    func loadNotifications() {
        isLoading = true
        
        // Simulate API call with demo data
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.notifications = self.createDemoNotifications()
            self.filterNotifications()
            self.updateUnreadCount()
            self.isLoading = false
        }
    }
    
    func refreshNotifications() async {
        isLoading = true
        
        // Simulate API refresh
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        notifications = createDemoNotifications()
        filterNotifications()
        updateUnreadCount()
        isLoading = false
    }
    
    // MARK: - Filtering
    func filterNotifications() {
        var filtered = notifications
        
        // Filter by category
        switch selectedCategory {
        case .all:
            break
        case .unread:
            filtered = filtered.filter { !$0.isRead }
        case .today:
            filtered = filtered.filter { $0.isToday }
        case .thisWeek:
            filtered = filtered.filter { $0.isThisWeek }
        case .important:
            filtered = filtered.filter { $0.isImportant }
        }
        
        // Filter by type
        if let selectedType = selectedType {
            filtered = filtered.filter { $0.type == selectedType }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { notification in
                notification.title.localizedCaseInsensitiveContains(searchText) ||
                notification.message.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Sort by timestamp (newest first)
        filtered.sort { $0.timestamp > $1.timestamp }
        
        filteredNotifications = filtered
    }
    
    // MARK: - Actions
    func markAsRead(_ notification: AppNotification) {
        if let index = notifications.firstIndex(where: { $0.id == notification.id }) {
            notifications[index] = AppNotification(
                id: notification.id,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                category: notification.category,
                timestamp: notification.timestamp,
                isRead: true,
                actionData: notification.actionData,
                imageUrl: notification.imageUrl,
                priority: notification.priority
            )
            filterNotifications()
            updateUnreadCount()
        }
    }
    
    func markAllAsRead() {
        notifications = notifications.map { notification in
            AppNotification(
                id: notification.id,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                category: notification.category,
                timestamp: notification.timestamp,
                isRead: true,
                actionData: notification.actionData,
                imageUrl: notification.imageUrl,
                priority: notification.priority
            )
        }
        filterNotifications()
        updateUnreadCount()
    }
    
    func deleteNotification(_ notification: AppNotification) {
        notifications.removeAll { $0.id == notification.id }
        filterNotifications()
        updateUnreadCount()
    }
    
    func deleteAllNotifications() {
        notifications.removeAll()
        filterNotifications()
        updateUnreadCount()
    }
    
    func handleNotificationAction(_ notification: AppNotification) {
        markAsRead(notification)
        
        guard let actionData = notification.actionData else { return }
        
        switch actionData.actionType {
        case .openTransaction:
            // Navigate to transaction detail
            break
        case .openReward:
            // Navigate to reward detail
            break
        case .openMerchant:
            // Navigate to merchant detail
            break
        case .openUrl:
            if let urlString = actionData.url, let url = URL(string: urlString) {
                UIApplication.shared.open(url)
            }
        case .openSettings:
            showingSettings = true
        case .openWallet:
            // Navigate to wallet
            break
        case .none:
            break
        }
    }
    
    // MARK: - Settings
    func loadNotificationSettings() {
        // Load from UserDefaults or API
        if let data = UserDefaults.standard.data(forKey: "notification_settings"),
           let settings = try? JSONDecoder().decode(NotificationSettings.self, from: data) {
            notificationSettings = settings
        }
    }
    
    func saveNotificationSettings() {
        if let data = try? JSONEncoder().encode(notificationSettings) {
            UserDefaults.standard.set(data, forKey: "notification_settings")
        }
    }
    
    func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                self.notificationSettings.pushNotificationsEnabled = granted
                self.saveNotificationSettings()
            }
        }
    }
    
    // MARK: - Private Methods
    private func updateUnreadCount() {
        unreadCount = notifications.filter { !$0.isRead }.count
    }
    
    private func createDemoNotifications() -> [AppNotification] {
        let now = Date()
        let calendar = Calendar.current
        
        return [
            AppNotification(
                title: "Payment Received",
                message: "You received 50 LXT from Highlands Coffee transaction",
                type: .transaction,
                category: .all,
                timestamp: calendar.date(byAdding: .minute, value: -5, to: now) ?? now,
                isRead: false,
                actionData: NotificationActionData(actionType: .openTransaction, targetId: "tx_001"),
                priority: .normal
            ),
            AppNotification(
                title: "New Reward Available",
                message: "Earn 2x points at CGV Cinemas this weekend!",
                type: .reward,
                category: .all,
                timestamp: calendar.date(byAdding: .hour, value: -2, to: now) ?? now,
                isRead: false,
                actionData: NotificationActionData(actionType: .openReward, targetId: "reward_001"),
                priority: .high
            ),
            AppNotification(
                title: "Security Alert",
                message: "New device login detected. If this wasn't you, please secure your account.",
                type: .security,
                category: .all,
                timestamp: calendar.date(byAdding: .hour, value: -4, to: now) ?? now,
                isRead: true,
                actionData: NotificationActionData(actionType: .openSettings),
                priority: .urgent
            ),
            AppNotification(
                title: "Merchant Update",
                message: "Circle K now accepts LinkX payments at 50+ new locations",
                type: .merchant,
                category: .all,
                timestamp: calendar.date(byAdding: .day, value: -1, to: now) ?? now,
                isRead: true,
                actionData: NotificationActionData(actionType: .openMerchant, targetId: "merchant_002"),
                priority: .normal
            ),
            AppNotification(
                title: "Special Promotion",
                message: "Get 20% cashback on all Grab rides this month!",
                type: .promotion,
                category: .all,
                timestamp: calendar.date(byAdding: .day, value: -2, to: now) ?? now,
                isRead: false,
                actionData: NotificationActionData(actionType: .openMerchant, targetId: "merchant_005"),
                priority: .high
            ),
            AppNotification(
                title: "App Update Available",
                message: "Version 2.1.0 is now available with new features and bug fixes",
                type: .update,
                category: .all,
                timestamp: calendar.date(byAdding: .day, value: -3, to: now) ?? now,
                isRead: true,
                actionData: NotificationActionData(actionType: .openUrl, url: "https://apps.apple.com/app/linkx"),
                priority: .normal
            ),
            AppNotification(
                title: "System Maintenance",
                message: "Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM",
                type: .system,
                category: .all,
                timestamp: calendar.date(byAdding: .day, value: -5, to: now) ?? now,
                isRead: true,
                priority: .normal
            ),
            AppNotification(
                title: "Transaction Completed",
                message: "Your payment of 25 LXT to Shopee was successful",
                type: .transaction,
                category: .all,
                timestamp: calendar.date(byAdding: .day, value: -7, to: now) ?? now,
                isRead: true,
                actionData: NotificationActionData(actionType: .openTransaction, targetId: "tx_002"),
                priority: .normal
            )
        ]
    }
}

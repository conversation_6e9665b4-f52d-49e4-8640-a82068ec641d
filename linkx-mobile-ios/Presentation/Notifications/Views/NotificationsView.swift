import SwiftUI
import UIKit

struct NotificationsView: View {
    @EnvironmentObject var viewModel: NotificationsViewModel
    @State private var showingActionSheet = false
    @State private var selectedNotification: AppNotification?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Custom header with integrated search
                customHeaderSection

                // Compact filters
                compactFiltersSection

                // Notifications list
                notificationsList
            }
            .navigationBarHidden(true)
            .refreshable {
                await viewModel.refreshNotifications()
            }
            .sheet(isPresented: $viewModel.showingSettings) {
                NotificationSettingsView(viewModel: viewModel)
            }
            .actionSheet(isPresented: $showingActionSheet) {
                ActionSheet(
                    title: Text("Clear All Notifications"),
                    message: Text("This action cannot be undone."),
                    buttons: [
                        .destructive(Text("Clear All")) {
                            viewModel.deleteAllNotifications()
                        },
                        .cancel()
                    ]
                )
            }
        }
        .onChange(of: viewModel.selectedCategory) { _ in
            viewModel.filterNotifications()
        }
        .onChange(of: viewModel.selectedType) { _ in
            viewModel.filterNotifications()
        }
        .onChange(of: viewModel.searchText) { _ in
            viewModel.filterNotifications()
        }
    }

    // MARK: - Custom Header Section
    private var customHeaderSection: some View {
        VStack(spacing: 0) {
            // Status bar spacer
            Rectangle()
                .fill(Color.clear)
                .frame(height: getSafeAreaTop())

            // Header content
            HStack(spacing: 16) {
                // Title with notification count
                VStack(alignment: .leading, spacing: 2) {
                    Text("Notifications")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    if viewModel.unreadCount > 0 {
                        Text("\(viewModel.unreadCount) unread")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Action buttons
                HStack(spacing: 12) {
                    // Mark all as read button
                    if viewModel.unreadCount > 0 {
                        Button(action: {
                            viewModel.markAllAsRead()
                        }) {
                            Image(systemName: "envelope.open")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }

                    // Menu button
                    Menu {
                        Button("Mark All as Read") {
                            viewModel.markAllAsRead()
                        }

                        Button("Settings") {
                            viewModel.showingSettings = true
                        }

                        Divider()

                        Button("Clear All", role: .destructive) {
                            showingActionSheet = true
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.primary)
                    }
                }
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.bottom, 12)

            // Integrated search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))

                TextField("Search notifications...", text: $viewModel.searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(.system(size: 16))

                if !viewModel.searchText.isEmpty {
                    Button("Clear") {
                        viewModel.searchText = ""
                    }
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.bottom, 16)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemBackground),
                    Color(.systemBackground).opacity(0.95)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .overlay(
            // Bottom border
            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 0.5)
                .opacity(0.5),
            alignment: .bottom
        )
    }

    // MARK: - Compact Filters Section
    private var compactFiltersSection: some View {
        // Compact filter chips with badges
        ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    // Category filters with badges
                    ForEach(NotificationCategory.allCases, id: \.self) { category in
                        CompactFilterChip(
                            title: category.displayName,
                            badge: badgeCountForCategory(category),
                            isSelected: viewModel.selectedCategory == category
                        ) {
                            viewModel.selectedCategory = category
                        }
                    }

                    Divider()
                        .frame(height: 20)
                        .padding(.horizontal, 4)

                    // Type filters with badges
                    CompactFilterChip(
                        title: "All Types",
                        badge: viewModel.notifications.count,
                        isSelected: viewModel.selectedType == nil
                    ) {
                        viewModel.selectedType = nil
                    }

                    ForEach(NotificationType.allCases, id: \.self) { type in
                        CompactFilterChip(
                            title: type.displayName,
                            badge: badgeCountForType(type),
                            isSelected: viewModel.selectedType == type
                        ) {
                            viewModel.selectedType = type
                        }
                    }
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.3))
    }
    
    // MARK: - Helper Functions
    private func getSafeAreaTop() -> CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 44 // Default status bar height
        }
        return window.safeAreaInsets.top
    }

    private func badgeCountForCategory(_ category: NotificationCategory) -> Int {
        switch category {
        case .all:
            return viewModel.notifications.count
        case .unread:
            return viewModel.unreadCount
        case .today:
            return viewModel.notifications.filter { $0.isToday }.count
        case .thisWeek:
            return viewModel.notifications.filter { $0.isThisWeek }.count
        case .important:
            return viewModel.notifications.filter { $0.priority == .high }.count
        }
    }

    private func badgeCountForType(_ type: NotificationType) -> Int {
        return viewModel.notifications.filter { $0.type == type }.count
    }
    
    // MARK: - Notifications List
    private var notificationsList: some View {
        Group {
            if viewModel.isLoading {
                ProgressView("Loading notifications...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if viewModel.filteredNotifications.isEmpty {
                EmptyStateView(
                    icon: "bell.slash",
                    title: "No Notifications",
                    subtitle: viewModel.searchText.isEmpty ? 
                        "You're all caught up!" : 
                        "No notifications match your search"
                )
            } else {
                List {
                    ForEach(viewModel.filteredNotifications) { notification in
                        NotificationRowView(
                            notification: notification,
                            onTap: {
                                viewModel.handleNotificationAction(notification)
                            },
                            onMarkAsRead: {
                                viewModel.markAsRead(notification)
                            },
                            onDelete: {
                                viewModel.deleteNotification(notification)
                            }
                        )
                        .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                        .listRowSeparator(.hidden)
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
    }
}

// MARK: - Compact Filter Chip with Badge
struct CompactFilterChip: View {
    let title: String
    let badge: Int
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)

                if badge > 0 {
                    Text("\(badge)")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(isSelected ? AppConstants.Colors.primary : .white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(isSelected ? .white : AppConstants.Colors.primary)
                        )
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(isSelected ? AppConstants.Colors.primary : Color(.systemGray5))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Preview
#Preview {
    NotificationsView()
        .environmentObject(NotificationsViewModel())
}

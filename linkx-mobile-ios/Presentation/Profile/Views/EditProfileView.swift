//
//  EditProfileView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct EditProfileView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var phone = ""
    @State private var dateOfBirth = Date()
    @State private var showDatePicker = false
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showSuccess = false
    @FocusState private var focusedField: EditProfileField?
    
    enum EditProfileField {
        case firstName, lastName, phone
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Avatar Section
                    avatarSection
                    
                    // Personal Information
                    personalInfoSection
                    
                    // Contact Information
                    contactInfoSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveProfile()
                        }
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .disabled(isLoading || !hasChanges)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
        }
        .onAppear {
            loadCurrentUserData()
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Profile updated successfully!")
        }
    }
    
    // MARK: - Avatar Section
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                // TODO: Implement photo picker
            }) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDark]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                    
                    if let avatarUrl = authViewModel.currentUser?.avatar {
                        AsyncImage(url: URL(string: avatarUrl)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Text(authViewModel.currentUser?.initials ?? "U")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                    } else {
                        Text(authViewModel.currentUser?.initials ?? "U")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                    
                    // Camera icon
                    Circle()
                        .fill(AppConstants.Colors.surface)
                        .frame(width: 36, height: 36)
                        .overlay(
                            Image(systemName: "camera.fill")
                                .font(.subheadline)
                                .foregroundColor(AppConstants.Colors.primary)
                        )
                        .offset(x: 40, y: 40)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            Text("Tap to change photo")
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Personal Information Section
    private var personalInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Personal Information")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: AppConstants.UI.itemSpacing) {
                // First Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("First Name")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    TextField("Enter your first name", text: $firstName)
                        .textFieldStyle()
                        .textContentType(.givenName)
                        .focused($focusedField, equals: .firstName)
                        .onSubmit {
                            focusedField = .lastName
                        }
                }
                
                // Last Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Last Name")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    TextField("Enter your last name", text: $lastName)
                        .textFieldStyle()
                        .textContentType(.familyName)
                        .focused($focusedField, equals: .lastName)
                        .onSubmit {
                            focusedField = .phone
                        }
                }
                
                // Date of Birth
                VStack(alignment: .leading, spacing: 8) {
                    Text("Date of Birth")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Button(action: {
                        showDatePicker = true
                    }) {
                        HStack {
                            Text(dateOfBirth.formatted(date: .abbreviated, time: .omitted))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Spacer()
                            
                            Image(systemName: "calendar")
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        .padding()
                        .background(AppConstants.Colors.surface)
                        .cornerRadius(AppConstants.UI.cornerRadius)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                                .stroke(AppConstants.Colors.border, lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $dateOfBirth)
        }
    }
    
    // MARK: - Contact Information Section
    private var contactInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Contact Information")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: AppConstants.UI.itemSpacing) {
                // Email (Read-only)
                VStack(alignment: .leading, spacing: 8) {
                    Text("Email")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    HStack {
                        Text(authViewModel.currentUser?.email ?? "")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text("Verified")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.success)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppConstants.Colors.success.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding()
                    .background(AppConstants.Colors.surfaceSecondary)
                    .cornerRadius(AppConstants.UI.cornerRadius)
                }
                
                // Phone
                VStack(alignment: .leading, spacing: 8) {
                    Text("Phone Number")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    TextField("Enter your phone number", text: $phone)
                        .textFieldStyle()
                        .keyboardType(.phonePad)
                        .textContentType(.telephoneNumber)
                        .focused($focusedField, equals: .phone)
                    
                    if !phone.isEmpty && !phone.isValidPhone {
                        Text("Please enter a valid phone number")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Computed Properties
    private var hasChanges: Bool {
        guard let user = authViewModel.currentUser else { return false }
        
        return firstName != (user.firstName ?? "") ||
               lastName != (user.lastName ?? "") ||
               phone != (user.phone ?? "")
    }
    
    // MARK: - Helper Methods
    private func loadCurrentUserData() {
        guard let user = authViewModel.currentUser else { return }
        
        firstName = user.firstName ?? ""
        lastName = user.lastName ?? ""
        phone = user.phone ?? ""
        
        if let dob = user.dateOfBirth {
            dateOfBirth = dob
        }
    }
    
    private func saveProfile() async {
        guard hasChanges else {
            dismiss()
            return
        }
        
        isLoading = true
        
        do {
            let request = UpdateProfileRequest(
                firstName: firstName.isEmpty ? nil : firstName,
                lastName: lastName.isEmpty ? nil : lastName,
                phone: phone.isEmpty ? nil : phone,
                dateOfBirth: dateOfBirth,
                avatar: nil // TODO: Implement avatar upload
            )
            
            let authRepository = AuthRepository()
            let updatedUser = try await authRepository.updateProfile(request: request)
            
            // Update the current user in auth state
            await authViewModel.refreshUserData()
            
            showSuccess = true
            
        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }
        
        isLoading = false
    }
}

// MARK: - Date Picker Sheet
struct DatePickerSheet: View {
    @Binding var selectedDate: Date
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "Select Date",
                    selection: $selectedDate,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(WheelDatePickerStyle())
                .padding()
                
                Spacer()
            }
            .navigationTitle("Date of Birth")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    EditProfileView()
        .environmentObject(AuthViewModel())
}

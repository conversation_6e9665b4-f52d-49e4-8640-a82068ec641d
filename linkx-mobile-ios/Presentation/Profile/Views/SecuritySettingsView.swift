//
//  SecuritySettingsView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import LocalAuthentication

struct SecuritySettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authViewModel = AuthViewModel()
    @State private var showChangePassword = false
    @State private var showDeleteAccount = false
    @State private var biometricEnabled = false
    @State private var biometricType = ""
    @State private var showBiometricError = false
    @State private var biometricErrorMessage = ""
    
    var body: some View {
        NavigationView {
            List {
                // Authentication Section
                Section("Authentication") {
                    if KeychainManager.shared.isBiometricAvailable() {
                        ToggleRow(
                            icon: biometricType == "Face ID" ? "faceid" : "touchid",
                            title: biometricType,
                            subtitle: "Use \(biometricType) for quick access",
                            isOn: $biometricEnabled
                        )
                        .onChange(of: biometricEnabled) { _, enabled in
                            toggleBiometric(enabled)
                        }
                    }
                    
                    Button(action: {
                        showChangePassword = true
                    }) {
                        SettingsRow(
                            icon: "key",
                            title: "Change Password",
                            subtitle: "Update your account password"
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                // Privacy Section
                Section("Privacy") {
                    NavigationLink(destination: PrivacySettingsView()) {
                        SettingsRow(
                            icon: "eye.slash",
                            title: "Privacy Settings",
                            subtitle: "Control your data and privacy"
                        )
                    }
                    
                    NavigationLink(destination: DataExportView()) {
                        SettingsRow(
                            icon: "square.and.arrow.up",
                            title: "Export Data",
                            subtitle: "Download your account data"
                        )
                    }
                }
                
                // Security Information
                Section("Security Information") {
                    NavigationLink(destination: ActiveSessionsView()) {
                        SettingsRow(
                            icon: "desktopcomputer",
                            title: "Active Sessions",
                            subtitle: "Manage your logged-in devices"
                        )
                    }
                    
                    NavigationLink(destination: SecurityLogView()) {
                        SettingsRow(
                            icon: "list.bullet.clipboard",
                            title: "Security Log",
                            subtitle: "View recent security events"
                        )
                    }
                }
                
                // Danger Zone
                Section("Danger Zone") {
                    Button(action: {
                        showDeleteAccount = true
                    }) {
                        SettingsRow(
                            icon: "trash",
                            title: "Delete Account",
                            subtitle: "Permanently delete your account",
                            titleColor: AppConstants.Colors.error,
                            showChevron: false
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("Security")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Back") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
        .onAppear {
            setupBiometric()
        }
        .sheet(isPresented: $showChangePassword) {
            ChangePasswordView()
        }
        .alert("Delete Account", isPresented: $showDeleteAccount) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                // TODO: Implement account deletion
            }
        } message: {
            Text("This action cannot be undone. All your data will be permanently deleted.")
        }
        .alert("Biometric Error", isPresented: $showBiometricError) {
            Button("OK") {
                showBiometricError = false
                biometricErrorMessage = ""
            }
        } message: {
            Text(biometricErrorMessage)
        }
    }
    
    // MARK: - Helper Methods
    private func setupBiometric() {
        biometricEnabled = UserDefaults.standard.bool(forKey: AppConstants.StorageKeys.biometricEnabled)
        biometricType = KeychainManager.shared.biometricTypeString()
    }
    
    private func toggleBiometric(_ enabled: Bool) {
        if enabled {
            // Enable biometric authentication
            let context = LAContext()
            var error: NSError?
            
            if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
                context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: "Enable \(biometricType) for LinkX") { success, authError in
                    DispatchQueue.main.async {
                        if success {
                            UserDefaults.standard.set(true, forKey: AppConstants.StorageKeys.biometricEnabled)
                            HapticManager.shared.trigger(.success)
                        } else {
                            biometricEnabled = false
                            biometricErrorMessage = authError?.localizedDescription ?? "Failed to enable \(biometricType)"
                            showBiometricError = true
                        }
                    }
                }
            } else {
                biometricEnabled = false
                biometricErrorMessage = error?.localizedDescription ?? "\(biometricType) is not available"
                showBiometricError = true
            }
        } else {
            // Disable biometric authentication
            UserDefaults.standard.set(false, forKey: AppConstants.StorageKeys.biometricEnabled)
            // Clear saved biometric credentials
            try? KeychainManager.shared.delete(key: "saved_credentials")
        }
    }
}

// MARK: - Change Password View
struct ChangePasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showSuccess = false
    @FocusState private var focusedField: PasswordField?
    
    enum PasswordField {
        case current, new, confirm
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "key.fill")
                            .font(.system(size: 50))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Change Password")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Enter your current password and choose a new one")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 40)
                    
                    // Password Form
                    VStack(spacing: AppConstants.UI.itemSpacing) {
                        // Current Password
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Current Password")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            SecureField("Enter current password", text: $currentPassword)
                                .textFieldStyle()
                                .focused($focusedField, equals: .current)
                                .onSubmit {
                                    focusedField = .new
                                }
                        }
                        
                        // New Password
                        VStack(alignment: .leading, spacing: 8) {
                            Text("New Password")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            SecureField("Enter new password", text: $newPassword)
                                .textFieldStyle()
                                .focused($focusedField, equals: .new)
                                .onSubmit {
                                    focusedField = .confirm
                                }
                            
                            if !newPassword.isEmpty && !newPassword.isValidPassword {
                                Text("Password must be at least \(AppConstants.Validation.minPasswordLength) characters")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.error)
                            }
                        }
                        
                        // Confirm Password
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Confirm New Password")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            SecureField("Confirm new password", text: $confirmPassword)
                                .textFieldStyle()
                                .focused($focusedField, equals: .confirm)
                                .onSubmit {
                                    Task {
                                        await changePassword()
                                    }
                                }
                            
                            if !confirmPassword.isEmpty && newPassword != confirmPassword {
                                Text("Passwords do not match")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.error)
                            }
                        }
                    }
                    .cardStyle()
                    .padding(.horizontal, 4)
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .navigationTitle("Change Password")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
            .safeAreaInset(edge: .bottom) {
                // Change Password Button
                Button(action: {
                    Task {
                        await changePassword()
                    }
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }
                        
                        Text(isLoading ? "Changing..." : "Change Password")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                }
                .primaryButtonStyle()
                .disabled(!isFormValid || isLoading)
                .opacity(isFormValid ? 1.0 : 0.6)
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .background(AppConstants.Colors.background)
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Password changed successfully!")
        }
    }
    
    private var isFormValid: Bool {
        return !currentPassword.isEmpty &&
               newPassword.isValidPassword &&
               newPassword == confirmPassword
    }
    
    private func changePassword() async {
        isLoading = true
        
        do {
            let request = ChangePasswordRequest(
                currentPassword: currentPassword,
                newPassword: newPassword
            )
            
            let authRepository = AuthRepository()
            try await authRepository.changePassword(request: request)
            
            showSuccess = true
            
        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }
        
        isLoading = false
    }
}

// MARK: - Placeholder Views
struct PrivacySettingsView: View {
    var body: some View {
        List {
            Text("Privacy settings coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Privacy")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct DataExportView: View {
    var body: some View {
        List {
            Text("Data export coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Export Data")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct ActiveSessionsView: View {
    var body: some View {
        List {
            Text("Active sessions coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Active Sessions")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct SecurityLogView: View {
    var body: some View {
        List {
            Text("Security log coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Security Log")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Preview
#Preview {
    SecuritySettingsView()
}

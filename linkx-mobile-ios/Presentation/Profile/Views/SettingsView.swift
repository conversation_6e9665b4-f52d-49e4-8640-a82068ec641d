//
//  SettingsView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage(AppConstants.StorageKeys.notificationsEnabled) private var notificationsEnabled = true
    @AppStorage(AppConstants.StorageKeys.biometricEnabled) private var biometricEnabled = false
    @AppStorage(AppConstants.StorageKeys.selectedLanguage) private var selectedLanguage = "en"
    @State private var showLanguagePicker = false
    @State private var showClearDataAlert = false
    @State private var showResetAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // Notifications Section
                Section("Notifications") {
                    ToggleRow(
                        icon: "bell.fill",
                        title: "Push Notifications",
                        subtitle: "Receive notifications for transactions and rewards",
                        isOn: $notificationsEnabled
                    )
                    
                    if notificationsEnabled {
                        NavigationLink(destination: ProfileNotificationSettingsView()) {
                            SettingsRow(
                                icon: "bell.badge",
                                title: "Notification Types",
                                subtitle: "Customize which notifications you receive"
                            )
                        }
                    }
                }
                
                // Security Section
                Section("Security") {
                    ToggleRow(
                        icon: "faceid",
                        title: "Biometric Authentication",
                        subtitle: "Use Face ID or Touch ID for quick access",
                        isOn: $biometricEnabled
                    )
                    
                    NavigationLink(destination: SecuritySettingsView()) {
                        SettingsRow(
                            icon: "lock.shield",
                            title: "Security Settings",
                            subtitle: "Password, privacy, and security options"
                        )
                    }
                }
                
                // App Preferences Section
                Section("App Preferences") {
                    Button(action: {
                        showLanguagePicker = true
                    }) {
                        SettingsRow(
                            icon: "globe",
                            title: "Language",
                            subtitle: languageDisplayName(for: selectedLanguage),
                            showChevron: true
                        )
                    }
                    .buttonStyle(PlainButtonStyle())

                    NavigationLink(destination: CurrencySettingsView()) {
                        SettingsRow(
                            icon: "dollarsign.circle",
                            title: "Currency",
                            subtitle: "VND (Vietnamese Dong)"
                        )
                    }
                }
                
                // Data & Storage Section
                Section("Data & Storage") {
                    NavigationLink(destination: DataUsageView()) {
                        SettingsRow(
                            icon: "chart.bar",
                            title: "Data Usage",
                            subtitle: "View app data consumption"
                        )
                    }
                    
                    Button(action: {
                        showClearDataAlert = true
                    }) {
                        SettingsRow(
                            icon: "trash",
                            title: "Clear Cache",
                            subtitle: "Free up storage space",
                            titleColor: AppConstants.Colors.warning
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                // Advanced Section
                Section("Advanced") {
                    NavigationLink(destination: DeveloperSettingsView()) {
                        SettingsRow(
                            icon: "hammer",
                            title: "Developer Options",
                            subtitle: "Advanced settings for developers"
                        )
                    }
                    
                    Button(action: {
                        showResetAlert = true
                    }) {
                        SettingsRow(
                            icon: "arrow.clockwise",
                            title: "Reset App",
                            subtitle: "Reset all settings to default",
                            titleColor: AppConstants.Colors.error
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
        .sheet(isPresented: $showLanguagePicker) {
            LanguagePickerView(selectedLanguage: $selectedLanguage)
        }
        .alert("Clear Cache", isPresented: $showClearDataAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Clear", role: .destructive) {
                clearCache()
            }
        } message: {
            Text("This will clear cached data and free up storage space. Your account data will not be affected.")
        }
        .alert("Reset App", isPresented: $showResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                resetApp()
            }
        } message: {
            Text("This will reset all app settings to their default values. You will need to sign in again.")
        }
    }
    
    // MARK: - Helper Methods
    private func languageDisplayName(for code: String) -> String {
        switch code {
        case "en":
            return "English"
        case "vi":
            return "Tiếng Việt"
        default:
            return "English"
        }
    }
    
    private func clearCache() {
        // TODO: Implement cache clearing
        HapticManager.shared.trigger(.success)
    }
    
    private func resetApp() {
        // TODO: Implement app reset
        HapticManager.shared.trigger(.warning)
    }
}

// MARK: - Settings Row
struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let titleColor: Color
    let showChevron: Bool
    
    init(
        icon: String,
        title: String,
        subtitle: String,
        titleColor: Color = AppConstants.Colors.textPrimary,
        showChevron: Bool = true
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.titleColor = titleColor
        self.showChevron = showChevron
    }
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(titleColor)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            if showChevron {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Toggle Row
struct ToggleRow: View {
    let icon: String
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Language Picker View
struct LanguagePickerView: View {
    @Binding var selectedLanguage: String
    @Environment(\.dismiss) private var dismiss
    
    private let languages = [
        ("en", "English"),
        ("vi", "Tiếng Việt")
    ]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(languages, id: \.0) { code, name in
                    Button(action: {
                        selectedLanguage = code
                        dismiss()
                    }) {
                        HStack {
                            Text(name)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Spacer()
                            
                            if selectedLanguage == code {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("Language")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - Placeholder Views
struct ProfileNotificationSettingsView: View {
    var body: some View {
        List {
            Section("Transaction Notifications") {
                ToggleRow(
                    icon: "arrow.up.circle",
                    title: "Token Earned",
                    subtitle: "When you earn tokens from purchases",
                    isOn: .constant(true)
                )
                
                ToggleRow(
                    icon: "gift",
                    title: "Reward Redeemed",
                    subtitle: "When you redeem a reward",
                    isOn: .constant(true)
                )
            }
            
            Section("Marketing") {
                ToggleRow(
                    icon: "megaphone",
                    title: "Promotions",
                    subtitle: "Special offers and promotions",
                    isOn: .constant(false)
                )
            }
        }
        .navigationTitle("Notifications")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct CurrencySettingsView: View {
    var body: some View {
        List {
            Text("Currency settings coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Currency")
        .navigationBarTitleDisplayMode(.inline)
    }
}



struct DataUsageView: View {
    var body: some View {
        List {
            Text("Data usage information coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Data Usage")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct DeveloperSettingsView: View {
    var body: some View {
        List {
            Text("Developer options coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Developer Options")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Preview
#Preview {
    SettingsView()
}

//
//  SupportView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import MessageUI

struct SupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showContactForm = false
    @State private var showFAQ = false
    @State private var showMailComposer = false
    @State private var showMailError = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // Quick Help Options
                    quickHelpSection
                    
                    // Contact Options
                    contactOptionsSection
                    
                    // Resources
                    resourcesSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Help & Support")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .sheet(isPresented: $showContactForm) {
            ContactFormView()
        }
        .sheet(isPresented: $showFAQ) {
            FAQView()
        }
        .sheet(isPresented: $showMailComposer) {
            MailComposerView()
        }
        .alert("Mail Not Available", isPresented: $showMailError) {
            Button("OK") { }
        } message: {
            Text("Mail is not configured on this device. Please contact <NAME_EMAIL>")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "questionmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.primary)
            
            Text("How can we help you?")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("We're here to help you with any questions or issues you might have")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Quick Help Section
    private var quickHelpSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Help")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                SupportOptionCard(
                    icon: "questionmark.circle",
                    title: "Frequently Asked Questions",
                    subtitle: "Find answers to common questions",
                    color: AppConstants.Colors.info
                ) {
                    showFAQ = true
                }
                
                SupportOptionCard(
                    icon: "book.circle",
                    title: "User Guide",
                    subtitle: "Learn how to use LinkX features",
                    color: AppConstants.Colors.secondary
                ) {
                    // TODO: Open user guide
                }
                
                SupportOptionCard(
                    icon: "play.circle",
                    title: "Video Tutorials",
                    subtitle: "Watch step-by-step tutorials",
                    color: AppConstants.Colors.accent
                ) {
                    // TODO: Open video tutorials
                }
            }
        }
    }
    
    // MARK: - Contact Options Section
    private var contactOptionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Contact Us")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                SupportOptionCard(
                    icon: "message.circle",
                    title: "Send Feedback",
                    subtitle: "Share your thoughts and suggestions",
                    color: AppConstants.Colors.primary
                ) {
                    showContactForm = true
                }
                
                SupportOptionCard(
                    icon: "envelope.circle",
                    title: "Email Support",
                    subtitle: "Get help via email",
                    color: AppConstants.Colors.warning
                ) {
                    if MFMailComposeViewController.canSendMail() {
                        showMailComposer = true
                    } else {
                        showMailError = true
                    }
                }
                
                SupportOptionCard(
                    icon: "phone.circle",
                    title: "Call Support",
                    subtitle: "Speak with our support team",
                    color: AppConstants.Colors.success
                ) {
                    if let phoneURL = URL(string: "tel:+84123456789") {
                        UIApplication.shared.open(phoneURL)
                    }
                }
            }
        }
    }
    
    // MARK: - Resources Section
    private var resourcesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Resources")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                SupportOptionCard(
                    icon: "globe",
                    title: "Visit Our Website",
                    subtitle: "Learn more about LinkX",
                    color: AppConstants.Colors.info
                ) {
                    if let url = URL(string: "https://linkx.com") {
                        UIApplication.shared.open(url)
                    }
                }
                
                SupportOptionCard(
                    icon: "doc.text.circle",
                    title: "Terms of Service",
                    subtitle: "Read our terms and conditions",
                    color: AppConstants.Colors.textSecondary
                ) {
                    // TODO: Open terms of service
                }
                
                SupportOptionCard(
                    icon: "shield.circle",
                    title: "Privacy Policy",
                    subtitle: "Learn about our privacy practices",
                    color: AppConstants.Colors.textSecondary
                ) {
                    // TODO: Open privacy policy
                }
            }
        }
    }
}

// MARK: - Support Option Card
struct SupportOptionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 32, height: 32)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding()
            .background(AppConstants.Colors.surface)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Contact Form View
struct ContactFormView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var subject = ""
    @State private var message = ""
    @State private var contactType = ContactType.feedback
    @State private var isLoading = false
    @State private var showSuccess = false
    @FocusState private var focusedField: ContactField?
    
    enum ContactField {
        case subject, message
    }
    
    enum ContactType: String, CaseIterable {
        case feedback = "Feedback"
        case bug = "Bug Report"
        case feature = "Feature Request"
        case support = "Support"
        case other = "Other"
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "envelope.fill")
                            .font(.system(size: 50))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Contact Us")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("We'd love to hear from you")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.top, 20)
                    
                    // Contact Form
                    VStack(spacing: AppConstants.UI.itemSpacing) {
                        // Contact Type
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Type")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Picker("Contact Type", selection: $contactType) {
                                ForEach(ContactType.allCases, id: \.self) { type in
                                    Text(type.rawValue).tag(type)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                            .padding()
                            .background(AppConstants.Colors.surface)
                            .cornerRadius(AppConstants.UI.cornerRadius)
                            .overlay(
                                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                                    .stroke(AppConstants.Colors.border, lineWidth: 1)
                            )
                        }
                        
                        // Subject
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Subject")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextField("Enter subject", text: $subject)
                                .textFieldStyle()
                                .focused($focusedField, equals: .subject)
                                .onSubmit {
                                    focusedField = .message
                                }
                        }
                        
                        // Message
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Message")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            TextEditor(text: $message)
                                .frame(minHeight: 120)
                                .padding()
                                .background(AppConstants.Colors.surface)
                                .cornerRadius(AppConstants.UI.cornerRadius)
                                .overlay(
                                    RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                                        .stroke(AppConstants.Colors.border, lineWidth: 1)
                                )
                                .focused($focusedField, equals: .message)
                        }
                    }
                    .cardStyle()
                    .padding(.horizontal, 4)
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .navigationTitle("Contact Form")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
            .safeAreaInset(edge: .bottom) {
                // Send Button
                Button(action: {
                    Task {
                        await sendMessage()
                    }
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }
                        
                        Text(isLoading ? "Sending..." : "Send Message")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                }
                .primaryButtonStyle()
                .disabled(!isFormValid || isLoading)
                .opacity(isFormValid ? 1.0 : 0.6)
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .background(AppConstants.Colors.background)
            }
        }
        .alert("Message Sent", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Thank you for contacting us! We'll get back to you soon.")
        }
    }
    
    private var isFormValid: Bool {
        return !subject.isEmpty && !message.isEmpty
    }
    
    private func sendMessage() async {
        isLoading = true
        
        // Simulate sending message
        try? await Task.sleep(nanoseconds: 2_000_000_000)
        
        // TODO: Implement actual message sending
        
        showSuccess = true
        isLoading = false
    }
}

// MARK: - FAQ View
struct FAQView: View {
    @Environment(\.dismiss) private var dismiss
    
    private let faqs = [
        FAQ(question: "How do I earn tokens?", answer: "You can earn LXT tokens by shopping at participating merchant partners. Simply scan the QR code at checkout or enter the merchant code manually."),
        FAQ(question: "How do I redeem rewards?", answer: "Browse the rewards catalog, select a reward you want, and tap 'Redeem Now'. You'll receive a redemption code that you can use at the merchant."),
        FAQ(question: "What is the exchange rate?", answer: "Currently, 1 LXT token equals 1,000 VND. This rate may change based on market conditions."),
        FAQ(question: "How do I transfer tokens?", answer: "Go to your wallet, tap 'Send', enter the recipient's wallet address, specify the amount, and confirm the transaction."),
        FAQ(question: "Is my data secure?", answer: "Yes, we use industry-standard encryption and security measures to protect your data. Your private keys are stored securely on your device."),
        FAQ(question: "How do I contact support?", answer: "You can contact us through the app, send an <NAME_EMAIL>, or call our support hotline.")
    ]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(faqs) { faq in
                    FAQRow(faq: faq)
                }
            }
            .navigationTitle("FAQ")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - FAQ Model
struct FAQ: Identifiable {
    let id = UUID()
    let question: String
    let answer: String
}

// MARK: - FAQ Row
struct FAQRow: View {
    let faq: FAQ
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text(faq.question)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            if isExpanded {
                Text(faq.answer)
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Mail Composer
struct MailComposerView: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> MFMailComposeViewController {
        let composer = MFMailComposeViewController()
        composer.mailComposeDelegate = context.coordinator
        composer.setToRecipients(["<EMAIL>"])
        composer.setSubject("LinkX Mobile App Support")
        composer.setMessageBody("Please describe your issue or question:", isHTML: false)
        return composer
    }
    
    func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            controller.dismiss(animated: true)
        }
    }
}

// MARK: - About View
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // App Icon and Info
                    VStack(spacing: 16) {
                        Image(systemName: "link.circle.fill")
                            .font(.system(size: 80))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("LinkX")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Version 1.0.0 (Build 1)")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text("Earn tokens, redeem rewards")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.top, 40)
                    
                    // Company Info
                    VStack(alignment: .leading, spacing: 16) {
                        Text("About LinkX")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("LinkX is a revolutionary loyalty platform that connects consumers with merchants through blockchain technology. Earn LXT tokens for your purchases and redeem them for exciting rewards.")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                    .padding()
                    .background(AppConstants.Colors.surface)
                    .cornerRadius(AppConstants.UI.cornerRadius)
                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    
                    // Legal Links
                    VStack(spacing: 12) {
                        Button("Terms of Service") {
                            // TODO: Open terms of service
                        }
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.primary)
                        
                        Button("Privacy Policy") {
                            // TODO: Open privacy policy
                        }
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.primary)
                        
                        Button("Open Source Licenses") {
                            // TODO: Open licenses
                        }
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                    .padding()
                    .background(AppConstants.Colors.surface)
                    .cornerRadius(AppConstants.UI.cornerRadius)
                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    
                    // Copyright
                    Text("© 2025 LinkX. All rights reserved.")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.top, 20)
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .navigationTitle("About")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
    }
}

// MARK: - Preview
#Preview {
    SupportView()
}

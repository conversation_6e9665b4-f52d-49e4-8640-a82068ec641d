//
//  MyRedemptionsView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct MyRedemptionsView: View {
    @EnvironmentObject var rewardManager: RewardManager
    @State private var selectedFilter: RedemptionStatus?
    @State private var showRedemptionDetail = false
    @State private var selectedRedemption: RedemptionWithReward?
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                // Stats Cards
                redemptionStatsSection
                
                // Filter Section
                filterSection
                
                // Redemptions List
                redemptionsListSection
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 16)
        }
        .sheet(item: $selectedRedemption) { redemption in
            RedemptionDetailView(redemption: redemption)
        }
    }
    
    // MARK: - Redemption Stats Section
    private var redemptionStatsSection: some View {
        HStack(spacing: 12) {
            StatCard(
                title: "Active",
                count: rewardManager.activeRedemptions.count,
                color: AppConstants.Colors.success
            )
            
            StatCard(
                title: "Used",
                count: rewardManager.usedRedemptions.count,
                color: AppConstants.Colors.info
            )
            
            StatCard(
                title: "Expired",
                count: rewardManager.expiredRedemptions.count,
                color: AppConstants.Colors.error
            )
        }
    }
    
    // MARK: - Filter Section
    private var filterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Filter Redemptions")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterChip(
                        title: "All",
                        isSelected: selectedFilter == nil
                    ) {
                        selectedFilter = nil
                    }
                    
                    ForEach(RedemptionStatus.allCases, id: \.self) { status in
                        FilterChip(
                            title: status.displayName,
                            isSelected: selectedFilter == status
                        ) {
                            selectedFilter = status
                        }
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .padding(.horizontal, -AppConstants.UI.screenPadding)
        }
    }
    
    // MARK: - Redemptions List Section
    private var redemptionsListSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("My Redemptions")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Text("\(filteredRedemptions.count) items")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            if filteredRedemptions.isEmpty {
                if rewardManager.isLoading {
                    VStack {
                        ProgressView()
                        Text("Loading redemptions...")
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    VStack(spacing: 16) {
                        Image(systemName: "bag")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        Text("No Redemptions Yet")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("Your redeemed rewards will appear here")
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Browse Rewards") {
                            // TODO: Navigate to rewards catalog
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(filteredRedemptions) { redemption in
                        RedemptionRowView(redemption: redemption)
                            .onTapGesture {
                                selectedRedemption = redemption
                            }
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var filteredRedemptions: [RedemptionWithReward] {
        guard let filter = selectedFilter else {
            return rewardManager.redemptions
        }
        
        return rewardManager.redemptions.filter { $0.redemption.status == filter }
    }
}

// MARK: - Stat Card
struct StatCard: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Redemption Row View
struct RedemptionRowView: View {
    let redemption: RedemptionWithReward
    
    var body: some View {
        HStack(spacing: 12) {
            // Reward Image
            AsyncImage(url: URL(string: redemption.reward.thumbnailUrl ?? redemption.reward.imageUrl ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(AppConstants.Colors.surfaceSecondary)
                    .overlay(
                        Image(systemName: redemption.reward.category.icon)
                            .font(.title2)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    )
            }
            .frame(width: 60, height: 60)
            .clipped()
            .cornerRadius(8)
            
            // Redemption Details
            VStack(alignment: .leading, spacing: 4) {
                Text(redemption.reward.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .lineLimit(2)
                
                if let partnerName = redemption.reward.partnerName {
                    Text(partnerName)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                HStack {
                    Text("Code: \(redemption.redemption.redemptionCode)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Spacer()
                    
                    Text(redemption.redemption.createdAt.timeAgoDisplay())
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Status and Actions
            VStack(alignment: .trailing, spacing: 4) {
                // Status Badge
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color(redemption.redemption.status.color))
                        .frame(width: 6, height: 6)
                    
                    Text(redemption.redemption.status.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(Color(redemption.redemption.status.color))
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(redemption.redemption.status.color).opacity(0.1))
                .cornerRadius(12)
                
                // Expiry Info
                if redemption.redemption.status == .active {
                    Text("Expires \(redemption.redemption.expiresAt.timeAgoDisplay())")
                        .font(.caption)
                        .foregroundColor(redemption.redemption.isExpired ? AppConstants.Colors.error : AppConstants.Colors.textSecondary)
                }
                
                // Token Cost
                Text(redemption.reward.displayTokenCost)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Redemption Detail View
struct RedemptionDetailView: View {
    let redemption: RedemptionWithReward
    @Environment(\.dismiss) private var dismiss
    @State private var showShareSheet = false
    @State private var showQRCode = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Reward Image
                    rewardImageSection
                    
                    // Redemption Info
                    redemptionInfoSection
                    
                    // Redemption Code
                    redemptionCodeSection
                    
                    // Usage Instructions
                    usageInstructionsSection
                    
                    // Terms & Conditions
                    if let terms = redemption.reward.terms {
                        termsSection(terms)
                    }
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Redemption Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showShareSheet = true
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .safeAreaInset(edge: .bottom) {
                if redemption.redemption.isUsable {
                    actionButtonsSection
                }
            }
        }
        .sheet(isPresented: $showShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
        .sheet(isPresented: $showQRCode) {
            RedemptionQRCodeView(redemption: redemption)
        }
    }
    
    // MARK: - Reward Image Section
    private var rewardImageSection: some View {
        AsyncImage(url: URL(string: redemption.reward.imageUrl ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Rectangle()
                .fill(AppConstants.Colors.surfaceSecondary)
                .overlay(
                    Image(systemName: redemption.reward.category.icon)
                        .font(.system(size: 40))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                )
        }
        .frame(height: 200)
        .clipped()
        .cornerRadius(AppConstants.UI.cornerRadius)
    }
    
    // MARK: - Redemption Info Section
    private var redemptionInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(redemption.reward.title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let partnerName = redemption.reward.partnerName {
                Text("by \(partnerName)")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Status")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    HStack(spacing: 6) {
                        Circle()
                            .fill(Color(redemption.redemption.status.color))
                            .frame(width: 8, height: 8)
                        
                        Text(redemption.redemption.status.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(Color(redemption.redemption.status.color))
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Expires")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(redemption.redemption.displayExpiryDate)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(redemption.redemption.isExpired ? AppConstants.Colors.error : AppConstants.Colors.textPrimary)
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Redemption Code Section
    private var redemptionCodeSection: some View {
        VStack(spacing: 16) {
            Text("Redemption Code")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                Text(redemption.redemption.redemptionCode)
                    .font(.system(.title, design: .monospaced))
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding()
                    .background(AppConstants.Colors.primary.opacity(0.1))
                    .cornerRadius(8)
                
                HStack(spacing: 16) {
                    Button(action: {
                        UIPasteboard.general.string = redemption.redemption.redemptionCode
                        HapticManager.shared.trigger(.success)
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "doc.on.doc")
                                .font(.subheadline)
                            Text("Copy Code")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(AppConstants.Colors.primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(AppConstants.Colors.primary.opacity(0.1))
                        .cornerRadius(20)
                    }
                    
                    Button(action: {
                        showQRCode = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "qrcode")
                                .font(.subheadline)
                            Text("Show QR")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(AppConstants.Colors.secondary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(AppConstants.Colors.secondary.opacity(0.1))
                        .cornerRadius(20)
                    }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Usage Instructions Section
    private var usageInstructionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("How to Use")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(redemption.reward.redemptionInstructions ?? "Show this code to the merchant to redeem your reward.")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Terms Section
    private func termsSection(_ terms: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Terms & Conditions")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(terms)
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            Button("Mark as Used") {
                // TODO: Implement mark as used
            }
            .primaryButtonStyle()
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Helper Methods
    private func generateShareText() -> String {
        var text = "LinkX Reward Redemption\n\n"
        text += "Reward: \(redemption.reward.title)\n"
        if let partner = redemption.reward.partnerName {
            text += "Partner: \(partner)\n"
        }
        text += "Code: \(redemption.redemption.redemptionCode)\n"
        text += "Expires: \(redemption.redemption.displayExpiryDate)\n"
        return text
    }
}

// MARK: - Preview
#Preview {
    MyRedemptionsView()
        .environmentObject(RewardManager())
}

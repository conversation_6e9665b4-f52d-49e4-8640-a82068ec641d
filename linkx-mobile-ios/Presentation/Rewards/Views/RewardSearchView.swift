//
//  RewardSearchView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct RewardSearchView: View {
    @EnvironmentObject var rewardManager: RewardManager
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedCategory: RewardCategory?
    @State private var sortBy: RewardSortBy = .popularity
    @State private var isSearching = false
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Header
                searchHeaderSection
                
                // Filters
                filtersSection
                
                // Search Results
                searchResultsSection
            }
            .navigationTitle("Search Rewards")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear") {
                        clearSearch()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .disabled(searchText.isEmpty && selectedCategory == nil)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .onAppear {
            isSearchFocused = true
        }
    }
    
    // MARK: - Search Header Section
    private var searchHeaderSection: some View {
        VStack(spacing: 12) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Search rewards...", text: $searchText)
                    .focused($isSearchFocused)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onSubmit {
                        performSearch()
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        performSearch()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding()
            .background(AppConstants.Colors.surface)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 1)
            )
            
            // Search Suggestions
            if searchText.isEmpty && !isSearching {
                searchSuggestionsSection
            }
        }
        .padding(.horizontal, AppConstants.UI.screenPadding)
        .padding(.vertical, 12)
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Search Suggestions Section
    private var searchSuggestionsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Popular Searches")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(popularSearches, id: \.self) { suggestion in
                        Button(suggestion) {
                            searchText = suggestion
                            performSearch()
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(AppConstants.Colors.primary.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
            }
        }
    }
    
    // MARK: - Filters Section
    private var filtersSection: some View {
        VStack(spacing: 12) {
            // Category Filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterButton(
                        title: "All Categories",
                        isSelected: selectedCategory == nil
                    ) {
                        selectedCategory = nil
                        performSearch()
                    }
                    
                    ForEach(RewardCategory.allCases, id: \.self) { category in
                        FilterButton(
                            title: category.displayName,
                            isSelected: selectedCategory == category
                        ) {
                            selectedCategory = category
                            performSearch()
                        }
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .padding(.horizontal, -AppConstants.UI.screenPadding)
            
            // Sort Options
            HStack {
                Text("Sort by:")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Picker("Sort by", selection: $sortBy) {
                    Text("Popularity").tag(RewardSortBy.popularity)
                    Text("Token Cost").tag(RewardSortBy.tokenCost)
                    Text("Newest").tag(RewardSortBy.newest)
                    Text("Name").tag(RewardSortBy.name)
                }
                .pickerStyle(MenuPickerStyle())
                .onChange(of: sortBy) { _ in
                    performSearch()
                }
                
                Spacer()
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
        .padding(.vertical, 12)
        .background(AppConstants.Colors.surface)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Search Results Section
    private var searchResultsSection: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if isSearching {
                    VStack {
                        ProgressView()
                        Text("Searching rewards...")
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 40)
                } else if searchResults.isEmpty && (!searchText.isEmpty || selectedCategory != nil) {
                    VStack(spacing: 16) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        Text("No Results Found")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("Try adjusting your search or filters")
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Clear Search") {
                            clearSearch()
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding(.top, 40)
                } else if !searchResults.isEmpty {
                    // Search Results
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(searchResults) { reward in
                            RewardCardView(reward: reward)
                        }
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                    
                    // Load More Button
                    if searchResults.count >= AppConstants.Pagination.defaultLimit {
                        Button("Load More Results") {
                            // TODO: Load more search results
                        }
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.primary)
                        .padding()
                    }
                } else {
                    // Recent Searches or Popular Rewards
                    recentSearchesSection
                }
            }
            .padding(.top, 10)
        }
    }
    
    // MARK: - Recent Searches Section
    private var recentSearchesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Popular Rewards")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .padding(.horizontal, AppConstants.UI.screenPadding)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(rewardManager.popularRewards.prefix(6)) { reward in
                    RewardCardView(reward: reward)
                }
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
    }
    
    // MARK: - Computed Properties
    private var searchResults: [Reward] {
        // For now, return filtered rewards from the manager
        // In a real app, this would be separate search results
        return rewardManager.rewards.filter { reward in
            let matchesText = searchText.isEmpty || 
                             reward.title.localizedCaseInsensitiveContains(searchText) ||
                             reward.description.localizedCaseInsensitiveContains(searchText) ||
                             (reward.partnerName?.localizedCaseInsensitiveContains(searchText) ?? false)
            
            let matchesCategory = selectedCategory == nil || reward.category == selectedCategory
            
            return matchesText && matchesCategory
        }.sorted { reward1, reward2 in
            switch sortBy {
            case .popularity:
                return reward1.popularity > reward2.popularity
            case .tokenCost:
                return reward1.tokenCost < reward2.tokenCost
            case .newest:
                return reward1.createdAt > reward2.createdAt
            case .name:
                return reward1.title < reward2.title
            }
        }
    }
    
    private var popularSearches: [String] {
        ["Coffee", "Food", "Shopping", "Entertainment", "Voucher", "Discount"]
    }
    
    // MARK: - Actions
    private func performSearch() {
        isSearching = true
        
        // Simulate search delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isSearching = false
        }
        
        // In a real app, this would call the search API
        // Task {
        //     await rewardManager.searchRewards(
        //         query: searchText,
        //         category: selectedCategory,
        //         sortBy: sortBy
        //     )
        // }
    }
    
    private func clearSearch() {
        searchText = ""
        selectedCategory = nil
        sortBy = .popularity
        performSearch()
    }
}

// MARK: - Filter Button
struct FilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.surface)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppConstants.Colors.border, lineWidth: isSelected ? 0 : 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Reward Sort Options
enum RewardSortBy: String, CaseIterable {
    case popularity = "popularity"
    case tokenCost = "tokenCost"
    case newest = "newest"
    case name = "name"
    
    var displayName: String {
        switch self {
        case .popularity:
            return "Popularity"
        case .tokenCost:
            return "Token Cost"
        case .newest:
            return "Newest"
        case .name:
            return "Name"
        }
    }
}

// MARK: - Category Filter View
struct CategoryFilterView: View {
    @EnvironmentObject var rewardManager: RewardManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                Section("Categories") {
                    Button(action: {
                        Task {
                            await rewardManager.clearCategoryFilter()
                            dismiss()
                        }
                    }) {
                        HStack {
                            Image(systemName: "square.grid.2x2")
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Text("All Categories")
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Spacer()
                            
                            if rewardManager.selectedCategory == nil {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                    }
                    
                    ForEach(RewardCategory.allCases, id: \.self) { category in
                        Button(action: {
                            Task {
                                await rewardManager.filterByCategory(category)
                                dismiss()
                            }
                        }) {
                            HStack {
                                Image(systemName: category.icon)
                                    .foregroundColor(Color(category.color))
                                
                                Text(category.displayName)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Spacer()
                                
                                if rewardManager.selectedCategory == category {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppConstants.Colors.primary)
                                }
                            }
                        }
                    }
                }
            }
            .navigationTitle("Filter by Category")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - Redemption QR Code View
struct RedemptionQRCodeView: View {
    let redemption: RedemptionWithReward
    @Environment(\.dismiss) private var dismiss
    @State private var qrCodeImage: UIImage?
    
    var body: some View {
        NavigationView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Header
                VStack(spacing: 12) {
                    Text("Redemption QR Code")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Show this QR code to the merchant")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                // QR Code
                VStack(spacing: 16) {
                    if let qrCodeImage = qrCodeImage {
                        Image(uiImage: qrCodeImage)
                            .interpolation(.none)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 250, height: 250)
                            .background(Color.white)
                            .cornerRadius(16)
                            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                    } else {
                        Rectangle()
                            .fill(AppConstants.Colors.surfaceSecondary)
                            .frame(width: 250, height: 250)
                            .cornerRadius(16)
                            .overlay(
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: AppConstants.Colors.primary))
                            )
                    }
                    
                    Text(redemption.redemption.redemptionCode)
                        .font(.system(.title3, design: .monospaced))
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.primary)
                }
                
                Spacer()
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .navigationTitle("QR Code")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .onAppear {
            generateQRCode()
        }
    }
    
    private func generateQRCode() {
        let context = CIContext()
        let filter = CIFilter.qrCodeGenerator()
        
        filter.message = Data(redemption.redemption.redemptionCode.utf8)
        filter.correctionLevel = "M"
        
        if let outputImage = filter.outputImage {
            let transform = CGAffineTransform(scaleX: 10, y: 10)
            let scaledImage = outputImage.transformed(by: transform)
            
            if let cgImage = context.createCGImage(scaledImage, from: scaledImage.extent) {
                qrCodeImage = UIImage(cgImage: cgImage)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    RewardSearchView()
        .environmentObject(RewardManager())
}

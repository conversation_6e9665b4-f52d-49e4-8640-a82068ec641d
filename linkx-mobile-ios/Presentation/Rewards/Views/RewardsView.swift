//
//  RewardsView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import UIKit

struct RewardsView: View {
    enum Tab {
        case catalog
        case myRewards
    }

    @StateObject private var rewardManager = RewardManager()
    @StateObject private var transactionManager = TransactionManager()
    @State private var selectedTab: Tab = .catalog
    @State private var showSearch = false
    @State private var refreshing = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection

                    // Tab Selection
                    tabSelection

                    // Content based on selected tab
                    if selectedTab == .catalog {
                        catalogContent
                    } else {
                        myRewardsContent
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 10)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
        }
        .task {
            await loadInitialData()
        }
        .sheet(isPresented: $showSearch) {
            RewardSearchView()
                .environmentObject(rewardManager)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Text("Rewards")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            Spacer()

            Button(action: {
                showSearch = true
            }) {
                Image(systemName: "magnifyingglass")
                    .font(.title2)
                    .foregroundColor(AppConstants.Colors.primary)
            }
        }
    }

    // MARK: - Tab Selection
    private var tabSelection: some View {
        HStack(spacing: 0) {
            TabSelectorButton(
                title: "Catalog",
                isSelected: selectedTab == .catalog
            ) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = .catalog
                }
            }

            TabSelectorButton(
                title: "My Rewards",
                isSelected: selectedTab == .myRewards
            ) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    selectedTab = .myRewards
                }
            }
        }
    }

    // MARK: - Content Sections
    private var catalogContent: some View {
        RewardsCatalogView()
            .environmentObject(rewardManager)
            .environmentObject(transactionManager)
    }

    private var myRewardsContent: some View {
        MyRedemptionsView()
            .environmentObject(rewardManager)
    }


    
    // MARK: - Data Loading
    private func loadInitialData() async {
        await transactionManager.loadBalance()
        await rewardManager.loadRewards(refresh: true)
        await rewardManager.loadFeaturedRewards()
        await rewardManager.loadPopularRewards()
        await rewardManager.loadRedemptions(refresh: true)
    }
    
    private func refreshData() async {
        refreshing = true
        await rewardManager.refreshAll()
        await transactionManager.loadBalance()
        refreshing = false
    }
}

// MARK: - Tab Selector Button
struct TabSelectorButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)

                Rectangle()
                    .frame(height: 2)
                    .foregroundColor(isSelected ? AppConstants.Colors.primary : Color.clear)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Rewards Catalog View
struct RewardsCatalogView: View {
    @EnvironmentObject var rewardManager: RewardManager
    @EnvironmentObject var transactionManager: TransactionManager
    @State private var showCategoryFilter = false
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                // Balance Card
                balanceCard
                
                // Category Filter
                categoryFilterSection
                
                // Featured Rewards
                if !rewardManager.featuredRewards.isEmpty {
                    featuredRewardsSection
                }
                
                // Popular Rewards
                if !rewardManager.popularRewards.isEmpty {
                    popularRewardsSection
                }
                
                // All Rewards
                allRewardsSection
            }
        }
        .sheet(isPresented: $showCategoryFilter) {
            CategoryFilterView()
                .environmentObject(rewardManager)
        }
    }
    
    // MARK: - Balance Card
    private var balanceCard: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Available Balance")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(transactionManager.balance?.displayBalance ?? "0.00 LXT")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.primary)
            }
            
            Spacer()
            
            Button("Earn More") {
                // TODO: Navigate to earn tokens
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(AppConstants.Colors.primary)
            .cornerRadius(16)
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Category Filter Section
    private var categoryFilterSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Categories")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("Filter") {
                    showCategoryFilter = true
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    CategoryChip(
                        category: nil,
                        isSelected: rewardManager.selectedCategory == nil
                    ) {
                        Task {
                            await rewardManager.clearCategoryFilter()
                        }
                    }
                    
                    ForEach(RewardCategory.allCases, id: \.self) { category in
                        CategoryChip(
                            category: category,
                            isSelected: rewardManager.selectedCategory == category
                        ) {
                            Task {
                                await rewardManager.filterByCategory(category)
                            }
                        }
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .padding(.horizontal, -AppConstants.UI.screenPadding)
        }
    }
    
    // MARK: - Featured Rewards Section
    private var featuredRewardsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Featured Rewards")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 16) {
                    ForEach(rewardManager.featuredRewards) { reward in
                        RewardCardView(reward: reward)
                            .frame(width: 280)
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .padding(.horizontal, -AppConstants.UI.screenPadding)
        }
    }
    
    // MARK: - Popular Rewards Section
    private var popularRewardsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Popular This Week")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 16) {
                    ForEach(rewardManager.popularRewards) { reward in
                        RewardCardView(reward: reward)
                            .frame(width: 280)
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .padding(.horizontal, -AppConstants.UI.screenPadding)
        }
    }
    
    // MARK: - All Rewards Section
    private var allRewardsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("All Rewards")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let category = rewardManager.selectedCategory {
                    Text("• \(category.displayName)")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                Text("\(rewardManager.rewards.count) rewards")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            if rewardManager.rewards.isEmpty {
                if rewardManager.isLoading {
                    VStack {
                        ProgressView()
                        Text("Loading rewards...")
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    VStack(spacing: 16) {
                        Image(systemName: "gift")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        Text("No Rewards Available")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("Check back later for new rewards!")
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Refresh") {
                            Task {
                                await rewardManager.loadRewards(refresh: true)
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    ForEach(rewardManager.rewards) { reward in
                        RewardCardView(reward: reward)
                    }
                }
                
                // Load More Button
                if rewardManager.rewards.count >= AppConstants.Pagination.defaultLimit {
                    Button("Load More Rewards") {
                        Task {
                            await rewardManager.loadRewards()
                        }
                    }
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding()
                }
            }
        }
    }
}

// MARK: - Category Chip
struct CategoryChip: View {
    let category: RewardCategory?
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                if let category = category {
                    Image(systemName: category.icon)
                        .font(.caption)
                }
                
                Text(category?.displayName ?? "All")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.surface)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(AppConstants.Colors.border, lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    RewardsView()
}

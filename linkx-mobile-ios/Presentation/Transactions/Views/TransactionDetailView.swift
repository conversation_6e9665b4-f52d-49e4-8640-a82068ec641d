//
//  TransactionDetailView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct TransactionDetailView: View {
    let transaction: Transaction
    @Environment(\.dismiss) private var dismiss
    @State private var showShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Status Header
                    statusHeaderSection
                    
                    // Amount Section
                    amountSection
                    
                    // Transaction Details
                    transactionDetailsSection
                    
                    // Blockchain Details
                    blockchainDetailsSection
                    
                    // Additional Info
                    if transaction.description != nil || transaction.metadata != nil {
                        additionalInfoSection
                    }
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Transaction Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Close") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>(action: {
                        showShareSheet = true
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .sheet(isPresented: $showShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    // MARK: - Status Header Section
    private var statusHeaderSection: some View {
        VStack(spacing: 16) {
            // Status Icon
            Image(systemName: transaction.typeIcon)
                .font(.system(size: 60))
                .foregroundColor(statusIconColor)
                .frame(width: 100, height: 100)
                .background(statusIconColor.opacity(0.1))
                .cornerRadius(50)
            
            // Status Text
            VStack(spacing: 4) {
                Text(transaction.status.displayName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(statusTextColor)
                
                Text(transaction.typeDisplayName)
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Status Badge
            HStack(spacing: 6) {
                Circle()
                    .fill(statusIconColor)
                    .frame(width: 8, height: 8)
                
                Text(transaction.status.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(statusTextColor)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(statusIconColor.opacity(0.1))
            .cornerRadius(16)
        }
    }
    
    // MARK: - Amount Section
    private var amountSection: some View {
        VStack(spacing: 12) {
            // LXT Amount
            HStack {
                Text(amountPrefix)
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(amountColor)
                
                Text(transaction.displayAmount)
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(amountColor)
            }
            
            // VND Equivalent
            if let vndAmount = transaction.displayAmountVnd {
                Text("≈ \(vndAmount)")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Date
            Text(transaction.createdAt.formatted(style: .full))
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Transaction Details Section
    private var transactionDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Transaction Details")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                DetailRow(
                    title: "Transaction ID",
                    value: transaction.id,
                    copyable: true
                )
                
                DetailRow(
                    title: "Type",
                    value: transaction.typeDisplayName
                )
                
                DetailRow(
                    title: "Status",
                    value: transaction.status.displayName
                )
                
                DetailRow(
                    title: "Date & Time",
                    value: transaction.createdAt.formatted(format: "MMM dd, yyyy 'at' HH:mm")
                )
                
                if let merchantId = transaction.merchantId {
                    DetailRow(
                        title: "Merchant ID",
                        value: merchantId
                    )
                }
                
                if let commissionRate = transaction.commissionRate {
                    DetailRow(
                        title: "Commission Rate",
                        value: "\(String(format: "%.1f", commissionRate * 100))%"
                    )
                }
                
                if let merchantCommission = transaction.merchantCommission {
                    DetailRow(
                        title: "Merchant Commission",
                        value: merchantCommission.formatAsCurrency()
                    )
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Blockchain Details Section
    private var blockchainDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Blockchain Details")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if let txHash = transaction.blockchainTxHash {
                    DetailRow(
                        title: "Transaction Hash",
                        value: txHash,
                        copyable: true
                    )
                    
                    Button("View on Solana Explorer") {
                        if let url = URL(string: "https://explorer.solana.com/tx/\(txHash)?cluster=devnet") {
                            UIApplication.shared.open(url)
                        }
                    }
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.top, 8)
                } else {
                    DetailRow(
                        title: "Blockchain Status",
                        value: "Processing..."
                    )
                }
                
                DetailRow(
                    title: "Network",
                    value: "Solana Devnet"
                )
                
                if transaction.retryCount ?? 0 > 0 {
                    DetailRow(
                        title: "Retry Count",
                        value: "\(transaction.retryCount ?? 0)"
                    )
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Additional Info Section
    private var additionalInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Additional Information")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if let description = transaction.description {
                    DetailRow(
                        title: "Description",
                        value: description
                    )
                }
                
                if let errorMessage = transaction.errorMessage {
                    DetailRow(
                        title: "Error Message",
                        value: errorMessage,
                        valueColor: AppConstants.Colors.error
                    )
                }
                
                if let updatedAt = transaction.updatedAt {
                    DetailRow(
                        title: "Last Updated",
                        value: updatedAt.formatted(format: "MMM dd, yyyy 'at' HH:mm")
                    )
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Computed Properties
    private var statusIconColor: Color {
        switch transaction.status {
        case .pending:
            return AppConstants.Colors.warning
        case .completed:
            return AppConstants.Colors.success
        case .failed:
            return AppConstants.Colors.error
        }
    }
    
    private var statusTextColor: Color {
        switch transaction.status {
        case .pending:
            return AppConstants.Colors.warning
        case .completed:
            return AppConstants.Colors.success
        case .failed:
            return AppConstants.Colors.error
        }
    }
    
    private var amountColor: Color {
        switch transaction.type {
        case .earn, .transferIn:
            return AppConstants.Colors.success
        case .redeem, .transferOut:
            return AppConstants.Colors.error
        }
    }
    
    private var amountPrefix: String {
        switch transaction.type {
        case .earn, .transferIn:
            return "+"
        case .redeem, .transferOut:
            return "-"
        }
    }
    
    // MARK: - Helper Methods
    private func generateShareText() -> String {
        var text = "LinkX Transaction Details\n\n"
        text += "Type: \(transaction.typeDisplayName)\n"
        text += "Amount: \(amountPrefix)\(transaction.displayAmount)\n"
        text += "Status: \(transaction.status.displayName)\n"
        text += "Date: \(transaction.createdAt.formatted(style: .full))\n"
        
        if let txHash = transaction.blockchainTxHash {
            text += "Transaction Hash: \(txHash)\n"
        }
        
        return text
    }
}

// MARK: - Detail Row
struct DetailRow: View {
    let title: String
    let value: String
    let copyable: Bool
    let valueColor: Color
    @State private var showCopied = false
    
    init(title: String, value: String, copyable: Bool = false, valueColor: Color = AppConstants.Colors.textPrimary) {
        self.title = title
        self.value = value
        self.copyable = copyable
        self.valueColor = valueColor
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            HStack {
                Text(value)
                    .font(.subheadline)
                    .foregroundColor(valueColor)
                    .textSelection(.enabled)
                
                Spacer()
                
                if copyable {
                    Button(action: {
                        UIPasteboard.general.string = value
                        showCopied = true
                        HapticManager.shared.trigger(.success)
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            showCopied = false
                        }
                    }) {
                        Image(systemName: showCopied ? "checkmark" : "doc.on.doc")
                            .font(.caption)
                            .foregroundColor(showCopied ? AppConstants.Colors.success : AppConstants.Colors.primary)
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview
#Preview {
    TransactionDetailView(
        transaction: Transaction(
            id: "tx_123456789",
            userId: nil,  // userId is now optional
            type: .earn,
            amountVnd: 50000,
            amountLxt: 50,
            merchantId: "merchant1",
            merchantCommission: 1000,
            commissionRate: 0.02,
            platformProfit: 800,
            blockchainTxHash: "5J7XqWqJxvQx8rGdHuANBxQqKzYzYzYzYzYzYzYzYzYzYzYzYzYzYzYzYzYzYzYz",
            status: .completed,
            description: "Coffee purchase at Starbucks Central Park",
            metadata: nil,
            errorMessage: nil,
            retryCount: 0,
            createdAt: Date().addingTimeInterval(-3600),
            updatedAt: Date()
        )
    )
}

//
//  ReceiveTokensView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import CoreImage.CIFilterBuiltins

struct ReceiveTokensView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var qrCodeImage: UIImage?
    @State private var showShareSheet = false
    @State private var showCopiedAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // QR Code Section
                    qrCodeSection
                    
                    // Wallet Address Section
                    walletAddressSection
                    
                    // Instructions Section
                    instructionsSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Receive Tokens")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showShareSheet = true
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .onAppear {
            generateQRCode()
        }
        .sheet(isPresented: $showShareSheet) {
            if let qrCodeImage = qrCodeImage,
               let walletAddress = authViewModel.currentUser?.walletAddress {
                ShareSheet(items: [qrCodeImage, walletAddress])
            }
        }
        .alert("Copied!", isPresented: $showCopiedAlert) {
            Button("OK") { }
        } message: {
            Text("Wallet address copied to clipboard")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "arrow.down.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.secondary)
            
            Text("Receive LXT Tokens")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Share your wallet address or QR code to receive tokens")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - QR Code Section
    private var qrCodeSection: some View {
        VStack(spacing: 16) {
            Text("QR Code")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 16) {
                if let qrCodeImage = qrCodeImage {
                    Image(uiImage: qrCodeImage)
                        .interpolation(.none)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 200, height: 200)
                        .background(Color.white)
                        .cornerRadius(16)
                        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                } else {
                    Rectangle()
                        .fill(AppConstants.Colors.surfaceSecondary)
                        .frame(width: 200, height: 200)
                        .cornerRadius(16)
                        .overlay(
                            VStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: AppConstants.Colors.primary))
                                
                                Text("Generating QR Code...")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .padding(.top, 8)
                            }
                        )
                }
                
                Text("Scan this QR code to send tokens to your wallet")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
            .background(AppConstants.Colors.surface)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
    }
    
    // MARK: - Wallet Address Section
    private var walletAddressSection: some View {
        VStack(spacing: 16) {
            Text("Wallet Address")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let walletAddress = authViewModel.currentUser?.walletAddress {
                VStack(spacing: 12) {
                    Text(walletAddress)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .padding()
                        .background(AppConstants.Colors.surfaceSecondary)
                        .cornerRadius(8)
                        .multilineTextAlignment(.center)
                    
                    Button(action: {
                        UIPasteboard.general.string = walletAddress
                        showCopiedAlert = true
                        HapticManager.shared.trigger(.success)
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "doc.on.doc")
                                .font(.subheadline)
                            Text("Copy Address")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(AppConstants.Colors.primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(AppConstants.Colors.primary.opacity(0.1))
                        .cornerRadius(20)
                    }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Instructions Section
    private var instructionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("How to Receive Tokens")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 12) {
                InstructionRow(
                    number: "1",
                    title: "Share Your Address",
                    description: "Send your wallet address or QR code to the sender"
                )
                
                InstructionRow(
                    number: "2",
                    title: "Wait for Transfer",
                    description: "The sender initiates the transfer from their wallet"
                )
                
                InstructionRow(
                    number: "3",
                    title: "Receive Confirmation",
                    description: "You'll get a notification when tokens arrive in your wallet"
                )
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Helper Methods
    private func generateQRCode() {
        guard let walletAddress = authViewModel.currentUser?.walletAddress else { return }
        
        let context = CIContext()
        let filter = CIFilter.qrCodeGenerator()
        
        filter.message = Data(walletAddress.utf8)
        filter.correctionLevel = "M"
        
        if let outputImage = filter.outputImage {
            let transform = CGAffineTransform(scaleX: 10, y: 10)
            let scaledImage = outputImage.transformed(by: transform)
            
            if let cgImage = context.createCGImage(scaledImage, from: scaledImage.extent) {
                qrCodeImage = UIImage(cgImage: cgImage)
            }
        }
    }
}

// MARK: - Instruction Row
struct InstructionRow: View {
    let number: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(number)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(AppConstants.Colors.primary)
                .cornerRadius(12)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Share Sheet
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Send Confirmation View
struct SendConfirmationView: View {
    let recipientAddress: String
    let amount: Double
    let note: String
    let onConfirm: (Bool) -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Header
                VStack(spacing: 12) {
                    Image(systemName: "checkmark.shield")
                        .font(.system(size: 60))
                        .foregroundColor(AppConstants.Colors.warning)
                    
                    Text("Confirm Transaction")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Please review the transaction details before sending")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                // Transaction Details
                VStack(spacing: 16) {
                    TransactionDetailRow(
                        title: "Recipient",
                        value: recipientAddress.truncated(to: 20)
                    )
                    
                    TransactionDetailRow(
                        title: "Amount",
                        value: "\(String(format: "%.2f", amount)) LXT"
                    )
                    
                    TransactionDetailRow(
                        title: "Equivalent",
                        value: "≈ \(String(format: "%.0f", amount * AppConstants.Transaction.exchangeRate)) VND"
                    )
                    
                    if !note.isEmpty {
                        TransactionDetailRow(
                            title: "Note",
                            value: note
                        )
                    }
                    
                    TransactionDetailRow(
                        title: "Network Fee",
                        value: "0.001 SOL"
                    )
                }
                .cardStyle()
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button("Confirm & Send") {
                        onConfirm(true)
                        dismiss()
                    }
                    .primaryButtonStyle()
                    
                    Button("Cancel") {
                        onConfirm(false)
                        dismiss()
                    }
                    .secondaryButtonStyle()
                }
                .padding(.bottom, 40)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .navigationTitle("Confirm")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Back") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
    }
}

// MARK: - Transaction Detail Row
struct TransactionDetailRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview
#Preview {
    ReceiveTokensView()
        .environmentObject(AuthViewModel())
}

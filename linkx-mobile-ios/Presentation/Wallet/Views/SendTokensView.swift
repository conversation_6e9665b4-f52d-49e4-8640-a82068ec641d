//
//  SendTokensView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct SendTokensView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var transactionManager = TransactionManager.shared
    @State private var recipientAddress = ""
    @State private var amount = ""
    @State private var note = ""
    @State private var showQRScanner = false
    @State private var showConfirmation = false
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var errorMessage: String?
    @State private var showError = false
    @FocusState private var focusedField: SendTokensField?
    
    enum SendTokensField {
        case address, amount, note
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // Balance Display
                    balanceSection
                    
                    // Send Form
                    sendFormSection
                    
                    // Recent Recipients
                    recentRecipientsSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Send Tokens")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
            .safeAreaInset(edge: .bottom) {
                sendButtonSection
            }
        }
        .task {
            await transactionManager.loadBalance()
        }
        .sheet(isPresented: $showQRScanner) {
            QRScannerView { scannedAddress in
                recipientAddress = scannedAddress
            }
        }
        .sheet(isPresented: $showConfirmation) {
            SendConfirmationView(
                recipientAddress: recipientAddress,
                amount: Double(amount) ?? 0,
                note: note
            ) { confirmed in
                if confirmed {
                    Task {
                        await sendTokens()
                    }
                }
            }
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Tokens sent successfully!")
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = nil
            }
        } message: {
            Text(errorMessage ?? "An error occurred")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "arrow.up.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.primary)
            
            Text("Send LXT Tokens")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Transfer tokens to another LinkX wallet")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Balance Section
    private var balanceSection: some View {
        VStack(spacing: 8) {
            Text("Available Balance")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text(transactionManager.balance?.displayBalance ?? "0.00 LXT")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.primary)
            
            Text("≈ \(transactionManager.balance?.displayVndEquivalent ?? "0 VND")")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding()
        .background(AppConstants.Colors.primary.opacity(0.05))
        .cornerRadius(AppConstants.UI.cornerRadius)
    }
    
    // MARK: - Send Form Section
    private var sendFormSection: some View {
        VStack(spacing: AppConstants.UI.itemSpacing) {
            // Recipient Address
            VStack(alignment: .leading, spacing: 8) {
                Text("Recipient Address")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                HStack {
                    TextField("Enter wallet address or scan QR", text: $recipientAddress)
                        .textFieldStyle()
                        .focused($focusedField, equals: .address)
                        .onSubmit {
                            focusedField = .amount
                        }
                    
                    Button(action: {
                        showQRScanner = true
                    }) {
                        Image(systemName: "qrcode.viewfinder")
                            .font(.title2)
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 44, height: 44)
                            .background(AppConstants.Colors.primary.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                
                if !recipientAddress.isEmpty && !isValidAddress(recipientAddress) {
                    Text("Please enter a valid wallet address")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
            
            // Amount
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Amount (LXT)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Spacer()
                    
                    Button("Max") {
                        amount = String(transactionManager.balance?.lxtBalance ?? 0)
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.primary.opacity(0.1))
                    .cornerRadius(6)
                }
                
                TextField("0.00", text: $amount)
                    .textFieldStyle()
                    .keyboardType(.decimalPad)
                    .focused($focusedField, equals: .amount)
                    .onSubmit {
                        focusedField = .note
                    }
                
                if let amountValue = Double(amount), amountValue > 0 {
                    let vndAmount = amountValue * AppConstants.Transaction.exchangeRate
                    Text("≈ \(String(format: "%.0f", vndAmount)) VND")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                if let amountValue = Double(amount), 
                   let balance = transactionManager.balance,
                   amountValue > balance.lxtBalance {
                    Text("Insufficient balance")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
            
            // Note (Optional)
            VStack(alignment: .leading, spacing: 8) {
                Text("Note (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                TextField("Add a note for this transaction", text: $note)
                    .textFieldStyle()
                    .focused($focusedField, equals: .note)
            }
        }
        .cardStyle()
        .padding(.horizontal, 4)
    }
    
    // MARK: - Recent Recipients Section
    private var recentRecipientsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Recipients")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            // Placeholder for recent recipients
            VStack(spacing: 8) {
                ForEach(0..<3) { index in
                    RecentRecipientRow(
                        address: "0x1234...5678",
                        name: "Contact \(index + 1)",
                        lastAmount: "25.00 LXT"
                    ) { address in
                        recipientAddress = address
                    }
                }
            }
            .cardStyle()
        }
    }
    
    // MARK: - Send Button Section
    private var sendButtonSection: some View {
        VStack(spacing: 12) {
            Button(action: {
                showConfirmation = true
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    
                    Text(isLoading ? "Sending..." : "Review & Send")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
            }
            .primaryButtonStyle()
            .disabled(!isFormValid || isLoading)
            .opacity(isFormValid ? 1.0 : 0.6)
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        guard let amountValue = Double(amount),
              let balance = transactionManager.balance else { return false }
        
        return !recipientAddress.isEmpty &&
               isValidAddress(recipientAddress) &&
               amountValue > 0 &&
               amountValue <= balance.lxtBalance
    }
    
    // MARK: - Helper Methods
    private func isValidAddress(_ address: String) -> Bool {
        // Simple validation - in real app, validate Solana address format
        return address.count >= 32 && address.count <= 44
    }
    
    private func sendTokens() async {
        guard let amountValue = Double(amount) else { return }
        
        isLoading = true
        
        let success = await transactionManager.transferTokens(
            recipientAddress: recipientAddress,
            amountLxt: amountValue,
            description: note.isEmpty ? nil : note
        )
        
        if success {
            showSuccess = true
        } else if let error = transactionManager.error {
            errorMessage = error.localizedDescription
            showError = true
        }
        
        isLoading = false
    }
}

// MARK: - Recent Recipient Row
struct RecentRecipientRow: View {
    let address: String
    let name: String
    let lastAmount: String
    let onSelect: (String) -> Void
    
    var body: some View {
        Button(action: {
            onSelect(address)
        }) {
            HStack(spacing: 12) {
                Circle()
                    .fill(AppConstants.Colors.primary.opacity(0.1))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(name.prefix(1))
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.primary)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(address)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                Text(lastAmount)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    SendTokensView()
}

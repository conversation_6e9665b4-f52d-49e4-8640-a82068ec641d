//
//  WalletView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct WalletView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var transactionManager = TransactionManager.shared
    @State private var selectedFilter: TransactionType?
    @State private var showSendTokens = false
    @State private var showReceiveTokens = false
    @State private var showTransactionDetail = false
    @State private var selectedTransaction: Transaction?
    @State private var refreshing = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection

                    // Wallet Balance Card
                    walletBalanceCard

                    // Quick Actions
                    quickActionsSection

                    // Transaction Filters
                    transactionFiltersSection

                    // Transaction History
                    transactionHistorySection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 10)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
        }
        .task {
            await loadInitialData()
        }
        .sheet(isPresented: $showSendTokens) {
            SendTokensView()
        }
        .sheet(isPresented: $showReceiveTokens) {
            ReceiveTokensView()
        }
        .sheet(item: $selectedTransaction) { transaction in
            TransactionDetailView(transaction: transaction)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Text("Wallet")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            Spacer()

            Button(action: {
                // TODO: Add QR scan functionality
                showReceiveTokens = true
            }) {
                Image(systemName: "qrcode.viewfinder")
                    .font(.title2)
                    .foregroundColor(AppConstants.Colors.primary)
            }
        }
    }

    // MARK: - Wallet Balance Card
    private var walletBalanceCard: some View {
        VStack(spacing: 20) {
            // Balance Display
            VStack(spacing: 8) {
                Text("Total Balance")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                
                Text(transactionManager.balance?.displayBalance ?? "0.00 LXT")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text("≈ \(transactionManager.balance?.displayVndEquivalent ?? "0 VND")")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            // Wallet Address
            if let user = authViewModel.currentUser {
                VStack(spacing: 8) {
                    Text("Wallet Address")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    HStack {
                        Text(user.walletAddress.truncated(to: 20))
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(.white.opacity(0.1))
                            .cornerRadius(12)
                        
                        Button(action: {
                            UIPasteboard.general.string = user.walletAddress
                            HapticManager.shared.trigger(.success)
                        }) {
                            Image(systemName: "doc.on.doc")
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(8)
                                .background(.white.opacity(0.2))
                                .cornerRadius(8)
                        }
                    }
                }
            }
            
            // Action Buttons
            HStack(spacing: 16) {
                Button(action: {
                    showSendTokens = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.up.circle")
                            .font(.subheadline)
                        Text("Send")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 40)
                    .background(.white)
                    .cornerRadius(20)
                }
                
                Button(action: {
                    showReceiveTokens = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.down.circle")
                            .font(.subheadline)
                        Text("Receive")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 40)
                    .background(.white.opacity(0.2))
                    .cornerRadius(20)
                }
            }
        }
        .padding(24)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDark]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        HStack(spacing: 12) {
            QuickWalletAction(
                icon: "qrcode.viewfinder",
                title: "Scan & Pay",
                color: AppConstants.Colors.primary
            ) {
                // TODO: Open QR scanner
            }
            
            QuickWalletAction(
                icon: "chart.line.uptrend.xyaxis",
                title: "Analytics",
                color: AppConstants.Colors.secondary
            ) {
                // TODO: Show analytics
            }
            
            QuickWalletAction(
                icon: "clock.arrow.circlepath",
                title: "History",
                color: AppConstants.Colors.accent
            ) {
                // TODO: Show full history
            }
        }
    }
    
    // MARK: - Transaction Filters Section
    private var transactionFiltersSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Transaction History")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterChip(
                        title: "All",
                        isSelected: selectedFilter == nil
                    ) {
                        selectedFilter = nil
                    }
                    
                    ForEach(TransactionType.allCases, id: \.self) { type in
                        FilterChip(
                            title: type.displayName,
                            isSelected: selectedFilter == type
                        ) {
                            selectedFilter = type
                        }
                    }
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .padding(.horizontal, -AppConstants.UI.screenPadding)
        }
    }
    
    // MARK: - Transaction History Section
    private var transactionHistorySection: some View {
        VStack(spacing: 12) {
            if filteredTransactions.isEmpty {
                if transactionManager.isLoading {
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Loading transactions...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: 100)
                } else {
                    VStack(spacing: 16) {
                        Image(systemName: "creditcard")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        VStack(spacing: 8) {
                            Text("No Transactions Yet")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Text("Start earning tokens to see your transaction history here")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }

                        Button("Earn Tokens") {
                            // TODO: Navigate to earn tokens
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: 200)
                }
            } else {
                LazyVStack(spacing: 0) {
                    ForEach(filteredTransactions) { transaction in
                        TransactionRowView(transaction: transaction)
                            .onTapGesture {
                                selectedTransaction = transaction
                            }
                        
                        if transaction.id != filteredTransactions.last?.id {
                            Divider()
                                .padding(.leading, 64)
                        }
                    }
                }
                .cardStyle()
                
                // Load More Button
                if transactionManager.transactions.count >= AppConstants.Pagination.defaultLimit {
                    Button("Load More") {
                        Task {
                            await transactionManager.loadTransactions()
                        }
                    }
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding()
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var filteredTransactions: [Transaction] {
        return transactionManager.filterTransactions(by: selectedFilter)
    }
    
    // MARK: - Data Loading
    private func loadInitialData() async {
        await transactionManager.loadBalance()
        await transactionManager.loadTransactions(refresh: true)
    }
    
    private func refreshData() async {
        refreshing = true
        await transactionManager.refreshAll()
        refreshing = false
    }
}

// MARK: - Quick Wallet Action
struct QuickWalletAction: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(AppConstants.Colors.surface)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Filter Chip
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.surface)
                .cornerRadius(20)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppConstants.Colors.border, lineWidth: isSelected ? 0 : 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    WalletView()
        .environmentObject(AuthViewModel())
}

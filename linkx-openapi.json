{"openapi": "3.0.0", "paths": {"/api": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/api/health": {"get": {"operationId": "HealthController_getHealth", "parameters": [], "responses": {"200": {"description": "Service is alive"}}, "summary": "Basic health check (liveness probe)", "tags": ["Health"]}}, "/api/health/ready": {"get": {"operationId": "HealthController_getReadiness", "parameters": [], "responses": {"200": {"description": "The Health Check is successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}, "503": {"description": "The Health Check is not successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {"redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}, "redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}}, "summary": "Comprehensive readiness check", "tags": ["Health"]}}, "/api/health/live": {"get": {"operationId": "HealthController_getLiveness", "parameters": [], "responses": {"200": {"description": "Service is alive"}}, "summary": "Liveness probe for Kubernetes", "tags": ["Health"]}}, "/api/health/startup": {"get": {"operationId": "HealthController_getStartup", "parameters": [], "responses": {"200": {"description": "The Health Check is successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}, "503": {"description": "The Health Check is not successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {"redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}, "redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}}, "summary": "Startup probe for Kubernetes", "tags": ["Health"]}}, "/api/v1/auth/register": {"post": {"operationId": "AuthV1Controller_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication v1"]}}, "/api/v1/auth/login": {"post": {"operationId": "AuthV1Controller_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication v1"]}}, "/api/v1/auth/merchant/register": {"post": {"operationId": "AuthV1Controller_registerMerchant", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantRegisterDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication v1"]}}, "/api/v1/auth/merchant/login": {"post": {"operationId": "AuthV1Controller_loginMerchant", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantLoginDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Authentication v1"]}}, "/api/v1/users/profile": {"get": {"description": "Get current user profile information including wallet address and balance.", "operationId": "UsersV1Controller_getProfile", "parameters": [], "responses": {"200": {"description": "User profile retrieved successfully"}}, "security": [{"JWT-auth": []}], "summary": "Get user profile (v1)", "tags": ["Users v1"]}, "put": {"description": "Update user profile information.", "operationId": "UsersV1Controller_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserProfileDto"}}}}, "responses": {"200": {"description": "User profile updated successfully"}}, "security": [{"JWT-auth": []}], "summary": "Update user profile (v1)", "tags": ["Users v1"]}}, "/api/v1/users/balance": {"get": {"description": "Get current user LXT token balance and VND equivalent.", "operationId": "UsersV1Controller_getBalance", "parameters": [], "responses": {"200": {"description": "User balance retrieved successfully"}}, "security": [{"JWT-auth": []}], "summary": "Get user token balance (v1)", "tags": ["Users v1"]}}, "/api/v1/transactions/earn": {"post": {"description": "Earn LXT tokens by making a purchase at a merchant partner.", "operationId": "TransactionsV1Controller_earnTokens", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EarnTokensDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> earned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionResponseDto"}}}}, "400": {"description": "Invalid transaction amount"}, "404": {"description": "Merchant not found"}}, "security": [{"JWT-auth": []}], "summary": "Earn LXT tokens (v1)", "tags": ["Transactions v1"]}}, "/api/v1/transactions/balance": {"get": {"description": "Get current user token balance in LXT and VND equivalent.", "operationId": "TransactionsV1Controller_getBalance", "parameters": [], "responses": {"200": {"description": "Current token balance", "content": {"application/json": {"schema": {"type": "object", "properties": {"balance": {"type": "number", "example": 0.5}, "balanceVnd": {"type": "number", "example": 5}}}}}}}, "security": [{"JWT-auth": []}], "summary": "Get user token balance (v1)", "tags": ["Transactions v1"]}}, "/api/v1/transactions/history": {"get": {"description": "Get user transaction history with pagination.", "operationId": "TransactionsV1Controller_getHistory", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of transactions per page", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of transactions to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "Transaction history", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionHistoryDto"}}}}}, "security": [{"JWT-auth": []}], "summary": "Get transaction history (v1)", "tags": ["Transactions v1"]}}, "/api/v1/transactions/{id}": {"get": {"description": "Get detailed information about a specific transaction.", "operationId": "TransactionsV1Controller_getTransaction", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Transaction ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionResponseDto"}}}}, "404": {"description": "Transaction not found"}}, "security": [{"JWT-auth": []}], "summary": "Get transaction by ID (v1)", "tags": ["Transactions v1"]}}, "/api/v1/rewards": {"get": {"description": "Get list of available rewards that users can redeem with LXT tokens.", "operationId": "RewardsV1Controller_getRewards", "parameters": [{"name": "category", "required": false, "in": "query", "description": "Filter by reward category", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of rewards to return", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of rewards to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> retrieved successfully"}}, "summary": "Get available rewards (v1)", "tags": ["Rewards v1"]}}, "/api/v1/rewards/{id}": {"get": {"description": "Get detailed information about a specific reward.", "operationId": "RewardsV1Controller_getReward", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Reward ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Reward details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RewardResponseDto"}}}}, "404": {"description": "<PERSON><PERSON> not found"}}, "summary": "Get reward details (v1)", "tags": ["Rewards v1"]}}, "/api/v1/rewards/{id}/redeem": {"post": {"description": "Redeem a reward using LXT tokens. Returns redemption code for merchant verification.", "operationId": "RewardsV1Controller_redeemReward", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Reward ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RedeemRewardDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON> redeemed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RedemptionResponseDto"}}}}, "400": {"description": "Insufficient tokens or reward not available"}, "404": {"description": "<PERSON><PERSON> not found"}}, "security": [{"JWT-auth": []}], "summary": "Redeem a reward (v1)", "tags": ["Rewards v1"]}}, "/api/v1/rewards/user/redemptions": {"get": {"description": "Get current user redemption history with pagination.", "operationId": "RewardsV1Controller_getUserRedemptions", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of redemptions per page", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of redemptions to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "User redemption history", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RedemptionResponseDto"}}}}}}, "security": [{"JWT-auth": []}], "summary": "Get user redemption history (v1)", "tags": ["Rewards v1"]}}, "/api/v1/admin/stats": {"get": {"description": "Get comprehensive platform statistics including users, transactions, and revenue.", "operationId": "AdminV1Controller_getStats", "parameters": [], "responses": {"200": {"description": "Admin statistics retrieved successfully"}}, "security": [{"JWT-auth": []}], "summary": "Get admin statistics (v1)", "tags": ["Admin v1"]}}, "/api/v1/admin/rewards": {"get": {"description": "Get all rewards including inactive ones for admin management.", "operationId": "AdminV1Controller_getRewards", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of rewards to return", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of rewards to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> retrieved successfully"}}, "security": [{"JWT-auth": []}], "summary": "Get all rewards for admin (v1)", "tags": ["Admin v1"]}, "post": {"description": "Create a new reward for the platform.", "operationId": "AdminV1Controller_createReward", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateRewardDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON> created successfully"}}, "security": [{"JWT-auth": []}], "summary": "Create new reward (v1)", "tags": ["Admin v1"]}}, "/api/v1/admin/rewards/{id}": {"put": {"description": "Update an existing reward.", "operationId": "AdminV1Controller_updateReward", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Reward ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRewardDto"}}}}, "responses": {"200": {"description": "Reward updated successfully"}}, "security": [{"JWT-auth": []}], "summary": "Update reward (v1)", "tags": ["Admin v1"]}, "delete": {"description": "Delete a reward from the platform.", "operationId": "AdminV1Controller_deleteReward", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Reward ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> deleted successfully"}}, "security": [{"JWT-auth": []}], "summary": "Delete reward (v1)", "tags": ["Admin v1"]}}, "/api/v1/merchants": {"get": {"description": "Get list of all merchants (Admin only).", "operationId": "MerchantsV1Controller_findAll", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of merchants to return", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of merchants to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "Merchants retrieved successfully"}}, "security": [{"JWT-auth": []}], "summary": "Get all merchants (v1)", "tags": ["Merchants v1"]}, "post": {"description": "Create a new merchant (Admin only).", "operationId": "MerchantsV1Controller_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMerchantDto"}}}}, "responses": {"201": {"description": "Merchant created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantResponseDto"}}}}}, "security": [{"JWT-auth": []}], "summary": "Create merchant (v1)", "tags": ["Merchants v1"]}}, "/api/v1/merchants/{id}": {"get": {"description": "Get merchant details by ID (Admin only).", "operationId": "MerchantsV1Controller_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Merchant ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Merchant details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantResponseDto"}}}}}, "security": [{"JWT-auth": []}], "summary": "Get merchant by ID (v1)", "tags": ["Merchants v1"]}, "put": {"description": "Update a merchant (Admin only).", "operationId": "MerchantsV1Controller_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Merchant ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMerchantDto"}}}}, "responses": {"200": {"description": "Merchant updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantResponseDto"}}}}}, "security": [{"JWT-auth": []}], "summary": "Update merchant (v1)", "tags": ["Merchants v1"]}, "delete": {"description": "Delete a merchant (Admin only).", "operationId": "MerchantsV1Controller_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Merchant ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Merchant deleted successfully"}}, "security": [{"JWT-auth": []}], "summary": "Delete merchant (v1)", "tags": ["Merchants v1"]}}, "/api/v1/merchants/{id}/generate-api-key": {"post": {"description": "Generate API key for merchant (Admin only).", "operationId": "MerchantsV1Controller_generateApiKey", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Merchant ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "API key generated successfully"}}, "security": [{"JWT-auth": []}], "summary": "Generate API key for merchant (v1)", "tags": ["Merchants v1"]}}, "/api/v1/ocr/process-receipt": {"post": {"description": "Upload a receipt image to extract data via OCR and earn LXT tokens based on the purchase amount.", "operationId": "OCRV1Controller_processReceipt", "parameters": [], "requestBody": {"required": true, "description": "Receipt image file", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"receipt": {"type": "string", "format": "binary", "description": "Receipt image file (max 10MB, formats: jpg, jpeg, png, gif, webp)"}, "notes": {"type": "string", "description": "Optional notes about the receipt", "example": "Lunch with client"}}, "required": ["receipt"]}}}}, "responses": {"201": {"description": "Receipt processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptProcessingResultDto"}}}}, "400": {"description": "Invalid file format or processing error"}, "401": {"description": "Unauthorized - JWT token required"}, "413": {"description": "File too large - maximum 10MB allowed"}}, "security": [{"JWT-auth": []}], "summary": "Process receipt image for OCR and token earning", "tags": ["OCR v1"]}}, "/api/v1/ocr/process-receipt-url": {"post": {"description": "Process a receipt image from a URL to extract data via OCR and earn LXT tokens.", "operationId": "OCRV1Controller_processReceiptFromUrl", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessReceiptUrlDto"}}}}, "responses": {"201": {"description": "Receipt processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptProcessingResultDto"}}}}, "400": {"description": "Invalid URL or processing error"}, "401": {"description": "Unauthorized - JWT token required"}}, "security": [{"JWT-auth": []}], "summary": "Process receipt from image URL", "tags": ["OCR v1"]}}, "/api/v1/ocr/history": {"get": {"description": "Retrieve the history of processed receipts for the authenticated user.", "operationId": "OCRV1Controller_getReceiptHistory", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of receipts to return (default: 20, max: 100)", "schema": {"example": 20, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of receipts to skip (default: 0)", "schema": {"example": 0, "type": "number"}}], "responses": {"200": {"description": "Receipt history retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptHistoryDto"}}}}, "401": {"description": "Unauthorized - JWT token required"}}, "security": [{"JWT-auth": []}], "summary": "Get user receipt processing history", "tags": ["OCR v1"]}}, "/api/v1/ocr/receipt/{id}": {"get": {"description": "Retrieve detailed information about a specific receipt.", "operationId": "OCRV1Controller_getReceiptById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Receipt ID", "schema": {"example": "507f1f77bcf86cd799439011", "type": "string"}}], "responses": {"200": {"description": "Receipt details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptResponseDto"}}}}, "401": {"description": "Unauthorized - JWT token required"}, "404": {"description": "Receipt not found"}}, "security": [{"JWT-auth": []}], "summary": "Get receipt details by ID", "tags": ["OCR v1"]}}, "/api/v1/ocr/stats": {"get": {"description": "Get comprehensive statistics about the user's receipt processing activity including monthly trends.", "operationId": "OCRV1Controller_getProcessingStats", "parameters": [], "responses": {"200": {"description": "Statistics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalReceipts": {"type": "number", "example": 25}, "totalTokensEarned": {"type": "number", "example": 5.75}, "totalAmountProcessed": {"type": "number", "example": 5750000}, "successfulProcessing": {"type": "number", "example": 23}, "failedProcessing": {"type": "number", "example": 2}, "averageTokensPerReceipt": {"type": "number", "example": 0.25}, "thisMonthReceipts": {"type": "number", "example": 8}, "thisMonthTokens": {"type": "number", "example": 2.1}, "statusBreakdown": {"type": "object", "example": {"COMPLETED": 23, "FAILED": 2}}, "monthlyStats": {"type": "array", "items": {"type": "object", "properties": {"month": {"type": "string", "example": "2025-07"}, "receipts": {"type": "number", "example": 8}, "tokens": {"type": "number", "example": 2.1}, "amount": {"type": "number", "example": 2100000}}}}}}}}}, "401": {"description": "Unauthorized - JWT token required"}}, "security": [{"JWT-auth": []}], "summary": "Get user OCR processing statistics", "tags": ["OCR v1"]}}, "/api/public/merchants/onboard": {"post": {"operationId": "MerchantsPublicController_onboardMerchant", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantOnboardingDto"}}}}, "responses": {"503": {"description": "Service temporarily unavailable for maintenance"}}, "summary": "Merchant self-onboarding - TEMPORARILY DISABLED", "tags": ["Public - Merchant Onboarding"]}}, "/api/merchant/dashboard/wallet": {"get": {"operationId": "MerchantDashboardController_getWalletInfo", "parameters": [], "responses": {"200": {"description": "Merchant token wallet information", "content": {"application/json": {"schema": {"type": "object", "properties": {"walletAddress": {"type": "string", "description": "Merchant wallet address"}, "tokenBalance": {"type": "number", "description": "Current LXT token balance"}, "totalCommissionsPaid": {"type": "number", "description": "Total commissions paid to LinkX (VND)"}, "totalTransactions": {"type": "number", "description": "Total transactions processed"}}}}}}}, "security": [{"JWT-auth": []}], "summary": "Get merchant token wallet balance", "tags": ["Merchant Dashboard"]}}, "/api/merchant/dashboard/payments": {"get": {"operationId": "MerchantDashboardController_getPaymentHistory", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by payment type", "schema": {"enum": ["COMMISSION_PAYMENT", "SETTLEMENT_PAYMENT", "WALLET_TOPUP", "WALLET_WITHDRAWAL"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of payments per page", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of payments to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "Merchant payment history", "content": {"application/json": {"schema": {"type": "object", "properties": {"payments": {"type": "array"}, "total": {"type": "number"}}}}}}}, "security": [{"JWT-auth": []}], "summary": "Get merchant payment history", "tags": ["Merchant Dashboard"]}}, "/api/merchant-api/profile": {"get": {"operationId": "MerchantApiController_getProfile", "parameters": [], "responses": {"200": {"description": "Merchant profile retrieved successfully"}, "401": {"description": "Invalid or missing API key"}}, "security": [{"api-key": []}], "summary": "Get merchant profile using API key", "tags": ["Merchant API"]}}, "/api/merchant-api/stats": {"get": {"operationId": "MerchantApiController_getStats", "parameters": [], "responses": {"200": {"description": "Merchant statistics retrieved successfully"}}, "security": [{"api-key": []}], "summary": "Get merchant statistics using API key", "tags": ["Merchant API"]}}, "/api/merchant-api/webhook-test": {"post": {"operationId": "MerchantApiController_webhookTest", "parameters": [], "requestBody": {"required": true, "description": "Test webhook payload", "content": {"application/json": {"schema": {"type": "object", "properties": {"event": {"type": "string", "example": "transaction.completed"}, "data": {"type": "object"}}}}}}, "responses": {"200": {"description": "Webhook received successfully"}}, "security": [{"api-key": []}], "summary": "Test webhook endpoint for merchant integration", "tags": ["Merchant API"]}}, "/api/settlements/summary": {"get": {"operationId": "SettlementsController_getSummary", "parameters": [], "responses": {"200": {"description": "Settlement summary", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettlementSummaryDto"}}}}, "403": {"description": "Access denied. Admin role required."}}, "security": [{"JWT-auth": []}], "summary": "Get settlement summary statistics (Admin only)", "tags": ["Settlements"]}}, "/api/settlements": {"get": {"operationId": "SettlementsController_getSettlements", "parameters": [{"name": "merchantId", "required": false, "in": "query", "description": "Filter by merchant ID", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of settlements per page", "schema": {"type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of settlements to skip", "schema": {"type": "number"}}], "responses": {"200": {"description": "List of settlements", "content": {"application/json": {"schema": {"type": "object", "properties": {"settlements": {"type": "array", "items": {"$ref": "#/components/schemas/SettlementResponseDto"}}, "total": {"type": "number"}}}}}}, "403": {"description": "Access denied. Admin role required."}}, "security": [{"JWT-auth": []}], "summary": "Get settlements with optional filters (Admin only)", "tags": ["Settlements"]}, "post": {"operationId": "SettlementsController_createSettlement", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSettlementDto"}}}}, "responses": {"201": {"description": "Settlement created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettlementResponseDto"}}}}, "400": {"description": "Settlement already exists for this period"}, "403": {"description": "Access denied. Admin role required."}, "404": {"description": "Merchant not found"}}, "security": [{"JWT-auth": []}], "summary": "Create a new settlement for a merchant (Admin only)", "tags": ["Settlements"]}}, "/api/settlements/generate-weekly": {"post": {"operationId": "SettlementsController_generateWeeklySettlements", "parameters": [], "responses": {"503": {"description": "Service temporarily unavailable for maintenance"}}, "security": [{"JWT-auth": []}], "summary": "Auto-generate weekly settlements for all active merchants - TEMPORARILY DISABLED", "tags": ["Settlements"]}}, "/api/settlements/{id}/process": {"put": {"operationId": "SettlementsController_processSettlement", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Settlement ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessSettlementDto"}}}}, "responses": {"200": {"description": "Settlement processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettlementResponseDto"}}}}, "400": {"description": "Settlement is not in pending status"}, "404": {"description": "Settlement not found"}}, "security": [{"JWT-auth": []}], "summary": "Process a pending settlement", "tags": ["Settlements"]}}, "/api/settlements/{id}/complete": {"put": {"operationId": "SettlementsController_completeSettlement", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Settlement ID", "schema": {"type": "string"}}], "responses": {"503": {"description": "Service temporarily unavailable for maintenance"}}, "security": [{"JWT-auth": []}], "summary": "Mark settlement as completed - TEMPORARILY DISABLED", "tags": ["Settlements"]}}}, "info": {"title": "LinkX Token Protocol API", "description": "Backend API for LinkX loyalty platform", "version": "1.0.0", "contact": {}}, "tags": [], "servers": [{"url": "http://localhost:3002", "description": "Development Server"}, {"url": "https://api.linkx.vn", "description": "Production Server"}], "components": {"securitySchemes": {"JWT-auth": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http", "name": "JWT", "description": "Enter JWT token", "in": "header"}, "api-key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key", "description": "Enter API key with lx_ prefix"}}, "schemas": {"RegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password (minimum 6 characters)", "example": "password123", "minLength": 6}, "phone": {"type": "string", "description": "User phone number (optional)", "example": "+***********"}, "firstName": {"type": "string", "description": "User first name (optional)", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name (optional)", "example": "<PERSON><PERSON>"}}, "required": ["email", "password"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "password123"}}, "required": ["email", "password"]}, "MerchantRegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Business email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password", "example": "SecurePassword123!", "minLength": 6}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "Manager"}, "businessName": {"type": "string", "description": "Business name", "example": "Coffee House"}, "businessId": {"type": "string", "description": "Business registration ID", "example": "0123456789"}, "category": {"type": "string", "description": "Business category", "example": "RESTAURANT"}, "businessPhone": {"type": "string", "description": "Business phone number", "example": "+***********"}, "website": {"type": "string", "description": "Business website", "example": "https://coffeehouse.vn"}, "businessDescription": {"type": "string", "description": "Business description", "example": "Premium coffee chain"}}, "required": ["email", "password", "firstName", "lastName", "businessName", "businessId", "category", "businessPhone"]}, "MerchantLoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Business email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password", "example": "SecurePassword123!", "minLength": 6}}, "required": ["email", "password"]}, "UpdateUserProfileDto": {"type": "object", "properties": {}}, "EarnTokensDto": {"type": "object", "properties": {"amountVnd": {"type": "number", "description": "Amount spent in VND at merchant", "example": 100000, "minimum": 1000, "maximum": 10000000}, "merchantId": {"type": "string", "description": "Merchant ID where spending occurred", "example": "507f1f77bcf86cd799439011"}, "description": {"type": "string", "description": "Optional description of the spending", "example": "Coffee shop purchase"}}, "required": ["amountVnd", "merchantId"]}, "TransactionResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Transaction ID", "example": "507f1f77bcf86cd799439011"}, "type": {"type": "string", "description": "Transaction type", "example": "EARN", "enum": ["EARN", "REDEEM", "TRANSFER_IN", "TRANSFER_OUT"]}, "amountVnd": {"type": "number", "description": "Amount in VND (for EARN transactions)", "example": 100000}, "amountLxt": {"type": "number", "description": "Amount in LXT tokens", "example": 0.1}, "merchantId": {"type": "string", "description": "Merchant ID", "example": "507f1f77bcf86cd799439012"}, "merchantCommission": {"type": "number", "description": "Commission paid by merchant to LinkX", "example": 2000}, "commissionRate": {"type": "number", "description": "Commission rate applied", "example": 0.02}, "platformProfit": {"type": "number", "description": "Platform profit from this transaction", "example": 1999}, "status": {"type": "string", "description": "Transaction status", "example": "COMPLETED", "enum": ["PENDING", "COMPLETED", "FAILED"]}, "blockchainTxHash": {"type": "string", "description": "Blockchain transaction hash", "example": "5J7X8K9L..."}, "description": {"type": "string", "description": "Transaction description", "example": "Coffee shop purchase"}, "createdAt": {"format": "date-time", "type": "string", "description": "Transaction creation date", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Transaction last update date", "example": "2024-01-15T10:30:15Z"}}, "required": ["id", "type", "amountLxt", "status", "createdAt", "updatedAt"]}, "TransactionHistoryDto": {"type": "object", "properties": {"transactions": {"description": "List of transactions", "type": "array", "items": {"$ref": "#/components/schemas/TransactionResponseDto"}}, "total": {"type": "number", "description": "Total number of transactions", "example": 25}, "limit": {"type": "number", "description": "Number of transactions per page", "example": 20}, "offset": {"type": "number", "description": "Number of transactions skipped", "example": 0}}, "required": ["transactions", "total", "limit", "offset"]}, "RewardResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Reward ID", "example": "507f1f77bcf86cd799439011"}, "name": {"type": "string", "description": "Reward name", "example": "Coffee Voucher"}, "description": {"type": "string", "description": "Reward description", "example": "Free coffee at partner cafes"}, "tokenCost": {"type": "number", "description": "Token cost in LXT", "example": 0.05}, "category": {"type": "string", "description": "Reward category", "example": "food"}, "imageUrl": {"type": "string", "description": "Reward image URL", "example": "https://cdn.linkx.vn/rewards/coffee.jpg"}, "isActive": {"type": "boolean", "description": "Whether reward is active", "example": true}, "partnerId": {"type": "string", "description": "Partner ID", "example": "partner_coffee_001"}, "partnerName": {"type": "string", "description": "Partner name", "example": "Coffee House"}, "terms": {"type": "string", "description": "Terms and conditions", "example": "Valid for 30 days from redemption"}, "validFrom": {"format": "date-time", "type": "string", "description": "Valid from date", "example": "2024-01-01T00:00:00Z"}, "validUntil": {"format": "date-time", "type": "string", "description": "Valid until date", "example": "2024-12-31T23:59:59Z"}, "maxRedemptions": {"type": "number", "description": "Maximum redemptions allowed", "example": 1000}, "currentRedemptions": {"type": "number", "description": "Current number of redemptions", "example": 150}, "tags": {"description": "Reward tags", "example": ["popular", "limited"], "type": "array", "items": {"type": "string"}}, "createdAt": {"format": "date-time", "type": "string", "description": "Reward creation date", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Reward last update date", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "name", "description", "tokenCost", "category", "isActive", "currentRedemptions", "createdAt", "updatedAt"]}, "RedeemRewardDto": {"type": "object", "properties": {"notes": {"type": "string", "description": "Optional notes for redemption", "example": "Redeem at store location A"}}}, "RedemptionResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Redemption ID", "example": "507f1f77bcf86cd799439011"}, "userId": {"type": "string", "description": "User ID", "example": "507f1f77bcf86cd799439012"}, "rewardId": {"type": "string", "description": "Reward ID", "example": "507f1f77bcf86cd799439013"}, "tokenCost": {"type": "number", "description": "Token cost paid", "example": 0.05}, "redemptionCode": {"type": "string", "description": "Redemption code", "example": "LXT-ABC123-DEF456"}, "status": {"type": "string", "description": "Redemption status", "example": "PENDING", "enum": ["PENDING", "COMPLETED", "EXPIRED", "CANCELLED"]}, "blockchainTxHash": {"type": "string", "description": "Blockchain transaction hash", "example": "5J7X8K9L..."}, "expiresAt": {"format": "date-time", "type": "string", "description": "Expiration date", "example": "2024-02-15T10:30:00Z"}, "redeemedAt": {"format": "date-time", "type": "string", "description": "Redemption date", "example": "2024-01-16T10:30:00Z"}, "usedAt": {"format": "date-time", "type": "string", "description": "Usage date", "example": "2024-01-17T10:30:00Z"}, "partnerConfirmation": {"type": "string", "description": "Partner confirmation code", "example": "CONF-123456"}, "notes": {"type": "string", "description": "Redemption notes", "example": "Redeemed at store location A"}, "createdAt": {"format": "date-time", "type": "string", "description": "Redemption creation date", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Redemption last update date", "example": "2024-01-15T10:30:00Z"}, "reward": {"description": "Reward details", "allOf": [{"$ref": "#/components/schemas/RewardResponseDto"}]}}, "required": ["id", "userId", "rewardId", "tokenCost", "redemptionCode", "status", "expiresAt", "createdAt", "updatedAt", "reward"]}, "AdminCreateRewardDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Reward name", "example": "Coffee Voucher"}, "description": {"type": "string", "description": "Reward description", "example": "Free coffee at partner cafes"}, "tokenCost": {"type": "number", "description": "Token cost in LXT", "example": 0.05, "minimum": 0}, "category": {"type": "string", "description": "Reward category", "example": "food"}, "imageUrl": {"type": "string", "description": "Reward image URL", "example": "https://cdn.linkx.vn/rewards/coffee.jpg"}, "partnerId": {"type": "string", "description": "Partner ID", "example": "partner_coffee_001"}, "partnerName": {"type": "string", "description": "Partner name", "example": "Coffee House"}, "terms": {"type": "string", "description": "Terms and conditions", "example": "Valid for 30 days from redemption"}, "validFrom": {"type": "string", "description": "Valid from date", "example": "2024-01-01T00:00:00Z"}, "validUntil": {"type": "string", "description": "Valid until date", "example": "2024-12-31T23:59:59Z"}, "maxRedemptions": {"type": "number", "description": "Maximum redemptions allowed", "example": 1000, "minimum": 1}, "tags": {"description": "Reward tags", "example": ["popular", "limited"], "type": "array", "items": {"type": "string"}}}, "required": ["name", "description", "tokenCost", "category"]}, "UpdateRewardDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Reward name", "example": "Coffee Voucher"}, "description": {"type": "string", "description": "Reward description", "example": "Free coffee at partner cafes"}, "tokenCost": {"type": "number", "description": "Token cost in LXT", "example": 0.05, "minimum": 0}, "category": {"type": "string", "description": "Reward category", "example": "food"}, "imageUrl": {"type": "string", "description": "Reward image URL", "example": "https://cdn.linkx.vn/rewards/coffee.jpg"}, "isActive": {"type": "boolean", "description": "Whether reward is active", "example": true}, "partnerId": {"type": "string", "description": "Partner ID", "example": "partner_coffee_001"}, "partnerName": {"type": "string", "description": "Partner name", "example": "Coffee House"}, "terms": {"type": "string", "description": "Terms and conditions", "example": "Valid for 30 days from redemption"}, "validFrom": {"type": "string", "description": "Valid from date", "example": "2024-01-01T00:00:00Z"}, "validUntil": {"type": "string", "description": "Valid until date", "example": "2024-12-31T23:59:59Z"}, "maxRedemptions": {"type": "number", "description": "Maximum redemptions allowed", "example": 1000, "minimum": 1}, "tags": {"description": "Reward tags", "example": ["popular", "limited"], "type": "array", "items": {"type": "string"}}}}, "AddressDto": {"type": "object", "properties": {"street": {"type": "string", "description": "Street address"}, "city": {"type": "string", "description": "City"}, "state": {"type": "string", "description": "State/Province"}, "zipCode": {"type": "string", "description": "ZIP/Postal code"}, "country": {"type": "string", "description": "Country", "default": "Vietnam"}}}, "ContactPersonDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Contact person name"}, "email": {"type": "string", "description": "Contact person email"}, "phone": {"type": "string", "description": "Contact person phone"}, "position": {"type": "string", "description": "Contact person position"}}}, "MerchantResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Merchant ID"}, "name": {"type": "string", "description": "Merchant business name"}, "businessId": {"type": "string", "description": "Business ID/Tax ID"}, "email": {"type": "string", "description": "Business email"}, "phone": {"type": "string", "description": "Business phone"}, "category": {"type": "string", "description": "Business category", "enum": ["RESTAURANT", "RETAIL", "ENTERTAINMENT", "SERVICES", "ECOMMERCE", "OTHER"]}, "status": {"type": "string", "description": "Merchant status", "enum": ["PENDING", "ACTIVE", "SUSPENDED", "INACTIVE"]}, "commissionRate": {"type": "number", "description": "Commission rate"}, "description": {"type": "string", "description": "Business description"}, "website": {"type": "string", "description": "Website URL"}, "logoUrl": {"type": "string", "description": "Logo URL"}, "address": {"description": "Business address", "allOf": [{"$ref": "#/components/schemas/AddressDto"}]}, "contactPerson": {"description": "Contact person information", "allOf": [{"$ref": "#/components/schemas/ContactPersonDto"}]}, "totalTransactions": {"type": "number", "description": "Total transactions processed"}, "totalCommissionPaid": {"type": "number", "description": "Total commission paid to LinkX"}, "totalSettlementReceived": {"type": "number", "description": "Total settlement received from LinkX"}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update date"}}, "required": ["id", "name", "businessId", "email", "category", "status", "commissionRate", "totalTransactions", "totalCommissionPaid", "totalSettlementReceived", "createdAt", "updatedAt"]}, "CreateMerchantDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Merchant business name", "example": "Coffee House"}, "businessId": {"type": "string", "description": "Business ID/Tax ID", "example": "0123456789"}, "email": {"type": "string", "description": "Business email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "Business phone", "example": "+***********"}, "category": {"type": "string", "description": "Business category", "enum": ["RESTAURANT", "RETAIL", "ENTERTAINMENT", "SERVICES", "ECOMMERCE", "OTHER"]}, "commissionRate": {"type": "number", "description": "Commission rate (0-1)", "example": 0.02, "minimum": 0, "maximum": 1}, "description": {"type": "string", "description": "Business description"}, "website": {"type": "string", "description": "Website URL"}, "logoUrl": {"type": "string", "description": "Logo URL"}, "address": {"description": "Business address", "allOf": [{"$ref": "#/components/schemas/AddressDto"}]}, "contactPerson": {"description": "Contact person information", "allOf": [{"$ref": "#/components/schemas/ContactPersonDto"}]}}, "required": ["name", "businessId", "email", "category"]}, "UpdateMerchantDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Merchant business name"}, "phone": {"type": "string", "description": "Business phone"}, "category": {"type": "string", "description": "Business category", "enum": ["RESTAURANT", "RETAIL", "ENTERTAINMENT", "SERVICES", "ECOMMERCE", "OTHER"]}, "status": {"type": "string", "description": "Merchant status", "enum": ["PENDING", "ACTIVE", "SUSPENDED", "INACTIVE"]}, "commissionRate": {"type": "number", "description": "Commission rate (0-1)", "minimum": 0, "maximum": 1}, "description": {"type": "string", "description": "Business description"}, "website": {"type": "string", "description": "Website URL"}, "logoUrl": {"type": "string", "description": "Logo URL"}, "address": {"description": "Business address", "allOf": [{"$ref": "#/components/schemas/AddressDto"}]}, "contactPerson": {"description": "Contact person information", "allOf": [{"$ref": "#/components/schemas/ContactPersonDto"}]}}}, "OCRResultItemDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Item name", "example": "Cappuccino"}, "quantity": {"type": "number", "description": "Quantity", "example": 2}, "unitPrice": {"type": "number", "description": "Unit price", "example": 45000}, "totalPrice": {"type": "number", "description": "Total price for this item", "example": 90000}}, "required": ["name", "quantity", "unitPrice", "totalPrice"]}, "OCRResultDto": {"type": "object", "properties": {"storeName": {"type": "string", "description": "Store name", "example": "Coffee House"}, "storeAddress": {"type": "string", "description": "Store address", "example": "123 <PERSON><PERSON><PERSON>, District 1, HCMC"}, "totalAmount": {"type": "number", "description": "Total amount", "example": 150000}, "subtotal": {"type": "number", "description": "Subtotal amount", "example": 136364}, "tax": {"type": "number", "description": "Tax amount", "example": 13636}, "date": {"type": "string", "description": "Receipt date", "example": "2025-07-22"}, "time": {"type": "string", "description": "Receipt time", "example": "14:30:00"}, "items": {"description": "Receipt items", "type": "array", "items": {"$ref": "#/components/schemas/OCRResultItemDto"}}, "confidence": {"type": "number", "description": "OCR confidence score", "example": 0.95}, "provider": {"type": "string", "description": "OCR provider used", "enum": ["FPT_AI", "TABSCANNER", "GEMINI"]}, "rawData": {"type": "object", "description": "Raw OCR response data"}}, "required": ["storeName", "totalAmount", "date", "confidence", "provider", "rawData"]}, "ValidationResultDto": {"type": "object", "properties": {"isValid": {"type": "boolean", "description": "Whether the receipt is valid", "example": true}, "errors": {"description": "Validation errors", "example": [], "type": "array", "items": {"type": "string"}}, "warnings": {"description": "Validation warnings", "example": ["Low OCR confidence"], "type": "array", "items": {"type": "string"}}, "duplicateCheck": {"type": "object", "description": "Duplicate check result"}, "businessRules": {"type": "object", "description": "Business rules validation"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings"]}, "TokenCalculationDto": {"type": "object", "properties": {"baseAmount": {"type": "number", "description": "Base token amount", "example": 0.15}, "bonusAmount": {"type": "number", "description": "Bonus token amount", "example": 0.05}, "totalTokens": {"type": "number", "description": "Total tokens earned", "example": 0.2}, "exchangeRate": {"type": "number", "description": "VND to LXT exchange rate", "example": 1000000}, "tierMultiplier": {"type": "number", "description": "User tier multiplier", "example": 1.2}, "merchantBonus": {"type": "number", "description": "Merchant bonus", "example": 0.02}}, "required": ["baseAmount", "bonusAmount", "totalTokens", "exchangeRate", "tierMultiplier"]}, "ReceiptProcessingResultDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Processing success status", "example": true}, "receiptId": {"type": "string", "description": "Receipt ID", "example": "507f1f77bcf86cd799439011"}, "transactionId": {"type": "string", "description": "Transaction ID", "example": "507f1f77bcf86cd799439012"}, "tokensEarned": {"type": "number", "description": "Tokens earned", "example": 0.2}, "blockchainTxHash": {"type": "string", "description": "Blockchain transaction hash"}, "ocrResult": {"description": "OCR result data", "allOf": [{"$ref": "#/components/schemas/OCRResultDto"}]}, "validationResult": {"description": "Validation result", "allOf": [{"$ref": "#/components/schemas/ValidationResultDto"}]}, "tokenCalculation": {"description": "Token calculation", "allOf": [{"$ref": "#/components/schemas/TokenCalculationDto"}]}, "errors": {"description": "Processing errors", "example": [], "type": "array", "items": {"type": "string"}}, "warnings": {"description": "Processing warnings", "example": [], "type": "array", "items": {"type": "string"}}, "status": {"type": "string", "description": "Processing status", "enum": ["PROCESSING", "COMPLETED", "FAILED", "VALIDATION_FAILED"]}}, "required": ["success"]}, "ProcessReceiptUrlDto": {"type": "object", "properties": {"imageUrl": {"type": "string", "description": "URL of the receipt image to process", "example": "https://example.com/receipt.jpg"}, "metadata": {"type": "object", "description": "Additional metadata for processing", "example": {"source": "mobile_app", "location": "Ho Chi Minh City"}}, "notes": {"type": "string", "description": "Notes about the receipt", "example": "Lunch with client"}}, "required": ["imageUrl"]}, "ReceiptResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Receipt ID", "example": "507f1f77bcf86cd799439011"}, "userId": {"type": "string", "description": "User ID", "example": "507f1f77bcf86cd799439010"}, "originalImageUrl": {"type": "string", "description": "Original image URL"}, "processedImageUrl": {"type": "string", "description": "Processed image URL"}, "thumbnailUrl": {"type": "string", "description": "Thumbnail URL"}, "ocrResult": {"description": "OCR result", "allOf": [{"$ref": "#/components/schemas/OCRResultDto"}]}, "tokensEarned": {"type": "number", "description": "Tokens earned", "example": 0.2}, "status": {"type": "string", "description": "Processing status", "enum": ["PROCESSING", "COMPLETED", "FAILED", "VALIDATION_FAILED"]}, "createdAt": {"type": "string", "description": "Created at", "example": "2025-07-22T12:00:00.000Z"}, "updatedAt": {"type": "string", "description": "Updated at", "example": "2025-07-22T12:05:00.000Z"}}, "required": ["id", "userId", "originalImageUrl", "status", "createdAt", "updatedAt"]}, "ReceiptHistoryDto": {"type": "object", "properties": {"receipts": {"description": "List of receipts", "type": "array", "items": {"$ref": "#/components/schemas/ReceiptResponseDto"}}, "total": {"type": "number", "description": "Total count", "example": 25}, "limit": {"type": "number", "description": "Limit", "example": 20}, "offset": {"type": "number", "description": "Offset", "example": 0}}, "required": ["receipts", "total", "limit", "offset"]}, "MerchantOnboardingDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Merchant business name", "example": "Coffee House"}, "businessId": {"type": "string", "description": "Business ID/Tax ID", "example": "0123456789"}, "email": {"type": "string", "description": "Business email", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "Business phone", "example": "+***********"}, "category": {"type": "string", "description": "Business category", "enum": ["RESTAURANT", "RETAIL", "ENTERTAINMENT", "SERVICES", "ECOMMERCE", "OTHER"]}, "description": {"type": "string", "description": "Business description"}, "website": {"type": "string", "description": "Website URL"}, "address": {"description": "Business address", "allOf": [{"$ref": "#/components/schemas/AddressDto"}]}, "contactPerson": {"description": "Contact person information", "allOf": [{"$ref": "#/components/schemas/ContactPersonDto"}]}, "agreeToTerms": {"type": "boolean", "description": "Agree to terms and conditions", "example": true}}, "required": ["name", "businessId", "email", "category", "agreeToTerms"]}, "SettlementSummaryDto": {"type": "object", "properties": {"totalPending": {"type": "number", "description": "Total settlements pending"}, "totalPendingAmount": {"type": "number", "description": "Total amount pending"}, "totalCompletedThisMonth": {"type": "number", "description": "Total settlements completed this month"}, "totalSettledThisMonth": {"type": "number", "description": "Total amount settled this month"}, "averageSettlementAmount": {"type": "number", "description": "Average settlement amount"}, "merchantsWithPendingSettlements": {"type": "number", "description": "Number of merchants with pending settlements"}}, "required": ["totalPending", "totalPendingAmount", "totalCompletedThisMonth", "totalSettledThisMonth", "averageSettlementAmount", "merchantsWithPendingSettlements"]}, "CreateSettlementDto": {"type": "object", "properties": {"merchantId": {"type": "string", "description": "Merchant ID"}, "type": {"type": "string", "description": "Settlement type", "enum": ["WEEKLY", "MONTHLY", "MANUAL"]}, "periodStart": {"type": "string", "description": "Period start date", "example": "2025-01-01"}, "periodEnd": {"type": "string", "description": "Period end date", "example": "2025-01-31"}, "notes": {"type": "string", "description": "Additional notes"}}, "required": ["merchantId", "type", "periodStart", "periodEnd"]}, "SettlementResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Settlement ID"}, "merchantId": {"type": "string", "description": "Merchant ID"}, "merchantName": {"type": "string", "description": "Merchant name"}, "type": {"type": "string", "description": "Settlement type", "enum": ["WEEKLY", "MONTHLY", "MANUAL"]}, "status": {"type": "string", "description": "Settlement status", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date"}, "totalCommissionPaid": {"type": "number", "description": "Total commission paid by merchant"}, "totalSettlementOwed": {"type": "number", "description": "Total settlement owed to merchant"}, "netAmount": {"type": "number", "description": "Net amount (positive = owe merchant, negative = merchant owes)"}, "transactionCount": {"type": "number", "description": "Number of transactions in period"}, "tokensIssued": {"type": "number", "description": "Total LXT tokens issued"}, "tokensRedeemed": {"type": "number", "description": "Total LXT tokens redeemed"}, "paymentInfo": {"type": "object", "description": "Payment information"}, "notes": {"type": "string", "description": "Processing notes"}, "processedBy": {"type": "string", "description": "Processed by admin"}, "processedAt": {"format": "date-time", "type": "string", "description": "Processing date"}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update date"}}, "required": ["id", "merchantId", "merchantName", "type", "status", "periodStart", "periodEnd", "totalCommissionPaid", "totalSettlementOwed", "netAmount", "transactionCount", "tokensIssued", "tokensRedeemed", "createdAt", "updatedAt"]}, "ProcessSettlementDto": {"type": "object", "properties": {"paymentMethod": {"type": "string", "description": "Payment method", "enum": ["BANK_TRANSFER", "DIGITAL_WALLET", "CHECK"]}, "paymentReference": {"type": "string", "description": "Payment reference number"}, "notes": {"type": "string", "description": "Processing notes"}}, "required": ["paymentMethod"]}}}}
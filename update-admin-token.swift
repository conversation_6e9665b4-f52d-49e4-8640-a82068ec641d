import Foundation

// This script will be used to update the admin token in iOS app
// Token from API: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.sx5WT66cnzLozNYg2KKXXmHRlKm-HzcDYXMTSg_L4ew

// User data from API:
/*
{
  "id": "687b7352e609972757847287",
  "email": "<EMAIL>",
  "walletAddress": "FHsfVTkksQB9FTjsfVa4ur6cfVvssbGkoukMqrV1xwvR",
  "firstName": "LinkX",
  "lastName": "Admin",
  "role": "ADMIN"
}
*/

// Instructions:
// 1. Build and run the iOS app
// 2. In the iOS simulator, go to Device > Erase All Content and Settings
// 3. Or manually clear UserDefaults by adding this code to AppDelegate:
//    UserDefaults.standard.removeObject(forKey: "linkx_access_token")
//    UserDefaults.standard.removeObject(forKey: "linkx_current_user")
// 4. Login <NAME_EMAIL> / AdminLinkX2024!
// 5. The app should now get the correct ADMIN role from the API

print("Use this token to test: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.sx5WT66cnzLozNYg2KKXXmHRlKm-HzcDYXMTSg_L4ew")
